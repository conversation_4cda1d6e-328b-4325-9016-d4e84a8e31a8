{"name": "Postcode Flow_AU_ANDROID", "created": "2025-07-12 19:59:18", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "1690ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "Y8vz7AJD1i", "executionTime": "1381ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "J9loj6Zl5K", "executionTime": "844ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "xz8njynjpZ", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250702071621.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1749384094911, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@5", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1751405478055, "type": "multiStep"}, {"action_id": "RLz6vQo3ag", "executionTime": "3447ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "method": "locator", "timeout": 10, "timestamp": 1751404760227, "type": "waitTill"}, {"action_id": "QMXBlswP6H", "double_tap": false, "executionTime": "2833ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746143899898, "type": "tapOnText"}, {"action_id": "pldheRUBVi", "executionTime": "1192ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751442053790, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 1000, "executionTime": "1505ms", "text": "5000", "timestamp": 1746144035427, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "1124ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751451132525, "type": "tap"}, {"action_id": "mw9GQ4mzRE", "double_tap": false, "executionTime": "1859ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "BC", "threshold": 0.7, "timeout": 30, "timestamp": 1746144235322, "type": "tapOnText"}, {"action_id": "kDnmoQJG4o", "executionTime": "215ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 10, "timestamp": 1751443203144, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "855ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1746144262142, "type": "tap"}, {"action_id": "70iOOakiG7", "double_tap": false, "executionTime": "2592ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "IupxLP2Jsr", "enter": true, "executionTime": "285ms", "function_name": "text", "text": "P_6225544", "timestamp": 1745484826180, "type": "text"}, {"action_id": "MpdUKUazHa", "executionTime": "8696ms", "image_filename": "sort-by-relevance-android.png", "interval": 0.5, "locator_type": "image", "locator_value": "sort-by-relevance-android.png", "threshold": 0.7, "timeout": 30, "timestamp": 1751507244321, "type": "waitTill"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "1881ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746144823467, "type": "tapOnText"}, {"action_id": "YhLhTn3Wtm", "duration": 5, "executionTime": "5054ms", "time": 5, "timestamp": 1751457606479, "type": "wait"}, {"action_id": "pldheRUBVi", "executionTime": "907ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751442898549, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 1000, "executionTime": "1507ms", "text": "4000", "timestamp": 1751442824674, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "1309ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751451263586, "type": "tap"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "2306ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "CITY", "threshold": 0.7, "timeout": 30, "timestamp": 1751443446880, "type": "tapOnText"}, {"action_id": "kDnmoQJG4o", "executionTime": "188ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 10, "timestamp": 1751499370567, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "864ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1751499419919, "type": "tap"}, {"action_id": "73NABkfWyY", "executionTime": "1609ms", "locator_type": "text", "locator_value": "<PERSON><PERSON><PERSON>", "timeout": 10, "timestamp": 1746145022497, "type": "exists"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "1946ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "UNO", "threshold": 0.7, "timeout": 30, "timestamp": 1751444026038, "type": "tapOnText"}, {"action_id": "lnjoz8hHUU", "double_tap": false, "executionTime": "4513ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746145090019, "type": "waitTill"}, {"action_id": "WmNWcsWVHv", "double_tap": false, "executionTime": "1872ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "text_to_find": "4000", "timeout": 30, "timestamp": 1746145246389, "type": "tapOnText"}, {"action_id": "pldheRUBVi", "executionTime": "178ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751444225167, "type": "waitTill"}, {"action_id": "pldheRUBVi", "executionTime": "440ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751510645575, "type": "tap"}, {"action_id": "uZHvvAzVfx", "delay": 500, "executionTime": "877ms", "text": "HAYMARKET", "timestamp": 1746145223768, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "415ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751522321750, "type": "tap"}, {"action_id": "H0ODFz7sWJ", "double_tap": false, "executionTime": "1884ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746145274227, "type": "tapOnText"}, {"action_id": "kDnmoQJG4o", "executionTime": "221ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1751499445630, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "858ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1751499461981, "type": "tap"}, {"action_id": "eRCmRhc3re", "executionTime": "960ms", "locator_type": "text", "locator_value": "Broadway", "timeout": 20, "timestamp": 1746145322752, "type": "exists"}, {"action_id": "DfwaiVZ8Z9", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "1384ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751444331993, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "94ikwhIEE2", "double_tap": false, "executionTime": "2046ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "text_to_find": "bag", "threshold": 0.7, "timeout": 30, "timestamp": 1746145693144, "type": "tapOnText"}, {"action_id": "rkwVoJGZG4", "executionTime": "509ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751444508689, "type": "tap"}, {"action_id": "0QetCcQWOH", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "image", "locator_value": "delivery-tab-android.png", "threshold": 0.7, "timeout": 20, "timestamp": 1752295136759, "type": "waitTill", "executionTime": "30893ms"}, {"action_id": "BkQtEmZCrY", "image_filename": "cnc-tab-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752295174524, "type": "tap", "executionTime": "14441ms"}, {"action_id": "G4A3KBlXHq", "executionTime": "1563ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746145831229, "type": "tapOnText"}, {"action_id": "QpBLC6BStn", "delay": 500, "executionTime": "1953ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "delete", "method": "locator", "text": "3000", "timeout": 10, "timestamp": 1746145953013, "type": "textClear"}, {"action_id": "ZWpYNcpbFA", "double_tap": false, "executionTime": "13017ms", "image_filename": "MELBOURNE_SE.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746146079046, "type": "tapOnText"}, {"action_id": "GYK47u1y3A", "executionTime": "616ms", "function_name": "send_key_event", "key_event": "TAB", "timestamp": 1751455570517, "type": "androidFunctions"}, {"action_id": "s8h8VDUIOC", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "5021ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146260557, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "mHyK7BTEWp", "duration": 3, "executionTime": "3061ms", "time": 3, "timestamp": 1752091990565, "type": "wait"}, {"action_id": "JrPVGdts3J", "executionTime": "23336ms", "image_filename": "bag-remove-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1751499791382, "type": "tap"}, {"action_id": "Tebej51pT2", "executionTime": "624ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746146348304, "type": "tap"}, {"action_id": "0f2FSZYjWq", "executionTime": "1791ms", "locator_type": "text", "locator_value": "3000", "timeout": 10, "timestamp": 1746146563188, "type": "exists"}, {"action_id": "rkwVoJGZG4", "executionTime": "820ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751445434641, "type": "tap"}, {"action_id": "mWeLQtXiL6", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3219ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146644650, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xyHVihJMBi", "executionTime": "2048ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746146665049, "type": "tap"}, {"type": "tapIfLocatorExists", "timestamp": 1752314356418, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 20, "action_id": "ykv0Gi5VeL"}], "labels": [], "updated": "2025-07-12 19:59:18"}