{"name": "Delivery & CNC- NZ", "created": "2025-07-10 16:56:17", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "2446ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "7535ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1233ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "2919ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "SVt620PG1t", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "test_case_steps_count": 6, "timestamp": 1750039068690, "type": "multiStep"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "1785ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"type": "iosFunctions", "timestamp": 1752130528203, "function_name": "text", "text": "notebook", "enter": true, "action_id": "txlTDdpSV8"}, {"action_id": "nAB6Q8LAdv", "executionTime": "2415ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "3092ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "1984ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746102129685, "type": "exists"}, {"action_id": "F1olhgKhUt", "executionTime": "3124ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "G4fIUWmvcz", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781537682, "type": "tapIfLocatorExists"}, {"action_id": "uM5FOSrU5U", "executionTime": "2546ms", "locator_type": "image", "locator_value": "cnc-tab-se.png", "timeout": 10, "timestamp": 1746102183980, "type": "exists"}, {"action_id": "qjj0i3rcUh", "double_tap": false, "executionTime": "1833ms", "image_filename": "cnc-tab-se.png", "method": "image", "text_to_find": "Collect", "threshold": 0.7, "timeout": 30, "timestamp": 1746100742115, "type": "tapOnText"}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3919ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746102698568, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "15378ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "1733ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3139ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6814ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "3003ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "cKNu2QoRC1", "executionTime": "3171ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1746426742221, "type": "tap"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "2130ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1747132282219, "type": "tapOnText"}, {"action_id": "FhAExGzO0Z", "enter": true, "function_name": "text", "text": "P_43399611", "timestamp": 1751755759497, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "2496ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1747132311829, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "3113ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1747132328659, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "3020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1747132339669, "type": "tap"}, {"action_id": "DdLWhRiGuf", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781528143, "type": "tapIfLocatorExists"}, {"type": "multiStep", "timestamp": 1752130556941, "test_case_id": "Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json", "test_case_name": "Delivery Buy Step NZ", "test_case_steps_count": 34, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 15, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Q5A0cNaJ24", "executionTime": "3031ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "method": "locator", "timeout": 10, "timestamp": 1745486308596, "type": "tap"}, {"action_id": "h9trcMrvxt", "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486361281, "type": "tap"}, {"action_id": "CLMmkV1OIM", "delay": 500, "executionTime": "3392ms", "function_name": "text", "text": "First Name", "timestamp": 1745486374043, "type": "textClear"}, {"action_id": "p8rfQL9ara", "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Last Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "QvuueoTR8W", "delay": 500, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "timestamp": 1745486416273, "type": "textClear"}, {"action_id": "9B5MQGTmpP", "executionTime": "3080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745486441044, "type": "tap"}, {"action_id": "lWJtKSqlPS", "delay": 500, "executionTime": "3483ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745486452706, "type": "textClear"}, {"action_id": "yi5EsHEFvc", "executionTime": "3080ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Mobile number\"]", "method": "locator", "timeout": 10, "timestamp": 1745486486775, "type": "tap"}, {"action_id": "SFj4Aa7RHQ", "delay": 500, "executionTime": "3252ms", "function_name": "text", "text": "0400000000", "timestamp": 1745486504243, "type": "textClear"}, {"action_id": "kDpsm2D3xt", "enter": true, "executionTime": "2985ms", "function_name": "text", "text": " ", "timestamp": 1745570305956, "type": "iosFunctions"}, {"action_id": "XRiJwoJD9w", "duration": 10, "time": 10, "timestamp": 1748324784981, "type": "wait"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "tapOnText"}, {"action_id": "vbKluTj3XJ", "method": "coordinates", "text": "Quay Street", "timeout": 60, "timestamp": 1752129875793, "type": "tapAndType", "x": 54, "y": 314}, {"action_id": "NcU6aex76k", "executionTime": "1807ms", "image_filename": "keyboard_done_iphoneSE.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746616991897, "type": "tap"}, {"action_id": "PLrRarI0Y9", "double_tap": false, "executionTime": "2120ms", "image_filename": "nz-delivery-address.png", "method": "image", "text_to_find": "Quay", "threshold": 0.7, "timeout": 30, "timestamp": 1745538210655, "type": "tapOnText", "x": 185, "y": 414}, {"action_id": "TTpwkHEyuE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044105004, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "1Lirmyxkft", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "method": "locator", "timeout": 10, "timestamp": 1747044123748, "type": "tap"}, {"action_id": "6LQ5cq0f6N", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044256988, "type": "tap"}, {"action_id": "CBBib3pFkq", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044349502, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "ftA0OJvd0W", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "method": "locator", "timeout": 10, "timestamp": 1747044370730, "type": "tap"}, {"action_id": "mfOWujfRpL", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]", "timeout": 10, "timestamp": 1747044439860, "type": "exists"}, {"action_id": "XLpUP3Wr93", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"close\"]", "method": "locator", "timeout": 10, "timestamp": 1747044533430, "type": "tap"}, {"action_id": "dkSs61jGvX", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044584329, "type": "tap"}, {"action_id": "9Pwdq32eUk", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "method": "locator", "timeout": 10, "timestamp": 1747044771806, "type": "tap"}, {"action_id": "lSG7un0qKK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeImage[@name=\"Afterpay\"]", "timeout": 10, "timestamp": 1747044824024, "type": "exists"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1747045008851, "type": "tap"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1747132959393, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745490084698, "type": "tap"}, {"action_id": "TtZ9hTYrms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780543007, "type": "tapIfLocatorExists"}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "steps_loaded": true, "display_depth": 0, "action_id": "SSWn54QNG9"}, {"action_id": "ynNeRrFTrg", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_NZ_Cleanup_20250628211956.json", "test_case_name": "Kmart_NZ_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "nz.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751109708838, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-10 16:56:17"}