{"name": "Browse & PDP", "created": "2025-07-11 08:07:23", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3535ms", "package_id": "env[appid]", "timestamp": 1746597492636, "type": "restartApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "1433ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "tap"}, {"action_id": "Xr6F8gdd8q", "executionTime": "5236ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 30, "timestamp": 1746834621258, "type": "waitTill"}, {"action_id": "Xr6F8gdd8q", "executionTime": "5846ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 15, "timestamp": 1748003205132, "type": "tap"}, {"action_id": "vfwUVEyq6X", "executionTime": "4071ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeImage[@name=\"More\"]", "timeout": 20, "timestamp": 1746834702853, "type": "exists"}, {"action_id": "QPKR6jUF9O", "executionTime": "3976ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Scan barcode\"]", "timeout": 20, "timestamp": 1746834725468, "type": "exists"}, {"action_id": "ltDXyWvtEz", "executionTime": "2528ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298419980, "type": "tap"}, {"action_id": "RbNtEW6N9T", "double_tap": false, "executionTime": "4504ms", "text_to_find": "Toys", "timeout": 30, "timestamp": 1746830828429, "type": "tapOnText"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "4483ms", "text_to_find": "Latest", "timeout": 30, "timestamp": 1746830873534, "type": "tapOnText"}, {"action_id": "lYPskZt0Ya", "executionTime": "5572ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1748003003230, "type": "waitTill"}, {"action_id": "Y1O1clhMSJ", "executionTime": "6289ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "method": "locator", "timeout": 15, "timestamp": 1746834148265, "type": "tap"}, {"action_id": "a50JhCx0ir", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "method": "locator", "timeout": 10, "timestamp": 1749298898179, "type": "tap"}, {"action_id": "dMl1PH9Dlc", "duration": 5, "executionTime": "10013ms", "time": 5, "timestamp": 1747985121748, "type": "wait"}, {"action_id": "ktAufkDJnF", "executionTime": "6203ms", "image_filename": "Filter-checkbox-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Show (\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748003426477, "type": "tap"}, {"action_id": "XmAxcBtFI0", "executionTime": "4283ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "timeout": 20, "timestamp": 1746834533008, "type": "exists"}, {"action_id": "huUnpMMjVR", "executionTime": "6380ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "method": "locator", "timeout": 30, "timestamp": 1746834553339, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6942ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746835476969, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "dMl1PH9Dlc", "duration": 10, "executionTime": "10013ms", "time": 10, "timestamp": 1752183663859, "type": "wait"}, {"action_id": "OmKfD9iBjD", "executionTime": "5947ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "timeout": 20, "timestamp": 1746835134218, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "37153ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "dCqKBG3e7u", "image_filename": "env[product-share-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298579395, "type": "tap"}, {"action_id": "83tV9A4NOn", "executionTime": "6938ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Check out \")]", "timeout": 10, "timestamp": 1746832013286, "type": "exists"}, {"action_id": "sHQtYzpI4s", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298650911, "type": "tap"}, {"action_id": "ShJSdXvmVL", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "50390ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Learn more about AfterPay", "start_x": 50, "start_y": 70, "timestamp": 1746832248527, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "18149ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about AfterPay", "method": "locator", "timeout": 10, "timestamp": 1746832294456, "type": "tap"}, {"action_id": "DhWa2PCBXE", "enabled": false, "executionTime": "13773ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]", "method": "locator", "timeout": 10, "timestamp": 1746832373193, "type": "exists"}, {"action_id": "Et3kvnFdxh", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298408507, "type": "tap"}, {"action_id": "inrxgdWzXr", "executionTime": "18216ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "timeout": 10, "timestamp": 1746832507125, "type": "tap"}, {"action_id": "P4b2BITpCf", "executionTime": "7167ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"What is Z<PERSON>?\"]", "timeout": 20, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "KRQDBv2D3A", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298457107, "type": "tap"}, {"action_id": "q6cKxgMAIn", "executionTime": "18075ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about PayPal Pay in 4", "method": "locator", "timeout": 10, "timestamp": 1746832657816, "type": "tap"}, {"action_id": "mtYqeDttRc", "image_filename": "env[paypal-close-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749431777668, "type": "tap"}, {"action_id": "B6GDXWAmWp", "executionTime": "8901ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "method": "locator", "timeout": 15, "timestamp": 1746832967047, "type": "tap"}, {"action_id": "GWoppouz1l", "executionTime": "4900ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]", "timeout": 20, "timestamp": 1746833124593, "type": "exists"}, {"action_id": "PiQRBWBe3E", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298391226, "type": "tap"}, {"action_id": "XPEr3w6Zof", "package_id": "env[appid]", "timestamp": 1748255164286, "type": "restartApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "4261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1748255205822, "type": "tap"}, {"action_id": "yEga5MkcRe", "executionTime": "3668ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 15, "timestamp": 1748255228954, "type": "tap"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "3736ms", "function_name": "text", "text": "Kid toy", "timestamp": 1748255239813, "type": "iosFunctions"}, {"action_id": "kz9lnCdwoH", "executionTime": "5676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "method": "locator", "timeout": 30, "timestamp": 1748255374981, "type": "waitTill"}, {"action_id": "kz9lnCdwoH", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "12436ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 15, "timestamp": 1748255392139, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "F4NGh9HrLw", "executionTime": "4261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746833833911, "type": "tap"}, {"action_id": "yEga5MkcRe", "executionTime": "3668ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 15, "timestamp": 1746833889101, "type": "tap"}, {"action_id": "qIF9CVPc56", "enter": true, "function_name": "text", "text": "mat", "timestamp": 1748255682142, "type": "iosFunctions"}, {"action_id": "kz9lnCdwoH", "executionTime": "5676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "method": "locator", "timeout": 30, "timestamp": 1747563355236, "type": "waitTill"}, {"action_id": "kz9lnCdwoH", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "12436ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 15, "timestamp": 1748006804978, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "NOnuFzXy63", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1749469562712, "type": "tap"}, {"action_id": "VtMfqK1V9t", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749469643512, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "4904ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746834764179, "type": "tap"}, {"action_id": "w7I4F66YKQ", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780859504, "type": "tapIfLocatorExists"}, {"action_id": "nyBidG0kHp", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "13109ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "start_x": 50, "start_y": 70, "timestamp": 1748008495255, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "6101ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "2p13JoJbbA", "executionTime": "6323ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1747563410286, "type": "tap"}, {"action_id": "x4yLCZHaCR", "executionTime": "1076ms", "package_id": "env[appid]", "timestamp": 1746834909467, "type": "terminateApp"}, {"action_id": "vKo6Ox3YrP", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751004125244, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-11 08:07:23"}