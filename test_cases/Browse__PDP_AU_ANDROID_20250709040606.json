{"name": "Browse & PDP_AU_ANDROID", "created": "2025-07-12 20:07:18", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "307ms", "package_id": "au.com.kmart", "timestamp": 1746597492636, "type": "terminateApp"}, {"action_id": "H9fy9qcFbZ", "executionTime": "1425ms", "package_id": "au.com.kmart", "timestamp": 1752002461224, "type": "launchApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "3802ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "15085ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1751998546829, "type": "tap"}, {"action_id": "vfwUVEyq6X", "executionTime": "257ms", "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"More\"]", "timeout": 10, "timestamp": 1746834702853, "type": "exists"}, {"action_id": "QPKR6jUF9O", "executionTime": "231ms", "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Scan barcode\"]", "timeout": 10, "timestamp": 1746834725468, "type": "exists"}, {"action_id": "ltDXyWvtEz", "executionTime": "181ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "interval": 0.5, "key_event": "BACK", "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(0)", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749298419980, "type": "tap"}, {"action_id": "RbNtEW6N9T", "double_tap": false, "executionTime": "9048ms", "text_to_find": "Toys", "timeout": 30, "timestamp": 1746830828429, "type": "tapOnText"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "4052ms", "text_to_find": "Latest", "timeout": 30, "timestamp": 1746830873534, "type": "tapOnText"}, {"action_id": "lYPskZt0Ya", "executionTime": "24291ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1748003003230, "type": "waitTill"}, {"action_id": "Y1O1clhMSJ", "executionTime": "1441ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 15, "timestamp": 1746834148265, "type": "tap"}, {"action_id": "a50JhCx0ir", "double_tap": false, "executionTime": "19625ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "method": "locator", "text_to_find": "only", "timeout": 30, "timestamp": 1749298898179, "type": "tapOnText"}, {"action_id": "a50JhCx0ir", "double_tap": false, "executionTime": "29037ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "method": "locator", "text_to_find": "Show", "timeout": 30, "timestamp": 1751999330542, "type": "tapOnText"}, {"action_id": "XmAxcBtFI0", "executionTime": "1453ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"In stock only i... ChipsClose\"]", "timeout": 10, "timestamp": 1746834533008, "type": "exists"}, {"action_id": "huUnpMMjVR", "executionTime": "1577ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"In stock only i... ChipsClose\"]", "method": "locator", "timeout": 30, "timestamp": 1746834553339, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "18745ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746835476969, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "dMl1PH9Dlc", "duration": 10, "executionTime": "10125ms", "time": 10, "timestamp": 1747985121748, "type": "wait"}, {"action_id": "OmKfD9iBjD", "executionTime": "17091ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "timeout": 30, "timestamp": 1746835134218, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "1460ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "dCqKBG3e7u", "executionTime": "639ms", "image_filename": "env[product-share-img]", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1749298579395, "type": "tap"}, {"action_id": "EEx673tuI0", "executionTime": "1357ms", "locator_type": "text", "locator_value": "Share", "timeout": 10, "timestamp": 1752000197122, "type": "exists"}, {"action_id": "y5FboDiRLS", "image_filename": "share-close.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752004354431, "type": "tap"}, {"action_id": "ShJSdXvmVL", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "63073ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout AfterPay\"]", "start_x": 50, "start_y": 70, "timestamp": 1746832248527, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "873ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout AfterPay\"]", "method": "locator", "timeout": 10, "timestamp": 1746832294456, "type": "tap"}, {"action_id": "DhWa2PCBXE", "enabled": true, "executionTime": "1560ms", "interval": 0.5, "locator_type": "text", "locator_value": "Apply", "method": "locator", "timeout": 10, "timestamp": 1746832373193, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "226ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "key_event": "BACK", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298408507, "type": "androidFunctions"}, {"action_id": "inrxgdWzXr", "executionTime": "18552ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout Zip\"]", "method": "locator", "timeout": 10, "timestamp": 1746832507125, "type": "tap"}, {"action_id": "P4b2BITpCf", "executionTime": "1388ms", "locator_type": "text", "locator_value": "What", "timeout": 20, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "204ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "key_event": "BACK", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1752000539283, "type": "androidFunctions"}, {"action_id": "q6cKxgMAIn", "executionTime": "23619ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Learn more about PayPal Pay in 4\"]", "method": "locator", "timeout": 10, "timestamp": 1746832657816, "type": "tap"}, {"action_id": "P4b2BITpCf", "executionTime": "1527ms", "locator_type": "text", "locator_value": "interest-", "timeout": 20, "timestamp": 1752000628249, "type": "exists"}, {"action_id": "mtYqeDttRc", "executionTime": "15989ms", "image_filename": "paypal-close-android.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749431777668, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 40, "executionTime": "18745ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752004208629, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.7]}, {"action_id": "B6GDXWAmWp", "executionTime": "47733ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Shop at\"]/following-sibling::android.widget.Button", "method": "locator", "timeout": 15, "timestamp": 1746832967047, "type": "tap"}, {"action_id": "GWoppouz1l", "executionTime": "1560ms", "locator_type": "text", "locator_value": "postcode", "timeout": 20, "timestamp": 1746833124593, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "1624ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "interval": 0.5, "key_event": "BACK", "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(0)", "method": "locator", "threshold": 0.7, "timeout": 15, "timestamp": 1752001063293, "type": "tap"}, {"action_id": "XPEr3w6Zof", "executionTime": "267ms", "package_id": "au.com.kmart", "timestamp": 1748255164286, "type": "terminateApp"}, {"action_id": "XPEr3w6Zof", "executionTime": "1395ms", "package_id": "au.com.kmart", "timestamp": 1752001232922, "type": "launchApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "3819ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752001264866, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "4201ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752001315505, "type": "tap"}, {"action_id": "o1gHFWHXTL", "double_tap": false, "executionTime": "18373ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752001523774, "type": "tapOnText"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "1335ms", "function_name": "text", "text": "Kids Toys", "timestamp": 1748255239813, "type": "text"}, {"action_id": "lYPskZt0Ya", "executionTime": "23250ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1752001701222, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "1230ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1752001894693, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3932ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752002048010, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "JRheDTvpJf", "double_tap": false, "enter": true, "executionTime": "4387ms", "function_name": "text", "text": "Kids Toys", "text_to_find": "Add", "timeout": 30, "timestamp": 1752002165808, "type": "tapOnText"}, {"action_id": "F4NGh9HrLw", "executionTime": "3083ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752001949098, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "5056ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752001960619, "type": "tap"}, {"action_id": "o1gHFWHXTL", "double_tap": false, "executionTime": "27365ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752001972260, "type": "tapOnText"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "950ms", "function_name": "text", "text": "mat", "timestamp": 1752001982805, "type": "text"}, {"action_id": "lYPskZt0Ya", "executionTime": "5129ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1752002005454, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "13693ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1752002236533, "type": "tap"}, {"action_id": "kwF3J9NbRc", "executionTime": "2608ms", "interval": 0.5, "locator_type": "text", "locator_value": "SKU", "timeout": 30, "timestamp": 1752003281037, "type": "waitTill"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4002ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752002249659, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "JRheDTvpJf", "double_tap": false, "enter": true, "executionTime": "2002ms", "function_name": "text", "text": "Kids Toys", "text_to_find": "Add", "timeout": 30, "timestamp": 1752002277484, "type": "tapOnText"}, {"action_id": "F4NGh9HrLw", "executionTime": "878ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752002299940, "type": "tap"}, {"type": "tapIfLocatorExists", "timestamp": 1752314711044, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "action_id": "cTLBS0O1ot"}, {"action_id": "XoMyLp2unA", "executionTime": "6949ms", "interval": 0.5, "locator_type": "image", "locator_value": "cnc-tab-android.png", "timeout": 30, "timestamp": 1752003300507, "type": "waitTill"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "1363ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752002347806, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "534ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "rbzkUOQMtf", "duration": 4, "time": 4, "timestamp": 1752004166428, "type": "wait"}, {"action_id": "2p13JoJbbA", "executionTime": "1566ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1752002413851, "type": "tap"}, {"action_id": "QspAF2MJsL", "duration": 4, "time": 4, "timestamp": 1752004174638, "type": "wait"}, {"action_id": "x4yLCZHaCR", "executionTime": "227ms", "package_id": "au.com.kmart", "timestamp": 1746834909467, "type": "terminateApp"}], "labels": [], "updated": "2025-07-12 20:07:18"}