Action Log - 2025-07-13 10:35:02
================================================================================

[[10:35:02]] [INFO] Generating execution report...
[[10:35:02]] [SUCCESS] All tests passed successfully!
[[10:35:01]] [SUCCESS] Screenshot refreshed
[[10:35:01]] [INFO] Refreshing screenshot...
[[10:34:57]] [SUCCESS] Screenshot refreshed successfully
[[10:34:57]] [SUCCESS] Screenshot refreshed successfully
[[10:34:57]] [INFO] Executing action 15/15: Wait till text appears: "Filter"
[[10:34:56]] [SUCCESS] Screenshot refreshed
[[10:34:56]] [INFO] Refreshing screenshot...
[[10:34:49]] [SUCCESS] Screenshot refreshed successfully
[[10:34:49]] [SUCCESS] Screenshot refreshed successfully
[[10:34:49]] [INFO] Executing action 14/15: Tap on element with xpath: //android.widget.Button[@content-desc="Go to previous page"]
[[10:34:48]] [SUCCESS] Screenshot refreshed
[[10:34:48]] [INFO] Refreshing screenshot...
[[10:34:48]] [INFO] YH6erO83XY=pass
[[10:34:31]] [SUCCESS] Screenshot refreshed successfully
[[10:34:31]] [SUCCESS] Screenshot refreshed successfully
[[10:34:30]] [INFO] YH6erO83XY=running
[[10:34:30]] [INFO] Executing action 13/15: Swipe from (50%, 80%) to (50%, 10%)
[[10:34:30]] [SUCCESS] Screenshot refreshed
[[10:34:30]] [INFO] Refreshing screenshot...
[[10:34:25]] [SUCCESS] Screenshot refreshed successfully
[[10:34:25]] [SUCCESS] Screenshot refreshed successfully
[[10:34:24]] [INFO] Executing action 12/15: Wait till text appears: "Filter"
[[10:34:24]] [SUCCESS] Screenshot refreshed
[[10:34:24]] [INFO] Refreshing screenshot...
[[10:34:17]] [SUCCESS] Screenshot refreshed successfully
[[10:34:17]] [SUCCESS] Screenshot refreshed successfully
[[10:34:17]] [INFO] Executing action 11/15: Tap on element with xpath: //android.widget.Button[@content-desc="Go to previous page"]
[[10:34:16]] [SUCCESS] Screenshot refreshed
[[10:34:16]] [INFO] Refreshing screenshot...
[[10:34:16]] [INFO] YH6erO83XY=pass
[[10:33:59]] [SUCCESS] Screenshot refreshed successfully
[[10:33:59]] [SUCCESS] Screenshot refreshed successfully
[[10:33:59]] [INFO] YH6erO83XY=running
[[10:33:59]] [INFO] Executing action 10/15: Swipe from (50%, 80%) to (50%, 10%)
[[10:33:58]] [SUCCESS] Screenshot refreshed
[[10:33:58]] [INFO] Refreshing screenshot...
[[10:33:53]] [SUCCESS] Screenshot refreshed successfully
[[10:33:53]] [SUCCESS] Screenshot refreshed successfully
[[10:33:53]] [INFO] Executing action 9/15: Wait till text appears: "Filter"
[[10:33:53]] [SUCCESS] Screenshot refreshed
[[10:33:53]] [INFO] Refreshing screenshot...
[[10:33:53]] [INFO] 88BYVcWtJZ=pass
[[10:33:50]] [SUCCESS] Screenshot refreshed successfully
[[10:33:50]] [SUCCESS] Screenshot refreshed successfully
[[10:33:50]] [INFO] 88BYVcWtJZ=running
[[10:33:50]] [INFO] Executing action 8/15: Tap on element with xpath: //android.widget.Button[@content-desc="Go to next page"]
[[10:33:49]] [SUCCESS] Screenshot refreshed
[[10:33:49]] [INFO] Refreshing screenshot...
[[10:33:32]] [SUCCESS] Screenshot refreshed successfully
[[10:33:32]] [SUCCESS] Screenshot refreshed successfully
[[10:33:32]] [INFO] Executing action 7/15: Swipe from (50%, 80%) to (50%, 10%)
[[10:33:32]] [SUCCESS] Screenshot refreshed
[[10:33:32]] [INFO] Refreshing screenshot...
[[10:33:26]] [SUCCESS] Screenshot refreshed successfully
[[10:33:26]] [SUCCESS] Screenshot refreshed successfully
[[10:33:26]] [INFO] Executing action 6/15: Wait till text appears: "Filter"
[[10:33:26]] [SUCCESS] Screenshot refreshed
[[10:33:26]] [INFO] Refreshing screenshot...
[[10:33:26]] [INFO] 88BYVcWtJZ=pass
[[10:33:23]] [SUCCESS] Screenshot refreshed successfully
[[10:33:23]] [SUCCESS] Screenshot refreshed successfully
[[10:33:23]] [INFO] 88BYVcWtJZ=running
[[10:33:23]] [INFO] Executing action 5/15: Tap on element with xpath: //android.widget.Button[@content-desc="Go to next page"]
[[10:33:22]] [SUCCESS] Screenshot refreshed
[[10:33:22]] [INFO] Refreshing screenshot...
[[10:33:05]] [SUCCESS] Screenshot refreshed successfully
[[10:33:05]] [SUCCESS] Screenshot refreshed successfully
[[10:33:05]] [INFO] Executing action 4/15: Swipe from (50%, 80%) to (50%, 10%)
[[10:33:04]] [SUCCESS] Screenshot refreshed
[[10:33:04]] [INFO] Refreshing screenshot...
[[10:32:57]] [SUCCESS] Screenshot refreshed successfully
[[10:32:57]] [SUCCESS] Screenshot refreshed successfully
[[10:32:57]] [INFO] Executing action 3/15: Wait till text appears: "Filter"
[[10:32:57]] [SUCCESS] Screenshot refreshed
[[10:32:57]] [INFO] Refreshing screenshot...
[[10:32:57]] [INFO] 88BYVcWtJZ=pass
[[10:32:52]] [SUCCESS] Screenshot refreshed successfully
[[10:32:52]] [SUCCESS] Screenshot refreshed successfully
[[10:32:52]] [INFO] 88BYVcWtJZ=running
[[10:32:52]] [INFO] Executing action 2/15: Tap on element with xpath: //android.widget.Button[@content-desc="Go to next page"]
[[10:32:52]] [SUCCESS] Screenshot refreshed
[[10:32:52]] [INFO] Refreshing screenshot...
[[10:32:34]] [INFO] Executing action 1/15: Swipe from (50%, 80%) to (50%, 10%)
[[10:32:34]] [INFO] ExecutionManager: Starting execution of 15 actions...
[[10:32:34]] [SUCCESS] Cleared 187 screenshots from database
[[10:32:34]] [INFO] Clearing screenshots from database before execution...
[[10:32:34]] [SUCCESS] All screenshots deleted successfully
[[10:32:34]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:32:34]] [INFO] Skipping report initialization - single test case execution
[[10:32:14]] [SUCCESS] Test case Click_Paginations_Android saved successfully
[[10:32:14]] [INFO] Saving test case "Click_Paginations_Android"...
[[10:32:12]] [INFO] Action removed at index 15
[[10:32:05]] [SUCCESS] Duplicated action at index 12
[[10:32:05]] [SUCCESS] Added action at position 13
[[10:31:57]] [INFO] Action removed at index 13
[[10:31:51]] [SUCCESS] Duplicated action at index 11
[[10:31:51]] [SUCCESS] Added action at position 12
[[10:31:50]] [INFO] Action removed at index 13
[[10:31:44]] [SUCCESS] Duplicated action at index 10
[[10:31:44]] [SUCCESS] Added action at position 11
[[10:31:31]] [SUCCESS] Duplicated action at index 9
[[10:31:31]] [SUCCESS] Added action at position 10
[[10:31:28]] [INFO] Action removed at index 11
[[10:31:12]] [SUCCESS] Added tap action
[[10:31:12]] [SUCCESS] Added action: tap
[[10:29:35]] [SUCCESS] Duplicated action at index 6
[[10:29:35]] [SUCCESS] Added action at position 7
[[10:29:29]] [SUCCESS] Duplicated action at index 5
[[10:29:29]] [SUCCESS] Added action at position 6
[[10:29:27]] [INFO] Action removed at index 7
[[10:29:14]] [SUCCESS] Duplicated action at index 4
[[10:29:14]] [SUCCESS] Added action at position 5
[[10:29:12]] [INFO] Action removed at index 6
[[10:29:08]] [INFO] Action removed at index 6
[[10:29:03]] [SUCCESS] Screenshot refreshed successfully
[[10:29:02]] [INFO] G4PwPrzjcu=pass
[[10:29:02]] [SUCCESS] Action executed: Action executed successfully: waitTill
[[10:29:02]] [SUCCESS] Screenshot refreshed
[[10:29:02]] [INFO] Refreshing screenshot...
[[10:28:57]] [INFO] Executing action: Wait till text appears: "Filter"
[[10:28:55]] [SUCCESS] Screenshot refreshed successfully
[[10:28:55]] [INFO] 88BYVcWtJZ=pass
[[10:28:55]] [SUCCESS] Action executed: Action executed successfully: tap
[[10:28:55]] [SUCCESS] Screenshot refreshed
[[10:28:55]] [INFO] Refreshing screenshot...
[[10:28:52]] [INFO] Executing action: Tap on element with xpath: //android.widget.Button[@content-desc="Go to next page"]
[[10:28:47]] [SUCCESS] Duplicated action at index 3
[[10:28:47]] [SUCCESS] Added action at position 4
[[10:28:43]] [SUCCESS] Screenshot refreshed successfully
[[10:28:42]] [SUCCESS] Duplicated action at index 2
[[10:28:42]] [SUCCESS] Added action at position 3
[[10:28:42]] [INFO] czYEIjpN0B=pass
[[10:28:42]] [SUCCESS] Action executed: Action executed successfully: swipe
[[10:28:42]] [SUCCESS] Screenshot refreshed
[[10:28:42]] [INFO] Refreshing screenshot...
[[10:28:25]] [INFO] Executing action: Swipe from (50%, 80%) to (50%, 10%)
[[10:28:23]] [INFO] Action removed at index 4
[[10:28:16]] [SUCCESS] Duplicated action at index 1
[[10:28:16]] [SUCCESS] Added action at position 2
[[10:28:09]] [SUCCESS] Screenshot refreshed successfully
[[10:28:08]] [INFO] wM36ck9pM1=pass
[[10:28:08]] [SUCCESS] Action executed: Action executed successfully: waitTill
[[10:28:08]] [SUCCESS] Screenshot refreshed
[[10:28:08]] [INFO] Refreshing screenshot...
[[10:28:05]] [INFO] Executing action: Wait till text appears: "Filter"
[[10:28:04]] [SUCCESS] Test case Click_Paginations_Android saved successfully
[[10:28:04]] [INFO] Saving test case "Click_Paginations_Android"...
[[10:28:02]] [SUCCESS] Updated action #3
[[10:27:44]] [SUCCESS] Inserted tap action below step 2
[[10:27:28]] [SUCCESS] Screenshot refreshed successfully
[[10:27:28]] [INFO] 88BYVcWtJZ=pass
[[10:27:28]] [SUCCESS] Action executed: Action executed successfully: tap
[[10:27:28]] [SUCCESS] Screenshot refreshed
[[10:27:28]] [INFO] Refreshing screenshot...
[[10:27:25]] [INFO] Executing action: Tap on element with xpath: //android.widget.Button[@content-desc="Go to next page"]
[[10:27:24]] [SUCCESS] Test case Click_Paginations_Android saved successfully
[[10:27:24]] [INFO] Saving test case "Click_Paginations_Android"...
[[10:27:23]] [SUCCESS] Updated action #2
[[10:27:01]] [SUCCESS] Opened Appium Web Inspector with pre-filled connection details.
[[10:27:01]] [INFO] Opening URL: http://localhost:4723/inspector?host=127.0.0.1&port=4723&path=/wd/hub/
[[10:27:01]] [INFO] Found active session ID: 77b72678-593e-4b10-9d80-fc761ad6d713. Inspector should list this session under 'Attach to Session'.
[[10:27:01]] [INFO] Opening Appium Web Inspector in a new window...
[[10:27:01]] [INFO] Checking Appium Web Inspector availability...
[[10:26:59]] [SUCCESS] Test case Click_Paginations_Android saved successfully
[[10:26:59]] [INFO] Saving test case "Click_Paginations_Android"...
[[10:26:55]] [INFO] Action removed at index 1
[[10:26:32]] [SUCCESS] Screenshot refreshed successfully
[[10:26:31]] [INFO] ubMYJGfwHP=pass
[[10:26:31]] [SUCCESS] Action executed: Action executed successfully: swipe
[[10:26:31]] [SUCCESS] Screenshot refreshed
[[10:26:31]] [INFO] Refreshing screenshot...
[[10:26:03]] [INFO] Executing action: Swipe from (50%, 80%) to (50%, 10%)
[[10:25:54]] [SUCCESS] Updated action #12
[[10:25:44]] [SUCCESS] Screenshot refreshed successfully
[[10:25:43]] [INFO] nim5lo83tY=pass
[[10:25:43]] [SUCCESS] Action executed: Action executed successfully: swipe
[[10:25:43]] [SUCCESS] Screenshot refreshed
[[10:25:43]] [INFO] Refreshing screenshot...
[[10:25:23]] [INFO] Executing action: Swipe from (50%, 80%) to (50%, 10%)
[[10:25:14]] [SUCCESS] Added swipe action
[[10:25:14]] [SUCCESS] Added action: swipe
[[10:24:29]] [SUCCESS] Screenshot refreshed successfully
[[10:24:28]] [INFO] P9qhiM2aG3=pass
[[10:24:28]] [SUCCESS] Action executed: Action executed successfully: scrollToEnd
[[10:24:28]] [SUCCESS] Screenshot refreshed
[[10:24:28]] [INFO] Refreshing screenshot...
[[10:24:06]] [INFO] Executing action: scrollToEnd action
[[10:24:00]] [SUCCESS] Added scrollToEnd action
[[10:24:00]] [SUCCESS] Added action: scrollToEnd
[[10:23:33]] [SUCCESS] Screenshot refreshed successfully
[[10:23:32]] [INFO] YH6erO83XY=pass
[[10:23:32]] [SUCCESS] Action executed: Action executed successfully: scrollToEnd
[[10:23:32]] [SUCCESS] Screenshot refreshed
[[10:23:32]] [INFO] Refreshing screenshot...
[[10:23:15]] [INFO] Executing action: scrollToEnd action
[[10:23:09]] [SUCCESS] Updated action #1
[[10:22:38]] [SUCCESS] Screenshot refreshed successfully
[[10:22:37]] [INFO] YH6erO83XY=pass
[[10:22:37]] [SUCCESS] Action executed: Action executed successfully: scrollToEnd
[[10:22:37]] [SUCCESS] Screenshot refreshed
[[10:22:37]] [INFO] Refreshing screenshot...
[[10:21:49]] [INFO] Executing action: scrollToEnd action
[[10:21:45]] [SUCCESS] Updated action #1
[[10:21:29]] [SUCCESS] All screenshots deleted successfully
[[10:21:29]] [SUCCESS] Loaded test case "Click_Paginations_Android" with 10 actions
[[10:21:29]] [SUCCESS] Added action: tap
[[10:21:29]] [SUCCESS] Added action: swipe
[[10:21:29]] [SUCCESS] Added action: tap
[[10:21:29]] [SUCCESS] Added action: swipe
[[10:21:29]] [SUCCESS] Added action: tap
[[10:21:29]] [SUCCESS] Added action: swipe
[[10:21:29]] [SUCCESS] Added action: tap
[[10:21:29]] [SUCCESS] Added action: swipe
[[10:21:29]] [SUCCESS] Added action: tap
[[10:21:29]] [SUCCESS] Added action: swipe
[[10:21:29]] [INFO] All actions cleared
[[10:21:29]] [INFO] Cleaning up screenshots...
[[10:21:24]] [SUCCESS] Screenshot refreshed successfully
[[10:21:23]] [SUCCESS] Screenshot refreshed
[[10:21:23]] [INFO] Refreshing screenshot...
[[10:21:22]] [SUCCESS] Connected to device: ************:37391
[[10:21:22]] [INFO] Device info updated: RMX2151
[[10:21:13]] [INFO] Connecting to device: ************:37391 (Platform: Android)...
[[10:21:10]] [SUCCESS] All screenshots deleted successfully
[[10:21:10]] [SUCCESS] Loaded test case "Click_Paginations_Android" with 10 actions
[[10:21:10]] [SUCCESS] Added action: tap
[[10:21:10]] [SUCCESS] Added action: swipe
[[10:21:10]] [SUCCESS] Added action: tap
[[10:21:10]] [SUCCESS] Added action: swipe
[[10:21:10]] [SUCCESS] Added action: tap
[[10:21:10]] [SUCCESS] Added action: swipe
[[10:21:10]] [SUCCESS] Added action: tap
[[10:21:10]] [SUCCESS] Added action: swipe
[[10:21:10]] [SUCCESS] Added action: tap
[[10:21:10]] [SUCCESS] Added action: swipe
[[10:21:10]] [INFO] All actions cleared
[[10:21:10]] [INFO] Cleaning up screenshots...
[[10:21:08]] [SUCCESS] Found 3 device(s)
[[10:21:07]] [INFO] Refreshing device list...
