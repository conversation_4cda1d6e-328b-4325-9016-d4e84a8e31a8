Action Log - 2025-07-12 17:07:06
================================================================================

[[17:07:06]] [INFO] Generating execution report...
[[17:07:06]] [SUCCESS] All tests passed successfully!
[[17:07:06]] [SUCCESS] Screenshot refreshed
[[17:07:06]] [INFO] Refreshing screenshot...
[[17:07:06]] [INFO] BracBsfa3Y=pass
[[17:07:02]] [SUCCESS] Screenshot refreshed successfully
[[17:07:02]] [SUCCESS] Screenshot refreshed successfully
[[17:07:00]] [INFO] BracBsfa3Y=running
[[17:07:00]] [INFO] Executing action 60/60: Tap on Text: "out"
[[17:07:00]] [SUCCESS] Screenshot refreshed
[[17:07:00]] [INFO] Refreshing screenshot...
[[17:07:00]] [INFO] s6tWdQ5URW=pass
[[17:06:55]] [SUCCESS] Screenshot refreshed successfully
[[17:06:55]] [SUCCESS] Screenshot refreshed successfully
[[17:06:55]] [INFO] s6tWdQ5URW=running
[[17:06:55]] [INFO] Executing action 59/60: Swipe from (50%, 70%) to (50%, 30%)
[[17:06:54]] [SUCCESS] Screenshot refreshed
[[17:06:54]] [INFO] Refreshing screenshot...
[[17:06:54]] [INFO] YuuQe2KupX=pass
[[17:06:52]] [INFO] YuuQe2KupX=running
[[17:06:52]] [INFO] Executing action 58/60: Android Function: send_key_event - Key Event: BACK
[[17:06:52]] [INFO] BracBsfa3Y=fail
[[17:06:52]] [ERROR] Action 57 failed: Error tapping on text: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[17:06:19]] [SUCCESS] Screenshot refreshed successfully
[[17:06:19]] [SUCCESS] Screenshot refreshed successfully
[[17:06:17]] [INFO] BracBsfa3Y=running
[[17:06:17]] [INFO] Executing action 57/60: Tap on Text: "Customer"
[[17:06:17]] [SUCCESS] Screenshot refreshed
[[17:06:17]] [INFO] Refreshing screenshot...
[[17:06:17]] [INFO] YuuQe2KupX=pass
[[17:06:15]] [SUCCESS] Screenshot refreshed successfully
[[17:06:15]] [SUCCESS] Screenshot refreshed successfully
[[17:06:15]] [INFO] YuuQe2KupX=running
[[17:06:15]] [INFO] Executing action 56/60: Android Function: send_key_event - Key Event: BACK
[[17:06:14]] [SUCCESS] Screenshot refreshed
[[17:06:14]] [INFO] Refreshing screenshot...
[[17:06:14]] [INFO] ePyaYpttQA=pass
[[17:06:12]] [SUCCESS] Screenshot refreshed successfully
[[17:06:12]] [SUCCESS] Screenshot refreshed successfully
[[17:06:12]] [INFO] ePyaYpttQA=running
[[17:06:12]] [INFO] Executing action 55/60: Check if element with xpath="//android.widget.TextView[@resource-id="android:id/content_preview_text"]" exists
[[17:06:12]] [SUCCESS] Screenshot refreshed
[[17:06:12]] [INFO] Refreshing screenshot...
[[17:06:12]] [INFO] BracBsfa3Y=pass
[[17:06:04]] [SUCCESS] Screenshot refreshed successfully
[[17:06:04]] [SUCCESS] Screenshot refreshed successfully
[[17:06:04]] [INFO] BracBsfa3Y=running
[[17:06:04]] [INFO] Executing action 54/60: Tap on Text: "Invite"
[[17:06:03]] [SUCCESS] Screenshot refreshed
[[17:06:03]] [INFO] Refreshing screenshot...
[[17:06:03]] [INFO] YuuQe2KupX=pass
[[17:06:01]] [SUCCESS] Screenshot refreshed successfully
[[17:06:01]] [SUCCESS] Screenshot refreshed successfully
[[17:06:01]] [INFO] YuuQe2KupX=running
[[17:06:01]] [INFO] Executing action 53/60: Android Function: send_key_event - Key Event: BACK
[[17:06:01]] [SUCCESS] Screenshot refreshed
[[17:06:01]] [INFO] Refreshing screenshot...
[[17:06:01]] [INFO] 2BfJyzwQFx=pass
[[17:05:58]] [SUCCESS] Screenshot refreshed successfully
[[17:05:58]] [SUCCESS] Screenshot refreshed successfully
[[17:05:55]] [INFO] 2BfJyzwQFx=running
[[17:05:55]] [INFO] Executing action 52/60: Check if element with text="Cbd" exists
[[17:05:55]] [SUCCESS] Screenshot refreshed
[[17:05:55]] [INFO] Refreshing screenshot...
[[17:05:55]] [INFO] PgjJCrKFYo=pass
[[17:05:39]] [SUCCESS] Screenshot refreshed successfully
[[17:05:39]] [SUCCESS] Screenshot refreshed successfully
[[17:05:38]] [INFO] PgjJCrKFYo=running
[[17:05:38]] [INFO] Executing action 51/60: Tap on Text: "VIC"
[[17:05:38]] [SUCCESS] Screenshot refreshed
[[17:05:38]] [INFO] Refreshing screenshot...
[[17:05:38]] [INFO] 3Si0csRNaw=pass
[[17:05:36]] [SUCCESS] Screenshot refreshed successfully
[[17:05:36]] [SUCCESS] Screenshot refreshed successfully
[[17:05:36]] [INFO] 3Si0csRNaw=running
[[17:05:36]] [INFO] Executing action 50/60: Input text: "3000"
[[17:05:35]] [SUCCESS] Screenshot refreshed
[[17:05:35]] [INFO] Refreshing screenshot...
[[17:05:35]] [INFO] BracBsfa3Y=pass
[[17:05:11]] [SUCCESS] Screenshot refreshed successfully
[[17:05:11]] [SUCCESS] Screenshot refreshed successfully
[[17:05:11]] [INFO] BracBsfa3Y=running
[[17:05:11]] [INFO] Executing action 49/60: Tap on Text: "Nearby"
[[17:05:10]] [SUCCESS] Screenshot refreshed
[[17:05:10]] [INFO] Refreshing screenshot...
[[17:05:10]] [INFO] BracBsfa3Y=pass
[[17:05:07]] [SUCCESS] Screenshot refreshed successfully
[[17:05:07]] [SUCCESS] Screenshot refreshed successfully
[[17:05:05]] [INFO] BracBsfa3Y=running
[[17:05:05]] [INFO] Executing action 48/60: Tap on Text: "locator"
[[17:05:04]] [SUCCESS] Screenshot refreshed
[[17:05:04]] [INFO] Refreshing screenshot...
[[17:05:04]] [INFO] s6tWdQ5URW=pass
[[17:04:59]] [SUCCESS] Screenshot refreshed successfully
[[17:04:59]] [SUCCESS] Screenshot refreshed successfully
[[17:04:59]] [INFO] s6tWdQ5URW=running
[[17:04:59]] [INFO] Executing action 47/60: Swipe from (50%, 70%) to (50%, 30%)
[[17:04:59]] [SUCCESS] Screenshot refreshed
[[17:04:59]] [INFO] Refreshing screenshot...
[[17:04:59]] [INFO] 2M0KHOVecv=pass
[[17:04:57]] [SUCCESS] Screenshot refreshed successfully
[[17:04:57]] [SUCCESS] Screenshot refreshed successfully
[[17:04:57]] [INFO] 2M0KHOVecv=running
[[17:04:57]] [INFO] Executing action 46/60: Check if element with accessibility_id="txtMy Flybuys card" exists
[[17:04:56]] [SUCCESS] Screenshot refreshed
[[17:04:56]] [INFO] Refreshing screenshot...
[[17:04:56]] [INFO] YuuQe2KupX=pass
[[17:04:54]] [SUCCESS] Screenshot refreshed successfully
[[17:04:54]] [SUCCESS] Screenshot refreshed successfully
[[17:04:54]] [INFO] YuuQe2KupX=running
[[17:04:54]] [INFO] Executing action 45/60: Android Function: send_key_event - Key Event: BACK
[[17:04:53]] [SUCCESS] Screenshot refreshed
[[17:04:53]] [INFO] Refreshing screenshot...
[[17:04:53]] [INFO] biRyWs3nSs=pass
[[17:04:12]] [SUCCESS] Screenshot refreshed successfully
[[17:04:12]] [SUCCESS] Screenshot refreshed successfully
[[17:04:11]] [INFO] biRyWs3nSs=running
[[17:04:11]] [INFO] Executing action 44/60: Tap on element with accessibility_id: btnSaveFlybuysCard
[[17:04:11]] [SUCCESS] Screenshot refreshed
[[17:04:11]] [INFO] Refreshing screenshot...
[[17:04:11]] [INFO] sLe0Wurhgm=pass
[[17:04:09]] [SUCCESS] Screenshot refreshed successfully
[[17:04:09]] [SUCCESS] Screenshot refreshed successfully
[[17:04:09]] [INFO] sLe0Wurhgm=running
[[17:04:09]] [INFO] Executing action 43/60: Input text: "2791234567890"
[[17:04:08]] [SUCCESS] Screenshot refreshed
[[17:04:08]] [INFO] Refreshing screenshot...
[[17:04:08]] [INFO] Ey86YRVRzU=pass
[[17:04:06]] [SUCCESS] Screenshot refreshed successfully
[[17:04:06]] [SUCCESS] Screenshot refreshed successfully
[[17:04:05]] [INFO] Ey86YRVRzU=running
[[17:04:05]] [INFO] Executing action 42/60: Tap on element with uiselector: new UiSelector().className("android.widget.EditText")
[[17:04:05]] [SUCCESS] Screenshot refreshed
[[17:04:05]] [INFO] Refreshing screenshot...
[[17:04:05]] [INFO] Gxhf3XGc6e=pass
[[17:04:03]] [SUCCESS] Screenshot refreshed successfully
[[17:04:03]] [SUCCESS] Screenshot refreshed successfully
[[17:04:02]] [INFO] Gxhf3XGc6e=running
[[17:04:02]] [INFO] Executing action 41/60: Tap on element with accessibility_id: btnLinkFlyBuys
[[17:04:02]] [SUCCESS] Screenshot refreshed
[[17:04:02]] [INFO] Refreshing screenshot...
[[17:04:02]] [INFO] BracBsfa3Y=pass
[[17:03:58]] [SUCCESS] Screenshot refreshed successfully
[[17:03:58]] [SUCCESS] Screenshot refreshed successfully
[[17:03:57]] [INFO] BracBsfa3Y=running
[[17:03:57]] [INFO] Executing action 40/60: Tap on Text: "Flybuys"
[[17:03:57]] [SUCCESS] Screenshot refreshed
[[17:03:57]] [INFO] Refreshing screenshot...
[[17:03:57]] [INFO] Ds5GfNVb3x=pass
[[17:03:55]] [SUCCESS] Screenshot refreshed successfully
[[17:03:55]] [SUCCESS] Screenshot refreshed successfully
[[17:03:54]] [INFO] Ds5GfNVb3x=running
[[17:03:54]] [INFO] Executing action 39/60: Tap on element with accessibility_id: btnRemove
[[17:03:54]] [SUCCESS] Screenshot refreshed
[[17:03:54]] [INFO] Refreshing screenshot...
[[17:03:54]] [INFO] 3ZFgwFaiXp=pass
[[17:03:12]] [SUCCESS] Screenshot refreshed successfully
[[17:03:12]] [SUCCESS] Screenshot refreshed successfully
[[17:03:11]] [INFO] 3ZFgwFaiXp=running
[[17:03:11]] [INFO] Executing action 38/60: Tap on element with accessibility_id: Remove card
[[17:03:11]] [SUCCESS] Screenshot refreshed
[[17:03:11]] [INFO] Refreshing screenshot...
[[17:03:11]] [INFO] 40hnWPsQ9P=pass
[[17:03:08]] [SUCCESS] Screenshot refreshed successfully
[[17:03:08]] [SUCCESS] Screenshot refreshed successfully
[[17:03:08]] [INFO] 40hnWPsQ9P=running
[[17:03:08]] [INFO] Executing action 37/60: Tap on element with accessibility_id: btneditFlybuysCard
[[17:03:07]] [SUCCESS] Screenshot refreshed
[[17:03:07]] [INFO] Refreshing screenshot...
[[17:03:07]] [INFO] 40hnWPsQ9P=pass
[[17:03:06]] [SUCCESS] Screenshot refreshed successfully
[[17:03:06]] [SUCCESS] Screenshot refreshed successfully
[[17:03:05]] [INFO] 40hnWPsQ9P=running
[[17:03:05]] [INFO] Executing action 36/60: Wait till accessibility_id=btneditFlybuysCard
[[17:03:05]] [SUCCESS] Screenshot refreshed
[[17:03:05]] [INFO] Refreshing screenshot...
[[17:03:05]] [INFO] BracBsfa3Y=pass
[[17:03:01]] [SUCCESS] Screenshot refreshed successfully
[[17:03:01]] [SUCCESS] Screenshot refreshed successfully
[[17:02:59]] [INFO] BracBsfa3Y=running
[[17:02:59]] [INFO] Executing action 35/60: Tap on Text: "Flybuys"
[[17:02:58]] [SUCCESS] Screenshot refreshed
[[17:02:58]] [INFO] Refreshing screenshot...
[[17:02:58]] [INFO] YuuQe2KupX=pass
[[17:02:57]] [SUCCESS] Screenshot refreshed successfully
[[17:02:57]] [SUCCESS] Screenshot refreshed successfully
[[17:02:56]] [INFO] YuuQe2KupX=running
[[17:02:56]] [INFO] Executing action 34/60: Android Function: send_key_event - Key Event: BACK
[[17:02:56]] [SUCCESS] Screenshot refreshed
[[17:02:56]] [INFO] Refreshing screenshot...
[[17:02:56]] [INFO] napKDohf3Z=pass
[[17:02:52]] [SUCCESS] Screenshot refreshed successfully
[[17:02:52]] [SUCCESS] Screenshot refreshed successfully
[[17:02:12]] [INFO] napKDohf3Z=running
[[17:02:12]] [INFO] Executing action 33/60: Tap on Text: "payment"
[[17:02:11]] [SUCCESS] Screenshot refreshed
[[17:02:11]] [INFO] Refreshing screenshot...
[[17:02:11]] [INFO] YuuQe2KupX=pass
[[17:02:09]] [SUCCESS] Screenshot refreshed successfully
[[17:02:09]] [SUCCESS] Screenshot refreshed successfully
[[17:02:09]] [INFO] YuuQe2KupX=running
[[17:02:09]] [INFO] Executing action 32/60: Android Function: send_key_event - Key Event: BACK
[[17:02:09]] [SUCCESS] Screenshot refreshed
[[17:02:09]] [INFO] Refreshing screenshot...
[[17:02:09]] [INFO] 20qUCJgpE9=pass
[[17:02:05]] [SUCCESS] Screenshot refreshed successfully
[[17:02:05]] [SUCCESS] Screenshot refreshed successfully
[[17:02:03]] [INFO] 20qUCJgpE9=running
[[17:02:03]] [INFO] Executing action 31/60: Tap on Text: "address"
[[17:02:02]] [SUCCESS] Screenshot refreshed
[[17:02:02]] [INFO] Refreshing screenshot...
[[17:02:02]] [INFO] YuuQe2KupX=pass
[[17:02:00]] [SUCCESS] Screenshot refreshed successfully
[[17:02:00]] [SUCCESS] Screenshot refreshed successfully
[[17:02:00]] [INFO] YuuQe2KupX=running
[[17:02:00]] [INFO] Executing action 30/60: Android Function: send_key_event - Key Event: BACK
[[17:01:59]] [SUCCESS] Screenshot refreshed
[[17:01:59]] [INFO] Refreshing screenshot...
[[17:01:59]] [INFO] 3hOTINBVMf=pass
[[17:01:28]] [INFO] 3hOTINBVMf=running
[[17:01:28]] [INFO] Executing action 29/60: Tap on Text: "details"
[[17:01:28]] [INFO] yJi0WxnERj=fail
[[17:01:28]] [ERROR] Action 28 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[17:00:57]] [SUCCESS] Screenshot refreshed successfully
[[17:00:57]] [SUCCESS] Screenshot refreshed successfully
[[17:00:56]] [INFO] yJi0WxnERj=running
[[17:00:56]] [INFO] Executing action 28/60: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[17:00:56]] [SUCCESS] Screenshot refreshed
[[17:00:56]] [INFO] Refreshing screenshot...
[[17:00:56]] [INFO] sl3Wk1gK8X=pass
[[17:00:11]] [SUCCESS] Screenshot refreshed successfully
[[17:00:11]] [SUCCESS] Screenshot refreshed successfully
[[17:00:11]] [INFO] sl3Wk1gK8X=running
[[17:00:11]] [INFO] Executing action 27/60: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:00:10]] [SUCCESS] Screenshot refreshed
[[17:00:10]] [INFO] Refreshing screenshot...
[[17:00:10]] [INFO] vmc01sHkbr=pass
[[17:00:04]] [SUCCESS] Screenshot refreshed successfully
[[17:00:04]] [SUCCESS] Screenshot refreshed successfully
[[17:00:04]] [INFO] vmc01sHkbr=running
[[17:00:04]] [INFO] Executing action 26/60: Wait for 5 ms
[[17:00:03]] [SUCCESS] Screenshot refreshed
[[17:00:03]] [INFO] Refreshing screenshot...
[[17:00:03]] [INFO] YuuQe2KupX=pass
[[17:00:01]] [SUCCESS] Screenshot refreshed successfully
[[17:00:01]] [SUCCESS] Screenshot refreshed successfully
[[17:00:01]] [INFO] YuuQe2KupX=running
[[17:00:01]] [INFO] Executing action 25/60: Android Function: send_key_event - Key Event: BACK
[[17:00:01]] [SUCCESS] Screenshot refreshed
[[17:00:01]] [INFO] Refreshing screenshot...
[[17:00:01]] [INFO] aAaTtUE92h=pass
[[16:59:59]] [SUCCESS] Screenshot refreshed successfully
[[16:59:59]] [SUCCESS] Screenshot refreshed successfully
[[16:59:58]] [INFO] aAaTtUE92h=running
[[16:59:58]] [INFO] Executing action 24/60: Check if element with xpath="//android.widget.TextView[@resource-id="exchanges-returns"]" exists
[[16:59:58]] [SUCCESS] Screenshot refreshed
[[16:59:58]] [INFO] Refreshing screenshot...
[[16:59:58]] [INFO] 9iOZGMqAZK=pass
[[16:59:56]] [SUCCESS] Screenshot refreshed successfully
[[16:59:56]] [SUCCESS] Screenshot refreshed successfully
[[16:59:55]] [INFO] 9iOZGMqAZK=running
[[16:59:55]] [INFO] Executing action 23/60: Tap on element with xpath: //android.widget.TextView[@text="Learn more about refunds"]
[[16:59:55]] [SUCCESS] Screenshot refreshed
[[16:59:55]] [INFO] Refreshing screenshot...
[[16:59:55]] [INFO] mRTYzOFRRw=pass
[[16:59:22]] [INFO] mRTYzOFRRw=running
[[16:59:22]] [INFO] Executing action 22/60: Check if element with xpath="//android.widget.TextView[@text="Learn more about refunds"]" exists
[[16:59:22]] [INFO] 7g6MFJSGIO=fail
[[16:59:22]] [ERROR] Action 21 failed: Error tapping element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[16:58:19]] [SUCCESS] Screenshot refreshed successfully
[[16:58:19]] [SUCCESS] Screenshot refreshed successfully
[[16:58:19]] [INFO] 7g6MFJSGIO=running
[[16:58:19]] [INFO] Executing action 21/60: Tap on element with xpath: (//android.widget.TextView[@text="ID"]/following-sibling::android.view.View/android.view.View/android.widget.TextView)[1]
[[16:58:19]] [SUCCESS] Screenshot refreshed
[[16:58:19]] [INFO] Refreshing screenshot...
[[16:58:19]] [INFO] zNwyPagPE1=pass
[[16:58:12]] [SUCCESS] Screenshot refreshed successfully
[[16:58:12]] [SUCCESS] Screenshot refreshed successfully
[[16:58:12]] [INFO] zNwyPagPE1=running
[[16:58:12]] [INFO] Executing action 20/60: Wait for 5 ms
[[16:58:11]] [SUCCESS] Screenshot refreshed
[[16:58:11]] [INFO] Refreshing screenshot...
[[16:58:11]] [INFO] YuuQe2KupX=pass
[[16:58:09]] [SUCCESS] Screenshot refreshed successfully
[[16:58:09]] [SUCCESS] Screenshot refreshed successfully
[[16:58:09]] [INFO] YuuQe2KupX=running
[[16:58:09]] [INFO] Executing action 19/60: Tap on element with xpath: //android.widget.Button[@text="Return to my orders"]
[[16:58:08]] [SUCCESS] Screenshot refreshed
[[16:58:08]] [INFO] Refreshing screenshot...
[[16:58:08]] [INFO] YuuQe2KupX=pass
[[16:58:05]] [SUCCESS] Screenshot refreshed successfully
[[16:58:05]] [SUCCESS] Screenshot refreshed successfully
[[16:58:04]] [INFO] YuuQe2KupX=running
[[16:58:04]] [INFO] Executing action 18/60: Swipe up till element xpath: "//android.widget.Button[@text="Return to my orders"]" is visible
[[16:58:04]] [SUCCESS] Screenshot refreshed
[[16:58:04]] [INFO] Refreshing screenshot...
[[16:58:04]] [INFO] YuuQe2KupX=pass
[[16:58:02]] [SUCCESS] Screenshot refreshed successfully
[[16:58:02]] [SUCCESS] Screenshot refreshed successfully
[[16:58:02]] [INFO] YuuQe2KupX=running
[[16:58:02]] [INFO] Executing action 17/60: Android Function: send_key_event - Key Event: BACK
[[16:58:01]] [SUCCESS] Screenshot refreshed
[[16:58:01]] [INFO] Refreshing screenshot...
[[16:58:01]] [INFO] zNwyPagPE1=pass
[[16:57:53]] [SUCCESS] Screenshot refreshed successfully
[[16:57:53]] [SUCCESS] Screenshot refreshed successfully
[[16:57:53]] [INFO] zNwyPagPE1=running
[[16:57:53]] [INFO] Executing action 16/60: Wait for 6 ms
[[16:57:53]] [SUCCESS] Screenshot refreshed
[[16:57:53]] [INFO] Refreshing screenshot...
[[16:57:53]] [INFO] g0PE7Mofye=pass
[[16:57:12]] [SUCCESS] Screenshot refreshed successfully
[[16:57:12]] [SUCCESS] Screenshot refreshed successfully
[[16:57:12]] [INFO] g0PE7Mofye=running
[[16:57:12]] [INFO] Executing action 15/60: Tap on Text: "Print"
[[16:57:12]] [SUCCESS] Screenshot refreshed
[[16:57:12]] [INFO] Refreshing screenshot...
[[16:57:12]] [INFO] zNwyPagPE1=pass
[[16:57:04]] [SUCCESS] Screenshot refreshed successfully
[[16:57:04]] [SUCCESS] Screenshot refreshed successfully
[[16:57:04]] [INFO] zNwyPagPE1=running
[[16:57:04]] [INFO] Executing action 14/60: Wait for 6 ms
[[16:57:03]] [SUCCESS] Screenshot refreshed
[[16:57:03]] [INFO] Refreshing screenshot...
[[16:57:03]] [INFO] GgQaBLWYkb=pass
[[16:56:59]] [SUCCESS] Screenshot refreshed successfully
[[16:56:59]] [SUCCESS] Screenshot refreshed successfully
[[16:56:58]] [INFO] GgQaBLWYkb=running
[[16:56:58]] [INFO] Executing action 13/60: Tap on Text: "invoice"
[[16:56:57]] [SUCCESS] Screenshot refreshed
[[16:56:57]] [INFO] Refreshing screenshot...
[[16:56:57]] [INFO] Rl6s389Qsd=pass
[[16:56:46]] [SUCCESS] Screenshot refreshed successfully
[[16:56:46]] [SUCCESS] Screenshot refreshed successfully
[[16:56:45]] [INFO] Rl6s389Qsd=running
[[16:56:45]] [INFO] Executing action 12/60: Swipe from (50%, 70%) to (50%, 30%)
[[16:56:45]] [SUCCESS] Screenshot refreshed
[[16:56:45]] [INFO] Refreshing screenshot...
[[16:56:45]] [INFO] ADHRFCY0LX=pass
[[16:56:33]] [SUCCESS] Screenshot refreshed successfully
[[16:56:33]] [SUCCESS] Screenshot refreshed successfully
[[16:56:33]] [INFO] ADHRFCY0LX=running
[[16:56:33]] [INFO] Executing action 11/60: Wait for 10 ms
[[16:56:32]] [SUCCESS] Screenshot refreshed
[[16:56:32]] [INFO] Refreshing screenshot...
[[16:56:32]] [INFO] 7g6MFJSGIO=pass
[[16:56:29]] [SUCCESS] Screenshot refreshed successfully
[[16:56:29]] [SUCCESS] Screenshot refreshed successfully
[[16:56:28]] [INFO] 7g6MFJSGIO=running
[[16:56:28]] [INFO] Executing action 10/60: Tap on element with xpath: (//android.widget.TextView[@text="ID"]/following-sibling::android.view.View/android.view.View/android.widget.TextView)[1]
[[16:56:28]] [SUCCESS] Screenshot refreshed
[[16:56:28]] [INFO] Refreshing screenshot...
[[16:56:28]] [INFO] Z6g3sGuHTp=pass
[[16:56:21]] [SUCCESS] Screenshot refreshed successfully
[[16:56:21]] [SUCCESS] Screenshot refreshed successfully
[[16:56:21]] [INFO] Z6g3sGuHTp=running
[[16:56:21]] [INFO] Executing action 9/60: Wait for 5 ms
[[16:56:20]] [SUCCESS] Screenshot refreshed
[[16:56:20]] [INFO] Refreshing screenshot...
[[16:56:20]] [INFO] pFlYwTS53v=pass
[[16:56:17]] [SUCCESS] Screenshot refreshed successfully
[[16:56:17]] [SUCCESS] Screenshot refreshed successfully
[[16:56:15]] [INFO] pFlYwTS53v=running
[[16:56:15]] [INFO] Executing action 8/60: Tap on Text: "receipts"
[[16:56:14]] [SUCCESS] Screenshot refreshed
[[16:56:14]] [INFO] Refreshing screenshot...
[[16:56:14]] [INFO] V59u3l1wkM=pass
[[16:56:12]] [SUCCESS] Screenshot refreshed successfully
[[16:56:12]] [SUCCESS] Screenshot refreshed successfully
[[16:56:12]] [INFO] V59u3l1wkM=running
[[16:56:12]] [INFO] Executing action 7/60: Wait till xpath=//android.view.View[contains(@content-desc,"Manage your account")]
[[16:56:11]] [SUCCESS] Screenshot refreshed
[[16:56:11]] [INFO] Refreshing screenshot...
[[16:56:11]] [INFO] sl3Wk1gK8X=pass
[[16:56:08]] [SUCCESS] Screenshot refreshed successfully
[[16:56:08]] [SUCCESS] Screenshot refreshed successfully
[[16:56:04]] [INFO] sl3Wk1gK8X=running
[[16:56:04]] [INFO] Executing action 6/60: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:56:04]] [SUCCESS] Screenshot refreshed
[[16:56:04]] [INFO] Refreshing screenshot...
[[16:56:03]] [SUCCESS] Screenshot refreshed
[[16:56:03]] [INFO] Refreshing screenshot...
[[16:56:01]] [SUCCESS] Screenshot refreshed successfully
[[16:56:01]] [SUCCESS] Screenshot refreshed successfully
[[16:56:01]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[16:56:00]] [SUCCESS] Screenshot refreshed
[[16:56:00]] [INFO] Refreshing screenshot...
[[16:55:57]] [SUCCESS] Screenshot refreshed successfully
[[16:55:57]] [SUCCESS] Screenshot refreshed successfully
[[16:55:57]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:55:57]] [SUCCESS] Screenshot refreshed
[[16:55:57]] [INFO] Refreshing screenshot...
[[16:55:54]] [SUCCESS] Screenshot refreshed successfully
[[16:55:54]] [SUCCESS] Screenshot refreshed successfully
[[16:55:54]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[16:55:53]] [SUCCESS] Screenshot refreshed
[[16:55:53]] [INFO] Refreshing screenshot...
[[16:55:12]] [SUCCESS] Screenshot refreshed successfully
[[16:55:12]] [SUCCESS] Screenshot refreshed successfully
[[16:55:11]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:55:11]] [SUCCESS] Screenshot refreshed
[[16:55:11]] [INFO] Refreshing screenshot...
[[16:55:07]] [SUCCESS] Screenshot refreshed successfully
[[16:55:07]] [SUCCESS] Screenshot refreshed successfully
[[16:55:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:55:07]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[16:55:07]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:55:07]] [INFO] rrSllLCwLu=running
[[16:55:07]] [INFO] Executing action 5/60: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[16:55:06]] [SUCCESS] Screenshot refreshed
[[16:55:06]] [INFO] Refreshing screenshot...
[[16:55:06]] [INFO] xAPeBnVHrT=pass
[[16:55:04]] [SUCCESS] Screenshot refreshed successfully
[[16:55:04]] [SUCCESS] Screenshot refreshed successfully
[[16:55:04]] [INFO] xAPeBnVHrT=running
[[16:55:04]] [INFO] Executing action 4/60: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:55:04]] [SUCCESS] Screenshot refreshed
[[16:55:04]] [INFO] Refreshing screenshot...
[[16:55:04]] [INFO] u6bRYZZFAv=pass
[[16:54:57]] [SUCCESS] Screenshot refreshed successfully
[[16:54:57]] [SUCCESS] Screenshot refreshed successfully
[[16:54:56]] [INFO] u6bRYZZFAv=running
[[16:54:56]] [INFO] Executing action 3/60: Wait for 5 ms
[[16:54:56]] [SUCCESS] Screenshot refreshed
[[16:54:56]] [INFO] Refreshing screenshot...
[[16:54:56]] [INFO] pjFNt3w5Fr=pass
[[16:54:54]] [SUCCESS] Screenshot refreshed successfully
[[16:54:54]] [SUCCESS] Screenshot refreshed successfully
[[16:54:53]] [INFO] pjFNt3w5Fr=running
[[16:54:53]] [INFO] Executing action 2/60: Launch app: au.com.kmart
[[16:54:52]] [SUCCESS] Screenshot refreshed
[[16:54:52]] [INFO] Refreshing screenshot...
[[16:54:52]] [INFO] FK0xWTx8zz=pass
[[16:54:24]] [INFO] FK0xWTx8zz=running
[[16:54:24]] [INFO] Executing action 1/60: Terminate app: au.com.kmart
[[16:54:24]] [INFO] ExecutionManager: Starting execution of 60 actions...
[[16:54:24]] [SUCCESS] Cleared 1 screenshots from database
[[16:54:24]] [INFO] Clearing screenshots from database before execution...
[[16:54:24]] [SUCCESS] All screenshots deleted successfully
[[16:54:24]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:54:24]] [INFO] Skipping report initialization - single test case execution
[[16:54:08]] [SUCCESS] All screenshots deleted successfully
[[16:54:08]] [SUCCESS] Loaded test case "AU- MyAccount_Android" with 60 actions
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: swipe
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: exists
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: exists
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: text
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: swipe
[[16:54:08]] [SUCCESS] Added action: exists
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: text
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: waitTill
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: waitTill
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: exists
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: exists
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: swipeTillVisible
[[16:54:08]] [SUCCESS] Added action: androidFunctions
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: swipe
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: tapOnText
[[16:54:08]] [SUCCESS] Added action: waitTill
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: multiStep
[[16:54:08]] [SUCCESS] Added action: tap
[[16:54:08]] [SUCCESS] Added action: wait
[[16:54:08]] [SUCCESS] Added action: launchApp
[[16:54:08]] [SUCCESS] Added action: terminateApp
[[16:54:08]] [INFO] All actions cleared
[[16:54:08]] [INFO] Cleaning up screenshots...
[[16:54:08]] [SUCCESS] Screenshot refreshed successfully
[[16:54:07]] [SUCCESS] Screenshot refreshed
[[16:54:07]] [INFO] Refreshing screenshot...
[[16:54:06]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[16:54:06]] [INFO] Device info updated: RMX2151
[[16:54:00]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[16:53:59]] [SUCCESS] Found 1 device(s)
[[16:53:58]] [INFO] Refreshing device list...
