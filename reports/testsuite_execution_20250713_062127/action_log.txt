Action Log - 2025-07-13 06:26:15
================================================================================

[[06:26:15]] [INFO] Generating execution report...
[[06:26:15]] [SUCCESS] All tests passed successfully!
[[06:26:15]] [INFO] Moving to the next test case after failure (server will handle retry)
[[06:26:15]] [ERROR] Error executing Multi Step action step 5: Failed to fetch
[[06:26:00]] [SUCCESS] Screenshot refreshed successfully
[[06:26:00]] [SUCCESS] Screenshot refreshed successfully
[[06:26:00]] [INFO] Executing Multi Step action step 5/48: Tap on Text: "First"
[[06:26:00]] [SUCCESS] Screenshot refreshed
[[06:26:00]] [INFO] Refreshing screenshot...
[[06:25:30]] [SUCCESS] Screenshot refreshed successfully
[[06:25:30]] [SUCCESS] Screenshot refreshed successfully
[[06:25:30]] [INFO] Executing Multi Step action step 4/48: Tap on image: delivery-address-options-android.png
[[06:25:29]] [SUCCESS] Screenshot refreshed
[[06:25:29]] [INFO] Refreshing screenshot...
[[06:25:24]] [SUCCESS] Screenshot refreshed successfully
[[06:25:24]] [SUCCESS] Screenshot refreshed successfully
[[06:25:24]] [INFO] Executing Multi Step action step 3/48: Swipe from (50%, 70%) to (50%, 30%)
[[06:25:23]] [SUCCESS] Screenshot refreshed
[[06:25:23]] [INFO] Refreshing screenshot...
[[06:25:20]] [SUCCESS] Screenshot refreshed successfully
[[06:25:20]] [SUCCESS] Screenshot refreshed successfully
[[06:25:20]] [INFO] Executing Multi Step action step 2/48: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[06:25:20]] [SUCCESS] Screenshot refreshed
[[06:25:20]] [INFO] Refreshing screenshot...
[[06:25:14]] [INFO] Executing Multi Step action step 1/48: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[06:25:14]] [INFO] Loaded 48 steps from test case: Delivery Buy Steps_AU-ANDROID
[[06:25:14]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[06:25:14]] [INFO] e5zwMRuhB1=running
[[06:25:14]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (48 steps)
[[06:25:14]] [SUCCESS] Screenshot refreshed successfully
[[06:25:14]] [SUCCESS] Screenshot refreshed successfully
[[06:25:13]] [SUCCESS] Screenshot refreshed
[[06:25:13]] [INFO] Refreshing screenshot...
[[06:25:13]] [INFO] zrdO3PVkX3=pass
[[06:25:10]] [SUCCESS] Screenshot refreshed successfully
[[06:25:10]] [SUCCESS] Screenshot refreshed successfully
[[06:25:10]] [INFO] zrdO3PVkX3=running
[[06:25:10]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[06:25:09]] [SUCCESS] Screenshot refreshed
[[06:25:09]] [INFO] Refreshing screenshot...
[[06:25:09]] [INFO] F1olhgKhUt=pass
[[06:25:07]] [SUCCESS] Screenshot refreshed successfully
[[06:25:07]] [SUCCESS] Screenshot refreshed successfully
[[06:25:06]] [INFO] F1olhgKhUt=running
[[06:25:06]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[06:25:06]] [SUCCESS] Screenshot refreshed
[[06:25:06]] [INFO] Refreshing screenshot...
[[06:25:06]] [INFO] FnrbyHq7bU=pass
[[06:25:03]] [INFO] FnrbyHq7bU=running
[[06:25:03]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[06:25:03]] [INFO] nAB6Q8LAdv=fail
[[06:25:03]] [ERROR] Action 23 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[06:24:21]] [SUCCESS] Screenshot refreshed successfully
[[06:24:21]] [SUCCESS] Screenshot refreshed successfully
[[06:24:21]] [INFO] nAB6Q8LAdv=running
[[06:24:21]] [INFO] Executing action 23/27: Wait till xpath=//android.widget.Button[@text="Filter"]
[[06:24:21]] [SUCCESS] Screenshot refreshed
[[06:24:21]] [INFO] Refreshing screenshot...
[[06:24:21]] [INFO] JRheeTvpJf=pass
[[06:24:18]] [SUCCESS] Screenshot refreshed successfully
[[06:24:18]] [SUCCESS] Screenshot refreshed successfully
[[06:24:18]] [INFO] JRheeTvpJf=running
[[06:24:18]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[06:24:17]] [SUCCESS] Screenshot refreshed
[[06:24:17]] [INFO] Refreshing screenshot...
[[06:24:17]] [INFO] o1gHFWhXTL=pass
[[06:24:13]] [SUCCESS] Screenshot refreshed successfully
[[06:24:13]] [SUCCESS] Screenshot refreshed successfully
[[06:24:12]] [INFO] o1gHFWhXTL=running
[[06:24:12]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[06:24:12]] [SUCCESS] Screenshot refreshed
[[06:24:12]] [INFO] Refreshing screenshot...
[[06:24:12]] [INFO] cKNu2QoRC1=pass
[[06:24:08]] [SUCCESS] Screenshot refreshed successfully
[[06:24:08]] [SUCCESS] Screenshot refreshed successfully
[[06:24:07]] [INFO] cKNu2QoRC1=running
[[06:24:07]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[06:24:07]] [SUCCESS] Screenshot refreshed
[[06:24:07]] [INFO] Refreshing screenshot...
[[06:24:07]] [INFO] OyUowAaBzD=pass
[[06:24:04]] [SUCCESS] Screenshot refreshed successfully
[[06:24:04]] [SUCCESS] Screenshot refreshed successfully
[[06:24:03]] [INFO] OyUowAaBzD=running
[[06:24:03]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[06:24:03]] [SUCCESS] Screenshot refreshed
[[06:24:03]] [INFO] Refreshing screenshot...
[[06:24:03]] [INFO] Ob26qqcA0p=pass
[[06:23:57]] [SUCCESS] Screenshot refreshed successfully
[[06:23:57]] [SUCCESS] Screenshot refreshed successfully
[[06:23:56]] [INFO] Ob26qqcA0p=running
[[06:23:56]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[06:23:56]] [SUCCESS] Screenshot refreshed
[[06:23:56]] [INFO] Refreshing screenshot...
[[06:23:56]] [INFO] k3mu9Mt7Ec=pass
[[06:23:53]] [SUCCESS] Screenshot refreshed successfully
[[06:23:53]] [SUCCESS] Screenshot refreshed successfully
[[06:23:53]] [INFO] k3mu9Mt7Ec=running
[[06:23:53]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[06:23:53]] [SUCCESS] Screenshot refreshed
[[06:23:53]] [INFO] Refreshing screenshot...
[[06:23:53]] [INFO] FFM0CCo6Qg=pass
[[06:23:47]] [SUCCESS] Screenshot refreshed successfully
[[06:23:47]] [SUCCESS] Screenshot refreshed successfully
[[06:23:47]] [INFO] FFM0CCo6Qg=running
[[06:23:47]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[06:23:46]] [SUCCESS] Screenshot refreshed
[[06:23:46]] [INFO] Refreshing screenshot...
[[06:23:46]] [INFO] LWXWKZE4UV=pass
[[06:23:24]] [SUCCESS] Screenshot refreshed successfully
[[06:23:24]] [SUCCESS] Screenshot refreshed successfully
[[06:23:24]] [INFO] LWXWKZE4UV=running
[[06:23:24]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[06:23:23]] [SUCCESS] Screenshot refreshed
[[06:23:23]] [INFO] Refreshing screenshot...
[[06:23:23]] [INFO] Qbg9bipTGs=pass
[[06:23:02]] [SUCCESS] Screenshot refreshed successfully
[[06:23:02]] [SUCCESS] Screenshot refreshed successfully
[[06:23:02]] [INFO] Qbg9bipTGs=running
[[06:23:02]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[06:23:02]] [SUCCESS] Screenshot refreshed
[[06:23:02]] [INFO] Refreshing screenshot...
[[06:23:02]] [INFO] 7SpDO20tS2=pass
[[06:22:50]] [SUCCESS] Screenshot refreshed successfully
[[06:22:50]] [SUCCESS] Screenshot refreshed successfully
[[06:22:50]] [INFO] 7SpDO20tS2=running
[[06:22:50]] [INFO] Executing action 13/27: Wait for 10 ms
[[06:22:49]] [SUCCESS] Screenshot refreshed
[[06:22:49]] [INFO] Refreshing screenshot...
[[06:22:49]] [INFO] drbQBpgBfM=pass
[[06:22:46]] [SUCCESS] Screenshot refreshed successfully
[[06:22:46]] [SUCCESS] Screenshot refreshed successfully
[[06:22:46]] [INFO] drbQBpgBfM=running
[[06:22:46]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[06:22:45]] [SUCCESS] Screenshot refreshed
[[06:22:45]] [INFO] Refreshing screenshot...
[[06:22:45]] [INFO] F1olhgKhUt=pass
[[06:22:42]] [SUCCESS] Screenshot refreshed successfully
[[06:22:42]] [SUCCESS] Screenshot refreshed successfully
[[06:22:42]] [INFO] F1olhgKhUt=running
[[06:22:42]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[06:22:41]] [SUCCESS] Screenshot refreshed
[[06:22:41]] [INFO] Refreshing screenshot...
[[06:22:41]] [INFO] FnrbyHq7bU=pass
[[06:22:37]] [SUCCESS] Screenshot refreshed successfully
[[06:22:37]] [SUCCESS] Screenshot refreshed successfully
[[06:22:36]] [INFO] FnrbyHq7bU=running
[[06:22:36]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[06:22:36]] [SUCCESS] Screenshot refreshed
[[06:22:36]] [INFO] Refreshing screenshot...
[[06:22:36]] [INFO] nAB6Q8LAdv=pass
[[06:22:25]] [INFO] nAB6Q8LAdv=running
[[06:22:25]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[06:22:25]] [SUCCESS] Screenshot refreshed successfully
[[06:22:25]] [SUCCESS] Screenshot refreshed successfully
[[06:22:25]] [SUCCESS] Screenshot refreshed
[[06:22:25]] [INFO] Refreshing screenshot...
[[06:22:25]] [INFO] JRheeTvpJf=pass
[[06:22:23]] [SUCCESS] Screenshot refreshed successfully
[[06:22:23]] [SUCCESS] Screenshot refreshed successfully
[[06:22:23]] [INFO] JRheeTvpJf=running
[[06:22:23]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[06:22:23]] [SUCCESS] Screenshot refreshed
[[06:22:23]] [INFO] Refreshing screenshot...
[[06:22:23]] [INFO] o1gHFWhXTL=pass
[[06:22:18]] [SUCCESS] Screenshot refreshed successfully
[[06:22:18]] [SUCCESS] Screenshot refreshed successfully
[[06:22:17]] [INFO] o1gHFWhXTL=running
[[06:22:17]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[06:22:17]] [SUCCESS] Screenshot refreshed
[[06:22:17]] [INFO] Refreshing screenshot...
[[06:22:17]] [INFO] RLznb7o3ag=pass
[[06:22:13]] [SUCCESS] Screenshot refreshed successfully
[[06:22:13]] [SUCCESS] Screenshot refreshed successfully
[[06:22:12]] [INFO] RLznb7o3ag=running
[[06:22:12]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[06:22:12]] [SUCCESS] Screenshot refreshed
[[06:22:12]] [INFO] Refreshing screenshot...
[[06:22:12]] [SUCCESS] Screenshot refreshed
[[06:22:12]] [INFO] Refreshing screenshot...
[[06:22:08]] [SUCCESS] Screenshot refreshed successfully
[[06:22:08]] [SUCCESS] Screenshot refreshed successfully
[[06:22:08]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[06:22:08]] [SUCCESS] Screenshot refreshed
[[06:22:08]] [INFO] Refreshing screenshot...
[[06:22:05]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[06:22:05]] [SUCCESS] Screenshot refreshed successfully
[[06:22:05]] [SUCCESS] Screenshot refreshed successfully
[[06:22:05]] [SUCCESS] Screenshot refreshed
[[06:22:05]] [INFO] Refreshing screenshot...
[[06:22:01]] [SUCCESS] Screenshot refreshed successfully
[[06:22:01]] [SUCCESS] Screenshot refreshed successfully
[[06:22:01]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[06:22:01]] [SUCCESS] Screenshot refreshed
[[06:22:01]] [INFO] Refreshing screenshot...
[[06:21:44]] [SUCCESS] Screenshot refreshed successfully
[[06:21:44]] [SUCCESS] Screenshot refreshed successfully
[[06:21:44]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[06:21:44]] [SUCCESS] Screenshot refreshed
[[06:21:44]] [INFO] Refreshing screenshot...
[[06:21:42]] [SUCCESS] Screenshot refreshed successfully
[[06:21:42]] [SUCCESS] Screenshot refreshed successfully
[[06:21:42]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[06:21:42]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[06:21:42]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[06:21:42]] [INFO] g052Oo1Gcl=running
[[06:21:42]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[06:21:41]] [SUCCESS] Screenshot refreshed
[[06:21:41]] [INFO] Refreshing screenshot...
[[06:21:41]] [INFO] J9loj6Zs95K=pass
[[06:21:39]] [INFO] J9loj6Zs95K=running
[[06:21:39]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[06:21:39]] [SUCCESS] Screenshot refreshed successfully
[[06:21:39]] [SUCCESS] Screenshot refreshed successfully
[[06:21:39]] [SUCCESS] Screenshot refreshed
[[06:21:39]] [INFO] Refreshing screenshot...
[[06:21:39]] [INFO] Y8v5g7AJD1i=pass
[[06:21:31]] [INFO] Y8v5g7AJD1i=running
[[06:21:31]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[06:21:31]] [SUCCESS] Screenshot refreshed successfully
[[06:21:31]] [SUCCESS] Screenshot refreshed successfully
[[06:21:31]] [SUCCESS] Screenshot refreshed
[[06:21:31]] [INFO] Refreshing screenshot...
[[06:21:31]] [INFO] eqHB0Nj1He=pass
[[06:21:28]] [SUCCESS] Screenshot refreshed successfully
[[06:21:28]] [SUCCESS] Screenshot refreshed successfully
[[06:21:28]] [INFO] eqHB0Nj1He=running
[[06:21:28]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[06:21:27]] [SUCCESS] Screenshot refreshed
[[06:21:27]] [INFO] Refreshing screenshot...
[[06:21:27]] [INFO] H9fkkqcFbZ=pass
[[06:21:25]] [INFO] H9fkkqcFbZ=running
[[06:21:25]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[06:21:25]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[06:21:25]] [SUCCESS] Cleared 60 screenshots from database
[[06:21:25]] [INFO] Clearing screenshots from database before execution...
[[06:21:25]] [SUCCESS] All screenshots deleted successfully
[[06:21:25]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[06:21:25]] [INFO] Skipping report initialization - single test case execution
[[06:21:24]] [SUCCESS] All screenshots deleted successfully
[[06:21:24]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[06:21:24]] [SUCCESS] Added action: multiStep
[[06:21:24]] [SUCCESS] Added action: tapIfLocatorExists
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: waitTill
[[06:21:24]] [SUCCESS] Added action: text
[[06:21:24]] [SUCCESS] Added action: tapOnText
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: swipe
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: swipe
[[06:21:24]] [SUCCESS] Added action: wait
[[06:21:24]] [SUCCESS] Added action: tapIfLocatorExists
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: waitTill
[[06:21:24]] [SUCCESS] Added action: text
[[06:21:24]] [SUCCESS] Added action: tapOnText
[[06:21:24]] [SUCCESS] Added action: waitTill
[[06:21:24]] [SUCCESS] Added action: multiStep
[[06:21:24]] [SUCCESS] Added action: waitTill
[[06:21:24]] [SUCCESS] Added action: tap
[[06:21:24]] [SUCCESS] Added action: launchApp
[[06:21:24]] [SUCCESS] Added action: terminateApp
[[06:21:24]] [INFO] All actions cleared
[[06:21:24]] [INFO] Cleaning up screenshots...
[[06:21:24]] [WARNING] Device PJTCI7EMSSONYPU8 not found in available devices
[[06:21:11]] [SUCCESS] Screenshot refreshed successfully
[[06:21:10]] [SUCCESS] Screenshot refreshed
[[06:21:10]] [INFO] Refreshing screenshot...
[[06:21:09]] [SUCCESS] Connected to device: ************:33739
[[06:21:09]] [INFO] Device info updated: RMX2151
[[06:21:03]] [INFO] Connecting to device: ************:33739 (Platform: Android)...
[[06:20:59]] [SUCCESS] Found 1 device(s)
[[06:20:58]] [INFO] Refreshing device list...
