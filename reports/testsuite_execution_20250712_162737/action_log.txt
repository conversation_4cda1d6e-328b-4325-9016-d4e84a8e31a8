Action Log - 2025-07-12 16:41:44
================================================================================

[[16:41:44]] [INFO] Generating execution report...
[[16:41:44]] [SUCCESS] All tests passed successfully!
[[16:41:44]] [WARNING] Execution stopped by user.
[[16:41:44]] [ERROR] Error executing action 13: Failed to fetch
[[16:41:39]] [WARNING] Stop requested. Finishing current action...
[[16:40:25]] [SUCCESS] Screenshot refreshed successfully
[[16:40:25]] [SUCCESS] Screenshot refreshed successfully
[[16:40:25]] [INFO] GgQaBLWYkb=running
[[16:40:25]] [INFO] Executing action 13/57: Tap on element with xpath: //android.view.View[@text="Tracking ‌"]
[[16:40:25]] [SUCCESS] Screenshot refreshed
[[16:40:25]] [INFO] Refreshing screenshot...
[[16:40:25]] [INFO] Rl6s389Qsd=pass
[[16:40:18]] [SUCCESS] Screenshot refreshed successfully
[[16:40:18]] [SUCCESS] Screenshot refreshed successfully
[[16:40:18]] [INFO] Rl6s389Qsd=running
[[16:40:18]] [INFO] Executing action 12/57: Swipe from (50%, 70%) to (50%, 30%)
[[16:40:17]] [SUCCESS] Screenshot refreshed
[[16:40:17]] [INFO] Refreshing screenshot...
[[16:40:17]] [INFO] ADHRFCY0LX=pass
[[16:40:05]] [SUCCESS] Screenshot refreshed successfully
[[16:40:05]] [SUCCESS] Screenshot refreshed successfully
[[16:40:05]] [INFO] ADHRFCY0LX=running
[[16:40:05]] [INFO] Executing action 11/57: Wait for 10 ms
[[16:40:04]] [SUCCESS] Screenshot refreshed
[[16:40:04]] [INFO] Refreshing screenshot...
[[16:40:04]] [INFO] 7g6MFJSGIO=pass
[[16:39:54]] [SUCCESS] Screenshot refreshed successfully
[[16:39:54]] [SUCCESS] Screenshot refreshed successfully
[[16:39:53]] [INFO] 7g6MFJSGIO=running
[[16:39:53]] [INFO] Executing action 10/57: Tap on element with xpath: (//android.widget.TextView[@text="ID"]/following-sibling::android.view.View/android.view.View/android.widget.TextView)[1]
[[16:39:53]] [SUCCESS] Screenshot refreshed
[[16:39:53]] [INFO] Refreshing screenshot...
[[16:39:53]] [INFO] Z6g3sGuHTp=pass
[[16:39:46]] [SUCCESS] Screenshot refreshed successfully
[[16:39:46]] [SUCCESS] Screenshot refreshed successfully
[[16:39:46]] [INFO] Z6g3sGuHTp=running
[[16:39:46]] [INFO] Executing action 9/57: Wait for 5 ms
[[16:39:46]] [SUCCESS] Screenshot refreshed
[[16:39:46]] [INFO] Refreshing screenshot...
[[16:39:46]] [INFO] pFlYwTS53v=pass
[[16:39:42]] [SUCCESS] Screenshot refreshed successfully
[[16:39:42]] [SUCCESS] Screenshot refreshed successfully
[[16:39:40]] [INFO] pFlYwTS53v=running
[[16:39:40]] [INFO] Executing action 8/57: Tap on Text: "receipts"
[[16:39:39]] [SUCCESS] Screenshot refreshed
[[16:39:39]] [INFO] Refreshing screenshot...
[[16:39:39]] [INFO] V59u3l1wkM=pass
[[16:39:38]] [SUCCESS] Screenshot refreshed successfully
[[16:39:38]] [SUCCESS] Screenshot refreshed successfully
[[16:39:37]] [INFO] V59u3l1wkM=running
[[16:39:37]] [INFO] Executing action 7/57: Wait till xpath=//android.view.View[contains(@content-desc,"Manage your account")]
[[16:39:37]] [SUCCESS] Screenshot refreshed
[[16:39:37]] [INFO] Refreshing screenshot...
[[16:39:37]] [INFO] sl3Wk1gK8X=pass
[[16:39:33]] [SUCCESS] Screenshot refreshed successfully
[[16:39:33]] [SUCCESS] Screenshot refreshed successfully
[[16:39:30]] [INFO] sl3Wk1gK8X=running
[[16:39:30]] [INFO] Executing action 6/57: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:39:30]] [SUCCESS] Screenshot refreshed
[[16:39:30]] [INFO] Refreshing screenshot...
[[16:39:29]] [SUCCESS] Screenshot refreshed
[[16:39:29]] [INFO] Refreshing screenshot...
[[16:39:27]] [SUCCESS] Screenshot refreshed successfully
[[16:39:27]] [SUCCESS] Screenshot refreshed successfully
[[16:39:27]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[16:39:26]] [SUCCESS] Screenshot refreshed
[[16:39:26]] [INFO] Refreshing screenshot...
[[16:39:23]] [SUCCESS] Screenshot refreshed successfully
[[16:39:23]] [SUCCESS] Screenshot refreshed successfully
[[16:39:23]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:39:23]] [SUCCESS] Screenshot refreshed
[[16:39:23]] [INFO] Refreshing screenshot...
[[16:39:20]] [SUCCESS] Screenshot refreshed successfully
[[16:39:20]] [SUCCESS] Screenshot refreshed successfully
[[16:39:20]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[16:39:19]] [SUCCESS] Screenshot refreshed
[[16:39:19]] [INFO] Refreshing screenshot...
[[16:39:17]] [SUCCESS] Screenshot refreshed successfully
[[16:39:17]] [SUCCESS] Screenshot refreshed successfully
[[16:39:17]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:39:17]] [SUCCESS] Screenshot refreshed
[[16:39:17]] [INFO] Refreshing screenshot...
[[16:39:15]] [SUCCESS] Screenshot refreshed successfully
[[16:39:15]] [SUCCESS] Screenshot refreshed successfully
[[16:39:15]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:39:15]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[16:39:15]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:39:15]] [INFO] rrSllLCwLu=running
[[16:39:15]] [INFO] Executing action 5/57: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[16:39:14]] [SUCCESS] Screenshot refreshed
[[16:39:14]] [INFO] Refreshing screenshot...
[[16:39:14]] [INFO] xAPeBnVHrT=pass
[[16:39:11]] [SUCCESS] Screenshot refreshed successfully
[[16:39:11]] [SUCCESS] Screenshot refreshed successfully
[[16:39:11]] [INFO] xAPeBnVHrT=running
[[16:39:11]] [INFO] Executing action 4/57: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:39:11]] [SUCCESS] Screenshot refreshed
[[16:39:11]] [INFO] Refreshing screenshot...
[[16:39:11]] [INFO] u6bRYZZFAv=pass
[[16:39:04]] [SUCCESS] Screenshot refreshed successfully
[[16:39:04]] [SUCCESS] Screenshot refreshed successfully
[[16:39:03]] [INFO] u6bRYZZFAv=running
[[16:39:03]] [INFO] Executing action 3/57: Wait for 5 ms
[[16:39:03]] [SUCCESS] Screenshot refreshed
[[16:39:03]] [INFO] Refreshing screenshot...
[[16:39:03]] [INFO] pjFNt3w5Fr=pass
[[16:39:01]] [SUCCESS] Screenshot refreshed successfully
[[16:39:01]] [SUCCESS] Screenshot refreshed successfully
[[16:39:00]] [INFO] pjFNt3w5Fr=running
[[16:39:00]] [INFO] Executing action 2/57: Launch app: au.com.kmart
[[16:38:59]] [SUCCESS] Screenshot refreshed
[[16:38:59]] [INFO] Refreshing screenshot...
[[16:38:59]] [INFO] FK0xWTx8zz=pass
[[16:38:36]] [INFO] FK0xWTx8zz=running
[[16:38:36]] [INFO] Executing action 1/57: Terminate app: au.com.kmart
[[16:38:36]] [INFO] ExecutionManager: Starting execution of 57 actions...
[[16:38:36]] [SUCCESS] Cleared 1 screenshots from database
[[16:38:36]] [INFO] Clearing screenshots from database before execution...
[[16:38:36]] [SUCCESS] All screenshots deleted successfully
[[16:38:36]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:38:36]] [INFO] Skipping report initialization - single test case execution
[[16:38:10]] [SUCCESS] All screenshots deleted successfully
[[16:38:10]] [SUCCESS] Loaded test case "AU- MyAccount_Android" with 57 actions
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: swipe
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: exists
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: exists
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: text
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: swipe
[[16:38:10]] [SUCCESS] Added action: exists
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: text
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: waitTill
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: wait
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: exists
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: exists
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: wait
[[16:38:10]] [SUCCESS] Added action: swipeTillVisible
[[16:38:10]] [SUCCESS] Added action: androidFunctions
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: swipe
[[16:38:10]] [SUCCESS] Added action: wait
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: wait
[[16:38:10]] [SUCCESS] Added action: tapOnText
[[16:38:10]] [SUCCESS] Added action: waitTill
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: multiStep
[[16:38:10]] [SUCCESS] Added action: tap
[[16:38:10]] [SUCCESS] Added action: wait
[[16:38:10]] [SUCCESS] Added action: launchApp
[[16:38:10]] [SUCCESS] Added action: terminateApp
[[16:38:10]] [INFO] All actions cleared
[[16:38:10]] [INFO] Cleaning up screenshots...
[[16:38:07]] [SUCCESS] Screenshot refreshed successfully
[[16:38:07]] [SUCCESS] Screenshot refreshed
[[16:38:07]] [INFO] Refreshing screenshot...
[[16:38:06]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[16:38:06]] [INFO] Device info updated: RMX2151
[[16:38:05]] [SUCCESS] All screenshots deleted successfully
[[16:38:05]] [SUCCESS] Loaded test case "AU- MyAccount_Android" with 57 actions
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: swipe
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: exists
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: exists
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: text
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: swipe
[[16:38:05]] [SUCCESS] Added action: exists
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: text
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: waitTill
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: wait
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: exists
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: exists
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: wait
[[16:38:05]] [SUCCESS] Added action: swipeTillVisible
[[16:38:05]] [SUCCESS] Added action: androidFunctions
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: swipe
[[16:38:05]] [SUCCESS] Added action: wait
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: wait
[[16:38:05]] [SUCCESS] Added action: tapOnText
[[16:38:05]] [SUCCESS] Added action: waitTill
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: multiStep
[[16:38:05]] [SUCCESS] Added action: tap
[[16:38:05]] [SUCCESS] Added action: wait
[[16:38:05]] [SUCCESS] Added action: launchApp
[[16:38:05]] [SUCCESS] Added action: terminateApp
[[16:38:05]] [INFO] All actions cleared
[[16:38:05]] [INFO] Cleaning up screenshots...
[[16:38:05]] [INFO] Selected device: RMX2151 (PJTCI7EMSSONYPU8)
[[16:38:01]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[16:37:59]] [SUCCESS] Found 1 device(s)
[[16:37:58]] [INFO] Refreshing device list...
