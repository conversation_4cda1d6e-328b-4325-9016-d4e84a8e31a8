Action Log - 2025-07-12 14:33:16
================================================================================

[[14:33:16]] [INFO] Generating execution report...
[[14:33:16]] [SUCCESS] All tests passed successfully!
[[14:33:16]] [INFO] xyHVihJMBi=fail
[[14:33:16]] [ERROR] Action 52 failed: Error tapping element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[14:32:49]] [INFO] xyHVihJMBi=running
[[14:32:49]] [INFO] Executing action 52/52: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[14:32:48]] [SUCCESS] Screenshot refreshed
[[14:32:48]] [INFO] Refreshing screenshot...
[[14:32:48]] [INFO] mWeLQtXiL6=pass
[[14:32:43]] [SUCCESS] Screenshot refreshed successfully
[[14:32:43]] [SUCCESS] Screenshot refreshed successfully
[[14:32:43]] [INFO] mWeLQtXiL6=running
[[14:32:43]] [INFO] Executing action 51/52: Swipe from (50%, 70%) to (50%, 30%)
[[14:32:42]] [SUCCESS] Screenshot refreshed
[[14:32:42]] [INFO] Refreshing screenshot...
[[14:32:42]] [INFO] rkwVoJGZG4=pass
[[14:32:41]] [SUCCESS] Screenshot refreshed successfully
[[14:32:41]] [SUCCESS] Screenshot refreshed successfully
[[14:32:40]] [INFO] rkwVoJGZG4=running
[[14:32:40]] [INFO] Executing action 50/52: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[14:32:40]] [SUCCESS] Screenshot refreshed
[[14:32:40]] [INFO] Refreshing screenshot...
[[14:32:40]] [INFO] 0f2FSZYjWq=pass
[[14:32:34]] [SUCCESS] Screenshot refreshed successfully
[[14:32:34]] [SUCCESS] Screenshot refreshed successfully
[[14:32:33]] [INFO] 0f2FSZYjWq=running
[[14:32:33]] [INFO] Executing action 49/52: Check if element with text="3000" exists
[[14:32:33]] [SUCCESS] Screenshot refreshed
[[14:32:33]] [INFO] Refreshing screenshot...
[[14:32:33]] [INFO] Tebej51pT2=pass
[[14:32:31]] [SUCCESS] Screenshot refreshed successfully
[[14:32:31]] [SUCCESS] Screenshot refreshed successfully
[[14:32:30]] [INFO] Tebej51pT2=running
[[14:32:30]] [INFO] Executing action 48/52: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[14:32:30]] [SUCCESS] Screenshot refreshed
[[14:32:30]] [INFO] Refreshing screenshot...
[[14:32:30]] [INFO] JrPVGdts3J=pass
[[14:32:14]] [SUCCESS] Screenshot refreshed successfully
[[14:32:14]] [SUCCESS] Screenshot refreshed successfully
[[14:32:14]] [INFO] JrPVGdts3J=running
[[14:32:14]] [INFO] Executing action 47/52: Tap on image: bag-remove-btn-android.png
[[14:32:13]] [SUCCESS] Screenshot refreshed
[[14:32:13]] [INFO] Refreshing screenshot...
[[14:32:13]] [INFO] mHyK7BTEWp=pass
[[14:32:09]] [SUCCESS] Screenshot refreshed successfully
[[14:32:09]] [SUCCESS] Screenshot refreshed successfully
[[14:32:08]] [INFO] mHyK7BTEWp=running
[[14:32:08]] [INFO] Executing action 46/52: Wait for 3 ms
[[14:32:08]] [SUCCESS] Screenshot refreshed
[[14:32:08]] [INFO] Refreshing screenshot...
[[14:32:08]] [INFO] s8h8VDUIOC=pass
[[14:32:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:01]] [INFO] s8h8VDUIOC=running
[[14:32:01]] [INFO] Executing action 45/52: Swipe from (50%, 70%) to (50%, 50%)
[[14:32:00]] [SUCCESS] Screenshot refreshed
[[14:32:00]] [INFO] Refreshing screenshot...
[[14:32:00]] [INFO] GYK47u1y3A=pass
[[14:31:53]] [SUCCESS] Screenshot refreshed successfully
[[14:31:53]] [SUCCESS] Screenshot refreshed successfully
[[14:31:53]] [INFO] GYK47u1y3A=running
[[14:31:53]] [INFO] Executing action 44/52: Android Function: send_key_event - Key Event: TAB
[[14:31:53]] [SUCCESS] Screenshot refreshed
[[14:31:53]] [INFO] Refreshing screenshot...
[[14:31:53]] [INFO] ZWpYNcpbFA=pass
[[14:31:48]] [SUCCESS] Screenshot refreshed successfully
[[14:31:48]] [SUCCESS] Screenshot refreshed successfully
[[14:31:48]] [INFO] ZWpYNcpbFA=running
[[14:31:48]] [INFO] Executing action 43/52: Tap on Text: "VIC"
[[14:31:47]] [SUCCESS] Screenshot refreshed
[[14:31:47]] [INFO] Refreshing screenshot...
[[14:31:47]] [INFO] QpBLC6BStn=pass
[[14:31:45]] [SUCCESS] Screenshot refreshed successfully
[[14:31:45]] [SUCCESS] Screenshot refreshed successfully
[[14:31:44]] [INFO] QpBLC6BStn=running
[[14:31:44]] [INFO] Executing action 42/52: textClear action
[[14:31:44]] [SUCCESS] Screenshot refreshed
[[14:31:44]] [INFO] Refreshing screenshot...
[[14:31:44]] [INFO] G4A3KBlXHq=pass
[[14:31:24]] [INFO] G4A3KBlXHq=running
[[14:31:24]] [INFO] Executing action 41/52: Tap on Text: "Nearby"
[[14:31:24]] [INFO] 3gJsiap2Ds=fail
[[14:31:24]] [ERROR] Action 40 failed: Text 'Collect' not found within timeout (30s)
[[14:30:43]] [INFO] 3gJsiap2Ds=running
[[14:30:43]] [INFO] Executing action 40/52: Tap on Text: "Collect"
[[14:30:43]] [INFO] qofJDqXBME=fail
[[14:30:43]] [ERROR] Action 39 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[14:30:11]] [SUCCESS] Screenshot refreshed successfully
[[14:30:11]] [SUCCESS] Screenshot refreshed successfully
[[14:30:11]] [INFO] qofJDqXBME=running
[[14:30:11]] [INFO] Executing action 39/52: Wait till text appears: "Delivery"
[[14:30:10]] [SUCCESS] Screenshot refreshed
[[14:30:10]] [INFO] Refreshing screenshot...
[[14:30:10]] [INFO] rkwVoJGZG4=pass
[[14:30:08]] [SUCCESS] Screenshot refreshed successfully
[[14:30:08]] [SUCCESS] Screenshot refreshed successfully
[[14:30:07]] [INFO] rkwVoJGZG4=running
[[14:30:07]] [INFO] Executing action 38/52: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[14:30:07]] [SUCCESS] Screenshot refreshed
[[14:30:07]] [INFO] Refreshing screenshot...
[[14:30:07]] [INFO] 94ikwhIEE2=pass
[[14:30:00]] [SUCCESS] Screenshot refreshed successfully
[[14:30:00]] [SUCCESS] Screenshot refreshed successfully
[[14:30:00]] [INFO] 94ikwhIEE2=running
[[14:30:00]] [INFO] Executing action 37/52: Tap on Text: "bag"
[[14:29:59]] [SUCCESS] Screenshot refreshed
[[14:29:59]] [INFO] Refreshing screenshot...
[[14:29:59]] [INFO] DfwaiVZ8Z9=pass
[[14:29:56]] [SUCCESS] Screenshot refreshed successfully
[[14:29:56]] [SUCCESS] Screenshot refreshed successfully
[[14:29:56]] [INFO] DfwaiVZ8Z9=running
[[14:29:56]] [INFO] Executing action 36/52: Swipe from (50%, 70%) to (50%, 50%)
[[14:29:56]] [SUCCESS] Screenshot refreshed
[[14:29:56]] [INFO] Refreshing screenshot...
[[14:29:56]] [INFO] eRCmRhc3re=pass
[[14:29:53]] [SUCCESS] Screenshot refreshed successfully
[[14:29:53]] [SUCCESS] Screenshot refreshed successfully
[[14:29:53]] [INFO] eRCmRhc3re=running
[[14:29:53]] [INFO] Executing action 35/52: Check if element with text="Broadway" exists
[[14:29:52]] [SUCCESS] Screenshot refreshed
[[14:29:52]] [INFO] Refreshing screenshot...
[[14:29:52]] [INFO] E2jpN7BioW=pass
[[14:29:21]] [SUCCESS] Screenshot refreshed successfully
[[14:29:21]] [SUCCESS] Screenshot refreshed successfully
[[14:29:21]] [INFO] E2jpN7BioW=running
[[14:29:21]] [INFO] Executing action 34/52: Tap on element with accessibility_id: btnSaveOrContinue
[[14:29:20]] [SUCCESS] Screenshot refreshed
[[14:29:20]] [INFO] Refreshing screenshot...
[[14:29:20]] [INFO] kDnmoQJG4o=pass
[[14:29:18]] [SUCCESS] Screenshot refreshed successfully
[[14:29:18]] [SUCCESS] Screenshot refreshed successfully
[[14:29:18]] [INFO] kDnmoQJG4o=running
[[14:29:18]] [INFO] Executing action 33/52: Wait till accessibility_id=btnSaveOrContinue
[[14:29:18]] [SUCCESS] Screenshot refreshed
[[14:29:18]] [INFO] Refreshing screenshot...
[[14:29:18]] [INFO] H0ODFz7sWJ=pass
[[14:29:12]] [SUCCESS] Screenshot refreshed successfully
[[14:29:12]] [SUCCESS] Screenshot refreshed successfully
[[14:29:12]] [INFO] H0ODFz7sWJ=running
[[14:29:12]] [INFO] Executing action 32/52: Tap on Text: "2000"
[[14:29:11]] [SUCCESS] Screenshot refreshed
[[14:29:11]] [INFO] Refreshing screenshot...
[[14:29:11]] [INFO] pldheRUBVi=pass
[[14:29:10]] [SUCCESS] Screenshot refreshed successfully
[[14:29:10]] [SUCCESS] Screenshot refreshed successfully
[[14:29:09]] [INFO] pldheRUBVi=running
[[14:29:09]] [INFO] Executing action 31/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:29:09]] [SUCCESS] Screenshot refreshed
[[14:29:09]] [INFO] Refreshing screenshot...
[[14:29:09]] [INFO] uZHvvAzVfx=pass
[[14:29:06]] [SUCCESS] Screenshot refreshed successfully
[[14:29:06]] [SUCCESS] Screenshot refreshed successfully
[[14:29:06]] [INFO] uZHvvAzVfx=running
[[14:29:06]] [INFO] Executing action 30/52: textClear action
[[14:29:06]] [SUCCESS] Screenshot refreshed
[[14:29:06]] [INFO] Refreshing screenshot...
[[14:29:06]] [INFO] pldheRUBVi=pass
[[14:29:03]] [SUCCESS] Screenshot refreshed successfully
[[14:29:03]] [SUCCESS] Screenshot refreshed successfully
[[14:29:03]] [INFO] pldheRUBVi=running
[[14:29:03]] [INFO] Executing action 29/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:29:03]] [SUCCESS] Screenshot refreshed
[[14:29:03]] [INFO] Refreshing screenshot...
[[14:29:03]] [INFO] pldheRUBVi=pass
[[14:29:01]] [SUCCESS] Screenshot refreshed successfully
[[14:29:01]] [SUCCESS] Screenshot refreshed successfully
[[14:29:01]] [INFO] pldheRUBVi=running
[[14:29:01]] [INFO] Executing action 28/52: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:29:00]] [SUCCESS] Screenshot refreshed
[[14:29:00]] [INFO] Refreshing screenshot...
[[14:29:00]] [INFO] WmNWcsWVHv=pass
[[14:28:56]] [SUCCESS] Screenshot refreshed successfully
[[14:28:56]] [SUCCESS] Screenshot refreshed successfully
[[14:28:53]] [INFO] WmNWcsWVHv=running
[[14:28:53]] [INFO] Executing action 27/52: Tap on Text: "4000"
[[14:28:52]] [SUCCESS] Screenshot refreshed
[[14:28:52]] [INFO] Refreshing screenshot...
[[14:28:52]] [INFO] lnjoz8hHUU=pass
[[14:28:22]] [SUCCESS] Screenshot refreshed successfully
[[14:28:22]] [SUCCESS] Screenshot refreshed successfully
[[14:28:22]] [INFO] lnjoz8hHUU=running
[[14:28:22]] [INFO] Executing action 26/52: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[14:28:21]] [SUCCESS] Screenshot refreshed
[[14:28:21]] [INFO] Refreshing screenshot...
[[14:28:21]] [INFO] VkUKQbf1Qt=pass
[[14:28:16]] [SUCCESS] Screenshot refreshed successfully
[[14:28:16]] [SUCCESS] Screenshot refreshed successfully
[[14:28:16]] [INFO] VkUKQbf1Qt=running
[[14:28:16]] [INFO] Executing action 25/52: Tap on Text: "UNO"
[[14:28:15]] [SUCCESS] Screenshot refreshed
[[14:28:15]] [INFO] Refreshing screenshot...
[[14:28:15]] [INFO] 73NABkfWyY=pass
[[14:28:10]] [SUCCESS] Screenshot refreshed successfully
[[14:28:10]] [SUCCESS] Screenshot refreshed successfully
[[14:28:10]] [INFO] 73NABkfWyY=running
[[14:28:10]] [INFO] Executing action 24/52: Check if element with text="Toowong" exists
[[14:28:09]] [SUCCESS] Screenshot refreshed
[[14:28:09]] [INFO] Refreshing screenshot...
[[14:28:09]] [INFO] E2jpN7BioW=pass
[[14:28:07]] [SUCCESS] Screenshot refreshed successfully
[[14:28:07]] [SUCCESS] Screenshot refreshed successfully
[[14:28:07]] [INFO] E2jpN7BioW=running
[[14:28:07]] [INFO] Executing action 23/52: Tap on element with accessibility_id: btnSaveOrContinue
[[14:28:07]] [SUCCESS] Screenshot refreshed
[[14:28:07]] [INFO] Refreshing screenshot...
[[14:28:07]] [INFO] kDnmoQJG4o=pass
[[14:28:05]] [SUCCESS] Screenshot refreshed successfully
[[14:28:05]] [SUCCESS] Screenshot refreshed successfully
[[14:28:05]] [INFO] kDnmoQJG4o=running
[[14:28:05]] [INFO] Executing action 22/52: Wait till accessibility_id=btnSaveOrContinue
[[14:28:04]] [SUCCESS] Screenshot refreshed
[[14:28:04]] [INFO] Refreshing screenshot...
[[14:28:04]] [INFO] VkUKQbf1Qt=pass
[[14:28:01]] [SUCCESS] Screenshot refreshed successfully
[[14:28:01]] [SUCCESS] Screenshot refreshed successfully
[[14:28:00]] [INFO] VkUKQbf1Qt=running
[[14:28:00]] [INFO] Executing action 21/52: Tap on Text: "CITY"
[[14:28:00]] [SUCCESS] Screenshot refreshed
[[14:28:00]] [INFO] Refreshing screenshot...
[[14:28:00]] [INFO] pldheRUBVi=pass
[[14:27:57]] [SUCCESS] Screenshot refreshed successfully
[[14:27:57]] [SUCCESS] Screenshot refreshed successfully
[[14:27:57]] [INFO] pldheRUBVi=running
[[14:27:57]] [INFO] Executing action 20/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:27:56]] [SUCCESS] Screenshot refreshed
[[14:27:56]] [INFO] Refreshing screenshot...
[[14:27:56]] [INFO] kbdEPCPYod=pass
[[14:27:54]] [SUCCESS] Screenshot refreshed successfully
[[14:27:54]] [SUCCESS] Screenshot refreshed successfully
[[14:27:53]] [INFO] kbdEPCPYod=running
[[14:27:53]] [INFO] Executing action 19/52: textClear action
[[14:27:53]] [SUCCESS] Screenshot refreshed
[[14:27:53]] [INFO] Refreshing screenshot...
[[14:27:53]] [INFO] pldheRUBVi=pass
[[14:27:30]] [SUCCESS] Screenshot refreshed successfully
[[14:27:30]] [SUCCESS] Screenshot refreshed successfully
[[14:27:30]] [INFO] pldheRUBVi=running
[[14:27:30]] [INFO] Executing action 18/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:27:30]] [SUCCESS] Screenshot refreshed
[[14:27:30]] [INFO] Refreshing screenshot...
[[14:27:30]] [INFO] YhLhTn3Wtm=pass
[[14:27:23]] [SUCCESS] Screenshot refreshed successfully
[[14:27:23]] [SUCCESS] Screenshot refreshed successfully
[[14:27:23]] [INFO] YhLhTn3Wtm=running
[[14:27:23]] [INFO] Executing action 17/52: Wait for 5 ms
[[14:27:22]] [SUCCESS] Screenshot refreshed
[[14:27:22]] [INFO] Refreshing screenshot...
[[14:27:22]] [INFO] VkUKQbf1Qt=pass
[[14:27:17]] [SUCCESS] Screenshot refreshed successfully
[[14:27:17]] [SUCCESS] Screenshot refreshed successfully
[[14:27:16]] [INFO] VkUKQbf1Qt=running
[[14:27:16]] [INFO] Executing action 16/52: Tap on Text: "Edit"
[[14:27:16]] [SUCCESS] Screenshot refreshed
[[14:27:16]] [INFO] Refreshing screenshot...
[[14:27:16]] [INFO] MpdUKUazHa=pass
[[14:27:12]] [SUCCESS] Screenshot refreshed successfully
[[14:27:12]] [SUCCESS] Screenshot refreshed successfully
[[14:27:12]] [INFO] MpdUKUazHa=running
[[14:27:12]] [INFO] Executing action 15/52: Wait till image appears: sort-by-relevance-android.png
[[14:27:11]] [SUCCESS] Screenshot refreshed
[[14:27:11]] [INFO] Refreshing screenshot...
[[14:27:11]] [INFO] IupxLP2Jsr=pass
[[14:27:10]] [SUCCESS] Screenshot refreshed successfully
[[14:27:10]] [SUCCESS] Screenshot refreshed successfully
[[14:27:09]] [INFO] IupxLP2Jsr=running
[[14:27:09]] [INFO] Executing action 14/52: Input text: "P_6225544"
[[14:27:08]] [SUCCESS] Screenshot refreshed
[[14:27:08]] [INFO] Refreshing screenshot...
[[14:27:08]] [INFO] 70iOOakiG7=pass
[[14:27:04]] [SUCCESS] Screenshot refreshed successfully
[[14:27:04]] [SUCCESS] Screenshot refreshed successfully
[[14:27:00]] [INFO] 70iOOakiG7=running
[[14:27:00]] [INFO] Executing action 13/52: Tap on Text: "Find"
[[14:27:00]] [SUCCESS] Screenshot refreshed
[[14:27:00]] [INFO] Refreshing screenshot...
[[14:27:00]] [INFO] E2jpN7BioW=pass
[[14:26:57]] [SUCCESS] Screenshot refreshed successfully
[[14:26:57]] [SUCCESS] Screenshot refreshed successfully
[[14:26:57]] [INFO] E2jpN7BioW=running
[[14:26:57]] [INFO] Executing action 12/52: Tap on element with accessibility_id: btnSaveOrContinue
[[14:26:56]] [SUCCESS] Screenshot refreshed
[[14:26:56]] [INFO] Refreshing screenshot...
[[14:26:56]] [INFO] kDnmoQJG4o=pass
[[14:26:55]] [SUCCESS] Screenshot refreshed successfully
[[14:26:55]] [SUCCESS] Screenshot refreshed successfully
[[14:26:54]] [INFO] kDnmoQJG4o=running
[[14:26:54]] [INFO] Executing action 11/52: Wait till accessibility_id=btnSaveOrContinue
[[14:26:54]] [SUCCESS] Screenshot refreshed
[[14:26:54]] [INFO] Refreshing screenshot...
[[14:26:54]] [INFO] mw9GQ4mzRE=pass
[[14:26:10]] [SUCCESS] Screenshot refreshed successfully
[[14:26:10]] [SUCCESS] Screenshot refreshed successfully
[[14:26:09]] [INFO] mw9GQ4mzRE=running
[[14:26:09]] [INFO] Executing action 10/52: Tap on Text: "BC"
[[14:26:09]] [SUCCESS] Screenshot refreshed
[[14:26:09]] [INFO] Refreshing screenshot...
[[14:26:09]] [INFO] pldheRUBVi=pass
[[14:26:07]] [SUCCESS] Screenshot refreshed successfully
[[14:26:07]] [SUCCESS] Screenshot refreshed successfully
[[14:26:07]] [INFO] pldheRUBVi=running
[[14:26:07]] [INFO] Executing action 9/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:26:06]] [SUCCESS] Screenshot refreshed
[[14:26:06]] [INFO] Refreshing screenshot...
[[14:26:06]] [INFO] kbdEPCPYod=pass
[[14:26:03]] [SUCCESS] Screenshot refreshed successfully
[[14:26:03]] [SUCCESS] Screenshot refreshed successfully
[[14:26:03]] [INFO] kbdEPCPYod=running
[[14:26:03]] [INFO] Executing action 8/52: textClear action
[[14:26:02]] [SUCCESS] Screenshot refreshed
[[14:26:02]] [INFO] Refreshing screenshot...
[[14:26:02]] [INFO] pldheRUBVi=pass
[[14:26:00]] [SUCCESS] Screenshot refreshed successfully
[[14:26:00]] [SUCCESS] Screenshot refreshed successfully
[[14:26:00]] [INFO] pldheRUBVi=running
[[14:26:00]] [INFO] Executing action 7/52: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[14:25:59]] [SUCCESS] Screenshot refreshed
[[14:25:59]] [INFO] Refreshing screenshot...
[[14:25:59]] [INFO] QMXBlswP6H=pass
[[14:25:53]] [SUCCESS] Screenshot refreshed successfully
[[14:25:53]] [SUCCESS] Screenshot refreshed successfully
[[14:25:52]] [INFO] QMXBlswP6H=running
[[14:25:52]] [INFO] Executing action 6/52: Tap on Text: "Edit"
[[14:25:52]] [SUCCESS] Screenshot refreshed
[[14:25:52]] [INFO] Refreshing screenshot...
[[14:25:52]] [INFO] RLz6vQo3ag=pass
[[14:25:50]] [SUCCESS] Screenshot refreshed successfully
[[14:25:50]] [SUCCESS] Screenshot refreshed successfully
[[14:25:39]] [INFO] RLz6vQo3ag=running
[[14:25:39]] [INFO] Executing action 5/52: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[14:25:39]] [SUCCESS] Screenshot refreshed
[[14:25:39]] [INFO] Refreshing screenshot...
[[14:25:39]] [SUCCESS] Screenshot refreshed
[[14:25:39]] [INFO] Refreshing screenshot...
[[14:25:37]] [SUCCESS] Screenshot refreshed successfully
[[14:25:37]] [SUCCESS] Screenshot refreshed successfully
[[14:25:37]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[14:25:36]] [SUCCESS] Screenshot refreshed
[[14:25:36]] [INFO] Refreshing screenshot...
[[14:25:34]] [SUCCESS] Screenshot refreshed successfully
[[14:25:34]] [SUCCESS] Screenshot refreshed successfully
[[14:25:34]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[14:25:34]] [SUCCESS] Screenshot refreshed
[[14:25:34]] [INFO] Refreshing screenshot...
[[14:25:30]] [SUCCESS] Screenshot refreshed successfully
[[14:25:30]] [SUCCESS] Screenshot refreshed successfully
[[14:25:30]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[14:25:29]] [SUCCESS] Screenshot refreshed
[[14:25:29]] [INFO] Refreshing screenshot...
[[14:25:27]] [SUCCESS] Screenshot refreshed successfully
[[14:25:27]] [SUCCESS] Screenshot refreshed successfully
[[14:25:27]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[14:25:26]] [SUCCESS] Screenshot refreshed
[[14:25:26]] [INFO] Refreshing screenshot...
[[14:25:24]] [SUCCESS] Screenshot refreshed successfully
[[14:25:24]] [SUCCESS] Screenshot refreshed successfully
[[14:25:24]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[14:25:24]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[14:25:24]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[14:25:24]] [INFO] xz8njynjpZ=running
[[14:25:24]] [INFO] Executing action 4/52: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[14:25:24]] [SUCCESS] Screenshot refreshed
[[14:25:24]] [INFO] Refreshing screenshot...
[[14:25:24]] [INFO] J9loj6Zl5K=pass
[[14:25:22]] [SUCCESS] Screenshot refreshed successfully
[[14:25:22]] [SUCCESS] Screenshot refreshed successfully
[[14:25:21]] [INFO] J9loj6Zl5K=running
[[14:25:21]] [INFO] Executing action 3/52: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[14:25:21]] [SUCCESS] Screenshot refreshed
[[14:25:21]] [INFO] Refreshing screenshot...
[[14:25:21]] [INFO] Y8vz7AJD1i=pass
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:15]] [INFO] Y8vz7AJD1i=running
[[14:25:15]] [INFO] Executing action 2/52: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:25:14]] [SUCCESS] Screenshot refreshed
[[14:25:14]] [INFO] Refreshing screenshot...
[[14:25:14]] [INFO] H9fy9qcFbZ=pass
[[14:25:11]] [INFO] H9fy9qcFbZ=running
[[14:25:11]] [INFO] Executing action 1/52: Launch app: au.com.kmart
[[14:25:11]] [INFO] ExecutionManager: Starting execution of 52 actions...
[[14:25:11]] [SUCCESS] Cleared 1 screenshots from database
[[14:25:11]] [INFO] Clearing screenshots from database before execution...
[[14:25:11]] [SUCCESS] All screenshots deleted successfully
[[14:25:11]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:25:11]] [INFO] Skipping report initialization - single test case execution
[[14:25:01]] [SUCCESS] All screenshots deleted successfully
[[14:25:01]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 52 actions
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: swipe
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: exists
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: wait
[[14:25:01]] [SUCCESS] Added action: swipe
[[14:25:01]] [SUCCESS] Added action: androidFunctions
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: textClear
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: swipe
[[14:25:01]] [SUCCESS] Added action: exists
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: textClear
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: exists
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: textClear
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: wait
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: text
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: textClear
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: tapOnText
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: multiStep
[[14:25:01]] [SUCCESS] Added action: waitTill
[[14:25:01]] [SUCCESS] Added action: tap
[[14:25:01]] [SUCCESS] Added action: launchApp
[[14:25:01]] [INFO] All actions cleared
[[14:25:01]] [INFO] Cleaning up screenshots...
[[14:24:47]] [SUCCESS] Screenshot refreshed successfully
[[14:24:45]] [SUCCESS] Screenshot refreshed
[[14:24:45]] [INFO] Refreshing screenshot...
[[14:24:44]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[14:24:44]] [INFO] Device info updated: RMX2151
[[14:24:38]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[14:24:37]] [SUCCESS] Found 1 device(s)
[[14:24:34]] [INFO] Refreshing device list...
