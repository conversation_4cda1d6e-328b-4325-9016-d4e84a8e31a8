Action Log - 2025-07-12 20:25:00
================================================================================

[[20:25:00]] [INFO] Generating execution report...
[[20:25:00]] [SUCCESS] All tests passed successfully!
[[20:25:00]] [INFO] Moving to the next test case after failure (server will handle retry)
[[20:25:00]] [ERROR] Multi Step action step 3 failed: Element not visible after 2 swipe(s)
[[20:22:47]] [SUCCESS] Screenshot refreshed successfully
[[20:22:47]] [SUCCESS] Screenshot refreshed successfully
[[20:22:47]] [INFO] Executing Multi Step action step 3/48: Swipe up till element xpath: "//android.widget.Button[@text="Continue to details"]" is visible
[[20:22:47]] [SUCCESS] Screenshot refreshed
[[20:22:47]] [INFO] Refreshing screenshot...
[[20:22:45]] [SUCCESS] Screenshot refreshed successfully
[[20:22:45]] [SUCCESS] Screenshot refreshed successfully
[[20:22:44]] [INFO] Executing Multi Step action step 2/48: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[20:22:43]] [SUCCESS] Screenshot refreshed
[[20:22:43]] [INFO] Refreshing screenshot...
[[20:21:57]] [SUCCESS] Screenshot refreshed successfully
[[20:21:57]] [SUCCESS] Screenshot refreshed successfully
[[20:21:57]] [INFO] Executing Multi Step action step 1/48: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[20:21:57]] [INFO] Loaded 48 steps from test case: Delivery Buy Steps_AU-ANDROID
[[20:21:57]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[20:21:57]] [INFO] 24xQv87uTq=running
[[20:21:57]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (47 steps)
[[20:21:57]] [SUCCESS] Screenshot refreshed
[[20:21:57]] [INFO] Refreshing screenshot...
[[20:21:57]] [INFO] zrdO3PVkX3=pass
[[20:21:54]] [SUCCESS] Screenshot refreshed successfully
[[20:21:54]] [SUCCESS] Screenshot refreshed successfully
[[20:21:54]] [INFO] zrdO3PVkX3=running
[[20:21:54]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[20:21:53]] [SUCCESS] Screenshot refreshed
[[20:21:53]] [INFO] Refreshing screenshot...
[[20:21:53]] [INFO] F1olhgKhUt=pass
[[20:21:50]] [SUCCESS] Screenshot refreshed successfully
[[20:21:50]] [SUCCESS] Screenshot refreshed successfully
[[20:21:50]] [INFO] F1olhgKhUt=running
[[20:21:50]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[20:21:49]] [SUCCESS] Screenshot refreshed
[[20:21:49]] [INFO] Refreshing screenshot...
[[20:21:49]] [INFO] FnrbyHq7bU=pass
[[20:21:46]] [SUCCESS] Screenshot refreshed successfully
[[20:21:46]] [SUCCESS] Screenshot refreshed successfully
[[20:21:45]] [INFO] FnrbyHq7bU=running
[[20:21:45]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[20:21:44]] [SUCCESS] Screenshot refreshed
[[20:21:44]] [INFO] Refreshing screenshot...
[[20:21:44]] [INFO] nAB6Q8LAdv=pass
[[20:20:56]] [INFO] nAB6Q8LAdv=running
[[20:20:56]] [INFO] Executing action 23/27: Wait till xpath=//android.widget.Button[@text="Filter"]
[[20:20:56]] [SUCCESS] Screenshot refreshed successfully
[[20:20:56]] [SUCCESS] Screenshot refreshed successfully
[[20:20:55]] [SUCCESS] Screenshot refreshed
[[20:20:55]] [INFO] Refreshing screenshot...
[[20:20:55]] [INFO] JRheeTvpJf=pass
[[20:20:53]] [SUCCESS] Screenshot refreshed successfully
[[20:20:53]] [SUCCESS] Screenshot refreshed successfully
[[20:20:53]] [INFO] JRheeTvpJf=running
[[20:20:53]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[20:20:52]] [SUCCESS] Screenshot refreshed
[[20:20:52]] [INFO] Refreshing screenshot...
[[20:20:52]] [INFO] o1gHFWhXTL=pass
[[20:20:48]] [SUCCESS] Screenshot refreshed successfully
[[20:20:48]] [SUCCESS] Screenshot refreshed successfully
[[20:20:48]] [INFO] o1gHFWhXTL=running
[[20:20:48]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[20:20:47]] [SUCCESS] Screenshot refreshed
[[20:20:47]] [INFO] Refreshing screenshot...
[[20:20:47]] [INFO] cKNu2QoRC1=pass
[[20:20:45]] [SUCCESS] Screenshot refreshed successfully
[[20:20:45]] [SUCCESS] Screenshot refreshed successfully
[[20:20:44]] [INFO] cKNu2QoRC1=running
[[20:20:44]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[20:20:44]] [SUCCESS] Screenshot refreshed
[[20:20:44]] [INFO] Refreshing screenshot...
[[20:20:44]] [INFO] OyUowAaBzD=pass
[[20:20:07]] [SUCCESS] Screenshot refreshed successfully
[[20:20:07]] [SUCCESS] Screenshot refreshed successfully
[[20:20:07]] [INFO] OyUowAaBzD=running
[[20:20:07]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[20:20:06]] [SUCCESS] Screenshot refreshed
[[20:20:06]] [INFO] Refreshing screenshot...
[[20:20:06]] [INFO] Ob26qqcA0p=pass
[[20:20:01]] [SUCCESS] Screenshot refreshed successfully
[[20:20:01]] [SUCCESS] Screenshot refreshed successfully
[[20:20:01]] [INFO] Ob26qqcA0p=running
[[20:20:01]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[20:20:00]] [SUCCESS] Screenshot refreshed
[[20:20:00]] [INFO] Refreshing screenshot...
[[20:20:00]] [INFO] k3mu9Mt7Ec=pass
[[20:19:47]] [INFO] k3mu9Mt7Ec=running
[[20:19:47]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[20:19:47]] [SUCCESS] Screenshot refreshed successfully
[[20:19:47]] [SUCCESS] Screenshot refreshed successfully
[[20:19:46]] [SUCCESS] Screenshot refreshed
[[20:19:46]] [INFO] Refreshing screenshot...
[[20:19:46]] [INFO] FFM0CCo6Qg=pass
[[20:19:41]] [INFO] FFM0CCo6Qg=running
[[20:19:41]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[20:19:41]] [SUCCESS] Screenshot refreshed successfully
[[20:19:41]] [SUCCESS] Screenshot refreshed successfully
[[20:19:40]] [SUCCESS] Screenshot refreshed
[[20:19:40]] [INFO] Refreshing screenshot...
[[20:19:40]] [INFO] LWXWKZE4UV=pass
[[20:19:24]] [SUCCESS] Screenshot refreshed successfully
[[20:19:24]] [SUCCESS] Screenshot refreshed successfully
[[20:19:24]] [INFO] LWXWKZE4UV=running
[[20:19:24]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[20:19:24]] [SUCCESS] Screenshot refreshed
[[20:19:24]] [INFO] Refreshing screenshot...
[[20:19:24]] [INFO] Qbg9bipTGs=pass
[[20:19:19]] [SUCCESS] Screenshot refreshed successfully
[[20:19:19]] [SUCCESS] Screenshot refreshed successfully
[[20:19:19]] [INFO] Qbg9bipTGs=running
[[20:19:19]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[20:19:18]] [SUCCESS] Screenshot refreshed
[[20:19:18]] [INFO] Refreshing screenshot...
[[20:19:18]] [INFO] 7SpDO20tS2=pass
[[20:19:07]] [SUCCESS] Screenshot refreshed successfully
[[20:19:07]] [SUCCESS] Screenshot refreshed successfully
[[20:19:07]] [INFO] 7SpDO20tS2=running
[[20:19:07]] [INFO] Executing action 13/27: Wait for 10 ms
[[20:19:06]] [SUCCESS] Screenshot refreshed
[[20:19:06]] [INFO] Refreshing screenshot...
[[20:19:06]] [INFO] drbQBpgBfM=pass
[[20:19:03]] [SUCCESS] Screenshot refreshed successfully
[[20:19:03]] [SUCCESS] Screenshot refreshed successfully
[[20:19:03]] [INFO] drbQBpgBfM=running
[[20:19:03]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[20:19:03]] [SUCCESS] Screenshot refreshed
[[20:19:03]] [INFO] Refreshing screenshot...
[[20:19:03]] [INFO] F1olhgKhUt=pass
[[20:19:00]] [SUCCESS] Screenshot refreshed successfully
[[20:19:00]] [SUCCESS] Screenshot refreshed successfully
[[20:19:00]] [INFO] F1olhgKhUt=running
[[20:19:00]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[20:18:59]] [SUCCESS] Screenshot refreshed
[[20:18:59]] [INFO] Refreshing screenshot...
[[20:18:59]] [INFO] FnrbyHq7bU=pass
[[20:18:57]] [SUCCESS] Screenshot refreshed successfully
[[20:18:57]] [SUCCESS] Screenshot refreshed successfully
[[20:18:57]] [INFO] FnrbyHq7bU=running
[[20:18:57]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[20:18:56]] [SUCCESS] Screenshot refreshed
[[20:18:56]] [INFO] Refreshing screenshot...
[[20:18:56]] [INFO] nAB6Q8LAdv=pass
[[20:18:51]] [INFO] nAB6Q8LAdv=running
[[20:18:51]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[20:18:51]] [SUCCESS] Screenshot refreshed successfully
[[20:18:51]] [SUCCESS] Screenshot refreshed successfully
[[20:18:50]] [SUCCESS] Screenshot refreshed
[[20:18:50]] [INFO] Refreshing screenshot...
[[20:18:50]] [INFO] JRheeTvpJf=pass
[[20:18:49]] [SUCCESS] Screenshot refreshed successfully
[[20:18:49]] [SUCCESS] Screenshot refreshed successfully
[[20:18:48]] [INFO] JRheeTvpJf=running
[[20:18:48]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[20:18:48]] [SUCCESS] Screenshot refreshed
[[20:18:48]] [INFO] Refreshing screenshot...
[[20:18:48]] [INFO] o1gHFWhXTL=pass
[[20:18:44]] [SUCCESS] Screenshot refreshed successfully
[[20:18:44]] [SUCCESS] Screenshot refreshed successfully
[[20:18:43]] [INFO] o1gHFWhXTL=running
[[20:18:43]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[20:18:43]] [SUCCESS] Screenshot refreshed
[[20:18:43]] [INFO] Refreshing screenshot...
[[20:18:43]] [INFO] RLznb7o3ag=pass
[[20:18:38]] [SUCCESS] Screenshot refreshed successfully
[[20:18:38]] [SUCCESS] Screenshot refreshed successfully
[[20:18:37]] [INFO] RLznb7o3ag=running
[[20:18:37]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[20:18:37]] [SUCCESS] Screenshot refreshed
[[20:18:37]] [INFO] Refreshing screenshot...
[[20:18:36]] [SUCCESS] Screenshot refreshed
[[20:18:36]] [INFO] Refreshing screenshot...
[[20:18:34]] [SUCCESS] Screenshot refreshed successfully
[[20:18:34]] [SUCCESS] Screenshot refreshed successfully
[[20:18:34]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[20:18:34]] [SUCCESS] Screenshot refreshed
[[20:18:34]] [INFO] Refreshing screenshot...
[[20:18:32]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[20:18:32]] [SUCCESS] Screenshot refreshed successfully
[[20:18:32]] [SUCCESS] Screenshot refreshed successfully
[[20:18:31]] [SUCCESS] Screenshot refreshed
[[20:18:31]] [INFO] Refreshing screenshot...
[[20:18:29]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[20:18:29]] [SUCCESS] Screenshot refreshed successfully
[[20:18:29]] [SUCCESS] Screenshot refreshed successfully
[[20:18:28]] [SUCCESS] Screenshot refreshed
[[20:18:28]] [INFO] Refreshing screenshot...
[[20:18:26]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[20:18:26]] [SUCCESS] Screenshot refreshed successfully
[[20:18:26]] [SUCCESS] Screenshot refreshed successfully
[[20:18:26]] [SUCCESS] Screenshot refreshed
[[20:18:26]] [INFO] Refreshing screenshot...
[[20:18:24]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[20:18:24]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[20:18:24]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[20:18:24]] [INFO] g052Oo1Gcl=running
[[20:18:24]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[20:18:24]] [SUCCESS] Screenshot refreshed successfully
[[20:18:24]] [SUCCESS] Screenshot refreshed successfully
[[20:18:24]] [SUCCESS] Screenshot refreshed
[[20:18:24]] [INFO] Refreshing screenshot...
[[20:18:24]] [INFO] J9loj6Zs95K=pass
[[20:18:22]] [INFO] J9loj6Zs95K=running
[[20:18:22]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[20:18:22]] [SUCCESS] Screenshot refreshed successfully
[[20:18:22]] [SUCCESS] Screenshot refreshed successfully
[[20:18:21]] [SUCCESS] Screenshot refreshed
[[20:18:21]] [INFO] Refreshing screenshot...
[[20:18:21]] [INFO] Y8v5g7AJD1i=pass
[[20:18:15]] [INFO] Y8v5g7AJD1i=running
[[20:18:15]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[20:18:15]] [SUCCESS] Screenshot refreshed successfully
[[20:18:15]] [SUCCESS] Screenshot refreshed successfully
[[20:18:15]] [SUCCESS] Screenshot refreshed
[[20:18:15]] [INFO] Refreshing screenshot...
[[20:18:15]] [INFO] eqHB0Nj1He=pass
[[20:18:12]] [SUCCESS] Screenshot refreshed successfully
[[20:18:12]] [SUCCESS] Screenshot refreshed successfully
[[20:18:12]] [INFO] eqHB0Nj1He=running
[[20:18:12]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[20:18:11]] [SUCCESS] Screenshot refreshed
[[20:18:11]] [INFO] Refreshing screenshot...
[[20:18:11]] [INFO] H9fkkqcFbZ=pass
[[20:18:09]] [INFO] H9fkkqcFbZ=running
[[20:18:09]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[20:18:09]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[20:18:09]] [SUCCESS] Cleared 65 screenshots from database
[[20:18:09]] [INFO] Clearing screenshots from database before execution...
[[20:18:09]] [SUCCESS] All screenshots deleted successfully
[[20:18:09]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:18:09]] [INFO] Skipping report initialization - single test case execution
[[20:18:08]] [SUCCESS] All screenshots deleted successfully
[[20:18:08]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[20:18:08]] [SUCCESS] Added action: multiStep
[[20:18:08]] [SUCCESS] Added action: tapIfLocatorExists
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: waitTill
[[20:18:08]] [SUCCESS] Added action: text
[[20:18:08]] [SUCCESS] Added action: tapOnText
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: swipe
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: swipe
[[20:18:08]] [SUCCESS] Added action: wait
[[20:18:08]] [SUCCESS] Added action: tapIfLocatorExists
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: waitTill
[[20:18:08]] [SUCCESS] Added action: text
[[20:18:08]] [SUCCESS] Added action: tapOnText
[[20:18:08]] [SUCCESS] Added action: waitTill
[[20:18:08]] [SUCCESS] Added action: multiStep
[[20:18:08]] [SUCCESS] Added action: waitTill
[[20:18:08]] [SUCCESS] Added action: tap
[[20:18:08]] [SUCCESS] Added action: launchApp
[[20:18:08]] [SUCCESS] Added action: terminateApp
[[20:18:08]] [INFO] All actions cleared
[[20:18:08]] [INFO] Cleaning up screenshots...
[[20:18:07]] [SUCCESS] Screenshot refreshed successfully
[[20:18:06]] [SUCCESS] Screenshot refreshed
[[20:18:06]] [INFO] Refreshing screenshot...
[[20:18:05]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[20:18:05]] [INFO] Device info updated: RMX2151
[[20:17:56]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[20:17:54]] [SUCCESS] Found 1 device(s)
[[20:17:54]] [INFO] Refreshing device list...
