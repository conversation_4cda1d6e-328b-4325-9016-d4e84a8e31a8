Action Log - 2025-07-13 06:39:48
================================================================================

[[06:39:48]] [INFO] Generating execution report...
[[06:39:48]] [SUCCESS] All tests passed successfully!
[[06:39:48]] [SUCCESS] Screenshot refreshed
[[06:39:48]] [INFO] Refreshing screenshot...
[[06:39:47]] [SUCCESS] Screenshot refreshed
[[06:39:47]] [INFO] Refreshing screenshot...
[[06:39:45]] [SUCCESS] Screenshot refreshed successfully
[[06:39:45]] [SUCCESS] Screenshot refreshed successfully
[[06:39:45]] [INFO] Executing Multi Step action step 48/48: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[06:39:44]] [SUCCESS] Screenshot refreshed
[[06:39:44]] [INFO] Refreshing screenshot...
[[06:39:42]] [SUCCESS] Screenshot refreshed successfully
[[06:39:42]] [SUCCESS] Screenshot refreshed successfully
[[06:39:42]] [INFO] Executing Multi Step action step 47/48: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[06:39:41]] [SUCCESS] Screenshot refreshed
[[06:39:41]] [INFO] Refreshing screenshot...
[[06:39:36]] [SUCCESS] Screenshot refreshed successfully
[[06:39:36]] [SUCCESS] Screenshot refreshed successfully
[[06:39:36]] [INFO] Executing Multi Step action step 46/48: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Remove")]" is visible
[[06:39:36]] [SUCCESS] Screenshot refreshed
[[06:39:36]] [INFO] Refreshing screenshot...
[[06:39:17]] [SUCCESS] Screenshot refreshed successfully
[[06:39:17]] [SUCCESS] Screenshot refreshed successfully
[[06:39:16]] [INFO] Executing Multi Step action step 45/48: Wait till xpath=//android.widget.TextView[@text="Click & Collect"]
[[06:39:16]] [SUCCESS] Screenshot refreshed
[[06:39:16]] [INFO] Refreshing screenshot...
[[06:39:14]] [SUCCESS] Screenshot refreshed successfully
[[06:39:14]] [SUCCESS] Screenshot refreshed successfully
[[06:39:13]] [INFO] Executing Multi Step action step 44/48: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[06:39:12]] [SUCCESS] Screenshot refreshed
[[06:39:12]] [INFO] Refreshing screenshot...
[[06:38:41]] [SUCCESS] Screenshot refreshed successfully
[[06:38:41]] [SUCCESS] Screenshot refreshed successfully
[[06:38:40]] [INFO] Executing Multi Step action step 43/48: Tap on image: bag-close-android.png
[[06:38:39]] [SUCCESS] Screenshot refreshed
[[06:38:39]] [INFO] Refreshing screenshot...
[[06:38:33]] [SUCCESS] Screenshot refreshed successfully
[[06:38:33]] [SUCCESS] Screenshot refreshed successfully
[[06:38:32]] [INFO] Executing Multi Step action step 42/48: Wait for 5 ms
[[06:38:31]] [SUCCESS] Screenshot refreshed
[[06:38:31]] [INFO] Refreshing screenshot...
[[06:38:29]] [SUCCESS] Screenshot refreshed successfully
[[06:38:29]] [SUCCESS] Screenshot refreshed successfully
[[06:38:29]] [INFO] Executing Multi Step action step 41/48: Android Function: send_key_event - Key Event: BACK
[[06:38:28]] [SUCCESS] Screenshot refreshed
[[06:38:28]] [INFO] Refreshing screenshot...
[[06:38:18]] [SUCCESS] Screenshot refreshed successfully
[[06:38:18]] [SUCCESS] Screenshot refreshed successfully
[[06:38:18]] [INFO] Executing Multi Step action step 40/48: Wait for 5 ms
[[06:38:17]] [SUCCESS] Screenshot refreshed
[[06:38:17]] [INFO] Refreshing screenshot...
[[06:38:13]] [SUCCESS] Screenshot refreshed successfully
[[06:38:13]] [SUCCESS] Screenshot refreshed successfully
[[06:38:13]] [INFO] Executing Multi Step action step 39/48: Tap on Text: "Check"
[[06:38:13]] [SUCCESS] Screenshot refreshed
[[06:38:13]] [INFO] Refreshing screenshot...
[[06:38:05]] [SUCCESS] Screenshot refreshed successfully
[[06:38:05]] [SUCCESS] Screenshot refreshed successfully
[[06:38:05]] [INFO] Executing Multi Step action step 38/48: Wait for 5 ms
[[06:38:04]] [SUCCESS] Screenshot refreshed
[[06:38:04]] [INFO] Refreshing screenshot...
[[06:37:22]] [SUCCESS] Screenshot refreshed successfully
[[06:37:22]] [SUCCESS] Screenshot refreshed successfully
[[06:37:22]] [INFO] Executing Multi Step action step 37/48: Tap on image: zippay-chkbox-btn.png
[[06:37:22]] [SUCCESS] Screenshot refreshed
[[06:37:22]] [INFO] Refreshing screenshot...
[[06:37:10]] [SUCCESS] Screenshot refreshed successfully
[[06:37:10]] [SUCCESS] Screenshot refreshed successfully
[[06:37:09]] [INFO] Executing Multi Step action step 36/48: Wait for 5 ms
[[06:37:09]] [SUCCESS] Screenshot refreshed
[[06:37:09]] [INFO] Refreshing screenshot...
[[06:37:07]] [SUCCESS] Screenshot refreshed successfully
[[06:37:07]] [SUCCESS] Screenshot refreshed successfully
[[06:37:07]] [INFO] Executing Multi Step action step 35/48: Android Function: send_key_event - Key Event: BACK
[[06:37:07]] [SUCCESS] Screenshot refreshed
[[06:37:07]] [INFO] Refreshing screenshot...
[[06:37:03]] [SUCCESS] Screenshot refreshed successfully
[[06:37:03]] [SUCCESS] Screenshot refreshed successfully
[[06:37:03]] [INFO] Executing Multi Step action step 34/48: Tap on Text: "Check"
[[06:37:02]] [SUCCESS] Screenshot refreshed
[[06:37:02]] [INFO] Refreshing screenshot...
[[06:36:56]] [SUCCESS] Screenshot refreshed successfully
[[06:36:56]] [SUCCESS] Screenshot refreshed successfully
[[06:36:56]] [INFO] Executing Multi Step action step 33/48: Wait for 5 ms
[[06:36:55]] [SUCCESS] Screenshot refreshed
[[06:36:55]] [INFO] Refreshing screenshot...
[[06:36:49]] [SUCCESS] Screenshot refreshed successfully
[[06:36:49]] [SUCCESS] Screenshot refreshed successfully
[[06:36:49]] [INFO] Executing Multi Step action step 32/48: Tap on image: afterpay-chkbox-android.png
[[06:36:48]] [SUCCESS] Screenshot refreshed
[[06:36:48]] [INFO] Refreshing screenshot...
[[06:36:31]] [SUCCESS] Screenshot refreshed successfully
[[06:36:31]] [SUCCESS] Screenshot refreshed successfully
[[06:36:30]] [INFO] Executing Multi Step action step 31/48: Wait for 5 ms
[[06:36:30]] [SUCCESS] Screenshot refreshed
[[06:36:30]] [INFO] Refreshing screenshot...
[[06:36:24]] [SUCCESS] Screenshot refreshed successfully
[[06:36:24]] [SUCCESS] Screenshot refreshed successfully
[[06:36:24]] [INFO] Executing Multi Step action step 30/48: Tap on image: paypal-close-btn-android.png
[[06:36:23]] [SUCCESS] Screenshot refreshed
[[06:36:23]] [INFO] Refreshing screenshot...
[[06:36:10]] [SUCCESS] Screenshot refreshed successfully
[[06:36:10]] [SUCCESS] Screenshot refreshed successfully
[[06:36:10]] [INFO] Executing Multi Step action step 29/48: Tap on image: Payin4-btn-Android.png
[[06:36:10]] [SUCCESS] Screenshot refreshed
[[06:36:10]] [INFO] Refreshing screenshot...
[[06:36:03]] [SUCCESS] Screenshot refreshed successfully
[[06:36:03]] [SUCCESS] Screenshot refreshed successfully
[[06:36:03]] [INFO] Executing Multi Step action step 28/48: Wait for 5 ms
[[06:36:02]] [SUCCESS] Screenshot refreshed
[[06:36:02]] [INFO] Refreshing screenshot...
[[06:35:31]] [SUCCESS] Screenshot refreshed successfully
[[06:35:31]] [SUCCESS] Screenshot refreshed successfully
[[06:35:31]] [INFO] Executing Multi Step action step 27/48: Tap on image: PaypalIn4-Chkbox-Android.png
[[06:35:30]] [SUCCESS] Screenshot refreshed
[[06:35:30]] [INFO] Refreshing screenshot...
[[06:35:24]] [SUCCESS] Screenshot refreshed successfully
[[06:35:24]] [SUCCESS] Screenshot refreshed successfully
[[06:35:24]] [INFO] Executing Multi Step action step 26/48: Wait for 5 ms
[[06:35:23]] [SUCCESS] Screenshot refreshed
[[06:35:23]] [INFO] Refreshing screenshot...
[[06:35:07]] [SUCCESS] Screenshot refreshed successfully
[[06:35:07]] [SUCCESS] Screenshot refreshed successfully
[[06:35:07]] [INFO] Executing Multi Step action step 25/48: Tap on image: paypal-close-btn-android.png
[[06:35:06]] [SUCCESS] Screenshot refreshed
[[06:35:06]] [INFO] Refreshing screenshot...
[[06:34:59]] [SUCCESS] Screenshot refreshed successfully
[[06:34:59]] [SUCCESS] Screenshot refreshed successfully
[[06:34:59]] [INFO] Executing Multi Step action step 24/48: Wait for 5 ms
[[06:34:59]] [SUCCESS] Screenshot refreshed
[[06:34:59]] [INFO] Refreshing screenshot...
[[06:34:53]] [SUCCESS] Screenshot refreshed successfully
[[06:34:53]] [SUCCESS] Screenshot refreshed successfully
[[06:34:52]] [INFO] Executing Multi Step action step 23/48: Tap on image: paypal-payment-btn-android.png
[[06:34:52]] [SUCCESS] Screenshot refreshed
[[06:34:52]] [INFO] Refreshing screenshot...
[[06:34:47]] [SUCCESS] Screenshot refreshed successfully
[[06:34:47]] [SUCCESS] Screenshot refreshed successfully
[[06:34:47]] [INFO] Executing Multi Step action step 22/48: Swipe from (50%, 70%) to (50%, 40%)
[[06:34:46]] [SUCCESS] Screenshot refreshed
[[06:34:46]] [INFO] Refreshing screenshot...
[[06:34:43]] [INFO] Executing Multi Step action step 21/48: Tap on Text: "PayPal"
[[06:34:43]] [SUCCESS] Screenshot refreshed successfully
[[06:34:43]] [SUCCESS] Screenshot refreshed successfully
[[06:34:42]] [SUCCESS] Screenshot refreshed
[[06:34:42]] [INFO] Refreshing screenshot...
[[06:34:39]] [SUCCESS] Screenshot refreshed successfully
[[06:34:39]] [SUCCESS] Screenshot refreshed successfully
[[06:34:39]] [INFO] Executing Multi Step action step 20/48: Tap on Text: "Continue"
[[06:34:38]] [SUCCESS] Screenshot refreshed
[[06:34:38]] [INFO] Refreshing screenshot...
[[06:34:26]] [SUCCESS] Screenshot refreshed successfully
[[06:34:26]] [SUCCESS] Screenshot refreshed successfully
[[06:34:26]] [INFO] Executing Multi Step action step 19/48: Swipe from (50%, 70%) to (50%, 40%)
[[06:34:26]] [SUCCESS] Screenshot refreshed
[[06:34:26]] [INFO] Refreshing screenshot...
[[06:34:24]] [SUCCESS] Screenshot refreshed successfully
[[06:34:24]] [SUCCESS] Screenshot refreshed successfully
[[06:34:24]] [INFO] Executing Multi Step action step 18/48: Android Function: send_key_event - Key Event: ENTER
[[06:34:23]] [SUCCESS] Screenshot refreshed
[[06:34:23]] [INFO] Refreshing screenshot...
[[06:34:12]] [SUCCESS] Screenshot refreshed successfully
[[06:34:12]] [SUCCESS] Screenshot refreshed successfully
[[06:34:12]] [INFO] Executing Multi Step action step 17/48: Tap on image: continue-to-details-android.png
[[06:34:12]] [SUCCESS] Screenshot refreshed
[[06:34:12]] [INFO] Refreshing screenshot...
[[06:34:10]] [SUCCESS] Screenshot refreshed successfully
[[06:34:10]] [SUCCESS] Screenshot refreshed successfully
[[06:34:10]] [INFO] Executing Multi Step action step 16/48: Input text: "305 238 Flinders Street"
[[06:34:09]] [SUCCESS] Screenshot refreshed
[[06:34:09]] [INFO] Refreshing screenshot...
[[06:34:06]] [SUCCESS] Screenshot refreshed successfully
[[06:34:06]] [SUCCESS] Screenshot refreshed successfully
[[06:34:05]] [INFO] Executing Multi Step action step 15/48: Tap on Text: "address"
[[06:34:05]] [SUCCESS] Screenshot refreshed
[[06:34:05]] [INFO] Refreshing screenshot...
[[06:34:01]] [SUCCESS] Screenshot refreshed successfully
[[06:34:01]] [SUCCESS] Screenshot refreshed successfully
[[06:34:01]] [INFO] Executing Multi Step action step 14/48: Tap on Text: "Continue"
[[06:34:00]] [SUCCESS] Screenshot refreshed
[[06:34:00]] [INFO] Refreshing screenshot...
[[06:33:28]] [SUCCESS] Screenshot refreshed successfully
[[06:33:28]] [SUCCESS] Screenshot refreshed successfully
[[06:33:27]] [INFO] Executing Multi Step action step 13/48: Swipe from (50%, 70%) to (50%, 50%)
[[06:33:27]] [SUCCESS] Screenshot refreshed
[[06:33:27]] [INFO] Refreshing screenshot...
[[06:33:25]] [SUCCESS] Screenshot refreshed successfully
[[06:33:25]] [SUCCESS] Screenshot refreshed successfully
[[06:33:25]] [INFO] Executing Multi Step action step 12/48: Input text: "0400000000"
[[06:33:24]] [SUCCESS] Screenshot refreshed
[[06:33:24]] [INFO] Refreshing screenshot...
[[06:33:03]] [SUCCESS] Screenshot refreshed successfully
[[06:33:03]] [SUCCESS] Screenshot refreshed successfully
[[06:33:03]] [INFO] Executing Multi Step action step 11/48: Tap on Text: "Mobile"
[[06:33:02]] [SUCCESS] Screenshot refreshed
[[06:33:02]] [INFO] Refreshing screenshot...
[[06:33:00]] [SUCCESS] Screenshot refreshed successfully
[[06:33:00]] [SUCCESS] Screenshot refreshed successfully
[[06:33:00]] [INFO] Executing Multi Step action step 10/48: Input text: "<EMAIL>"
[[06:32:59]] [SUCCESS] Screenshot refreshed
[[06:32:59]] [INFO] Refreshing screenshot...
[[06:32:55]] [SUCCESS] Screenshot refreshed successfully
[[06:32:55]] [SUCCESS] Screenshot refreshed successfully
[[06:32:55]] [INFO] Executing Multi Step action step 9/48: Tap on Text: "Email"
[[06:32:54]] [SUCCESS] Screenshot refreshed
[[06:32:54]] [INFO] Refreshing screenshot...
[[06:32:31]] [SUCCESS] Screenshot refreshed successfully
[[06:32:31]] [SUCCESS] Screenshot refreshed successfully
[[06:32:30]] [INFO] Executing Multi Step action step 8/48: Input text: "LastName"
[[06:32:30]] [SUCCESS] Screenshot refreshed
[[06:32:30]] [INFO] Refreshing screenshot...
[[06:32:26]] [SUCCESS] Screenshot refreshed successfully
[[06:32:26]] [SUCCESS] Screenshot refreshed successfully
[[06:32:26]] [INFO] Executing Multi Step action step 7/48: Tap on Text: "Last"
[[06:32:25]] [SUCCESS] Screenshot refreshed
[[06:32:25]] [INFO] Refreshing screenshot...
[[06:32:23]] [SUCCESS] Screenshot refreshed successfully
[[06:32:23]] [SUCCESS] Screenshot refreshed successfully
[[06:32:23]] [INFO] Executing Multi Step action step 6/48: Input text: "Dummy"
[[06:32:22]] [SUCCESS] Screenshot refreshed
[[06:32:22]] [INFO] Refreshing screenshot...
[[06:32:08]] [SUCCESS] Screenshot refreshed successfully
[[06:32:08]] [SUCCESS] Screenshot refreshed successfully
[[06:32:08]] [INFO] Executing Multi Step action step 5/48: Tap on Text: "First"
[[06:32:07]] [SUCCESS] Screenshot refreshed
[[06:32:07]] [INFO] Refreshing screenshot...
[[06:32:01]] [SUCCESS] Screenshot refreshed successfully
[[06:32:01]] [SUCCESS] Screenshot refreshed successfully
[[06:32:01]] [INFO] Executing Multi Step action step 4/48: Tap on image: continue-to-details-android.png
[[06:32:00]] [SUCCESS] Screenshot refreshed
[[06:32:00]] [INFO] Refreshing screenshot...
[[06:31:48]] [SUCCESS] Screenshot refreshed successfully
[[06:31:48]] [SUCCESS] Screenshot refreshed successfully
[[06:31:48]] [INFO] Executing Multi Step action step 3/48: Swipe from (50%, 70%) to (50%, 30%)
[[06:31:47]] [SUCCESS] Screenshot refreshed
[[06:31:47]] [INFO] Refreshing screenshot...
[[06:31:45]] [SUCCESS] Screenshot refreshed successfully
[[06:31:45]] [SUCCESS] Screenshot refreshed successfully
[[06:31:45]] [INFO] Executing Multi Step action step 2/48: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[06:31:44]] [SUCCESS] Screenshot refreshed
[[06:31:44]] [INFO] Refreshing screenshot...
[[06:31:39]] [INFO] Executing Multi Step action step 1/48: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[06:31:39]] [INFO] Loaded 48 steps from test case: Delivery Buy Steps_AU-ANDROID
[[06:31:39]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[06:31:39]] [INFO] e5zwMRuhB1=running
[[06:31:39]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (48 steps)
[[06:31:39]] [SUCCESS] Screenshot refreshed successfully
[[06:31:39]] [SUCCESS] Screenshot refreshed successfully
[[06:31:39]] [SUCCESS] Screenshot refreshed
[[06:31:39]] [INFO] Refreshing screenshot...
[[06:31:39]] [INFO] zrdO3PVkX3=pass
[[06:31:36]] [SUCCESS] Screenshot refreshed successfully
[[06:31:36]] [SUCCESS] Screenshot refreshed successfully
[[06:31:35]] [INFO] zrdO3PVkX3=running
[[06:31:35]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[06:31:35]] [SUCCESS] Screenshot refreshed
[[06:31:35]] [INFO] Refreshing screenshot...
[[06:31:35]] [INFO] F1olhgKhUt=pass
[[06:31:33]] [SUCCESS] Screenshot refreshed successfully
[[06:31:33]] [SUCCESS] Screenshot refreshed successfully
[[06:31:32]] [INFO] F1olhgKhUt=running
[[06:31:32]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[06:31:32]] [SUCCESS] Screenshot refreshed
[[06:31:32]] [INFO] Refreshing screenshot...
[[06:31:32]] [INFO] FnrbyHq7bU=pass
[[06:31:28]] [SUCCESS] Screenshot refreshed successfully
[[06:31:28]] [SUCCESS] Screenshot refreshed successfully
[[06:31:27]] [INFO] FnrbyHq7bU=running
[[06:31:27]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[06:31:27]] [SUCCESS] Screenshot refreshed
[[06:31:27]] [INFO] Refreshing screenshot...
[[06:31:27]] [INFO] nAB6Q8LAdv=pass
[[06:31:20]] [SUCCESS] Screenshot refreshed successfully
[[06:31:20]] [SUCCESS] Screenshot refreshed successfully
[[06:31:20]] [INFO] nAB6Q8LAdv=running
[[06:31:20]] [INFO] Executing action 23/27: Wait till text appears: "Filter"
[[06:31:19]] [SUCCESS] Screenshot refreshed
[[06:31:19]] [INFO] Refreshing screenshot...
[[06:31:19]] [INFO] JRheeTvpJf=pass
[[06:31:17]] [SUCCESS] Screenshot refreshed successfully
[[06:31:17]] [SUCCESS] Screenshot refreshed successfully
[[06:31:17]] [INFO] JRheeTvpJf=running
[[06:31:17]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[06:31:16]] [SUCCESS] Screenshot refreshed
[[06:31:16]] [INFO] Refreshing screenshot...
[[06:31:16]] [INFO] o1gHFWhXTL=pass
[[06:31:12]] [SUCCESS] Screenshot refreshed successfully
[[06:31:12]] [SUCCESS] Screenshot refreshed successfully
[[06:31:11]] [INFO] o1gHFWhXTL=running
[[06:31:11]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[06:31:10]] [SUCCESS] Screenshot refreshed
[[06:31:10]] [INFO] Refreshing screenshot...
[[06:31:10]] [INFO] cKNu2QoRC1=pass
[[06:31:06]] [SUCCESS] Screenshot refreshed successfully
[[06:31:06]] [SUCCESS] Screenshot refreshed successfully
[[06:31:06]] [INFO] cKNu2QoRC1=running
[[06:31:06]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[06:31:05]] [SUCCESS] Screenshot refreshed
[[06:31:05]] [INFO] Refreshing screenshot...
[[06:31:05]] [INFO] OyUowAaBzD=pass
[[06:31:00]] [SUCCESS] Screenshot refreshed successfully
[[06:31:00]] [SUCCESS] Screenshot refreshed successfully
[[06:31:00]] [INFO] OyUowAaBzD=running
[[06:31:00]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[06:30:59]] [SUCCESS] Screenshot refreshed
[[06:30:59]] [INFO] Refreshing screenshot...
[[06:30:59]] [INFO] Ob26qqcA0p=pass
[[06:30:54]] [SUCCESS] Screenshot refreshed successfully
[[06:30:54]] [SUCCESS] Screenshot refreshed successfully
[[06:30:54]] [INFO] Ob26qqcA0p=running
[[06:30:54]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[06:30:53]] [SUCCESS] Screenshot refreshed
[[06:30:53]] [INFO] Refreshing screenshot...
[[06:30:53]] [INFO] k3mu9Mt7Ec=pass
[[06:30:51]] [SUCCESS] Screenshot refreshed successfully
[[06:30:51]] [SUCCESS] Screenshot refreshed successfully
[[06:30:51]] [INFO] k3mu9Mt7Ec=running
[[06:30:51]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[06:30:50]] [SUCCESS] Screenshot refreshed
[[06:30:50]] [INFO] Refreshing screenshot...
[[06:30:50]] [INFO] FFM0CCo6Qg=pass
[[06:30:44]] [INFO] FFM0CCo6Qg=running
[[06:30:44]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[06:30:44]] [SUCCESS] Screenshot refreshed successfully
[[06:30:44]] [SUCCESS] Screenshot refreshed successfully
[[06:30:44]] [SUCCESS] Screenshot refreshed
[[06:30:44]] [INFO] Refreshing screenshot...
[[06:30:44]] [INFO] LWXWKZE4UV=pass
[[06:30:28]] [SUCCESS] Screenshot refreshed successfully
[[06:30:28]] [SUCCESS] Screenshot refreshed successfully
[[06:30:28]] [INFO] LWXWKZE4UV=running
[[06:30:28]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[06:30:27]] [SUCCESS] Screenshot refreshed
[[06:30:27]] [INFO] Refreshing screenshot...
[[06:30:27]] [INFO] Qbg9bipTGs=pass
[[06:30:22]] [SUCCESS] Screenshot refreshed successfully
[[06:30:22]] [SUCCESS] Screenshot refreshed successfully
[[06:30:22]] [INFO] Qbg9bipTGs=running
[[06:30:22]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[06:30:21]] [SUCCESS] Screenshot refreshed
[[06:30:21]] [INFO] Refreshing screenshot...
[[06:30:21]] [INFO] 7SpDO20tS2=pass
[[06:30:00]] [SUCCESS] Screenshot refreshed successfully
[[06:30:00]] [SUCCESS] Screenshot refreshed successfully
[[06:30:00]] [INFO] 7SpDO20tS2=running
[[06:30:00]] [INFO] Executing action 13/27: Wait for 10 ms
[[06:29:59]] [SUCCESS] Screenshot refreshed
[[06:29:59]] [INFO] Refreshing screenshot...
[[06:29:59]] [INFO] drbQBpgBfM=pass
[[06:29:56]] [SUCCESS] Screenshot refreshed successfully
[[06:29:56]] [SUCCESS] Screenshot refreshed successfully
[[06:29:55]] [INFO] drbQBpgBfM=running
[[06:29:55]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[06:29:55]] [SUCCESS] Screenshot refreshed
[[06:29:55]] [INFO] Refreshing screenshot...
[[06:29:55]] [INFO] F1olhgKhUt=pass
[[06:29:53]] [SUCCESS] Screenshot refreshed successfully
[[06:29:53]] [SUCCESS] Screenshot refreshed successfully
[[06:29:52]] [INFO] F1olhgKhUt=running
[[06:29:52]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[06:29:52]] [SUCCESS] Screenshot refreshed
[[06:29:52]] [INFO] Refreshing screenshot...
[[06:29:52]] [INFO] FnrbyHq7bU=pass
[[06:29:48]] [SUCCESS] Screenshot refreshed successfully
[[06:29:48]] [SUCCESS] Screenshot refreshed successfully
[[06:29:47]] [INFO] FnrbyHq7bU=running
[[06:29:47]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[06:29:47]] [SUCCESS] Screenshot refreshed
[[06:29:47]] [INFO] Refreshing screenshot...
[[06:29:47]] [INFO] nAB6Q8LAdv=pass
[[06:29:40]] [SUCCESS] Screenshot refreshed successfully
[[06:29:40]] [SUCCESS] Screenshot refreshed successfully
[[06:29:40]] [INFO] nAB6Q8LAdv=running
[[06:29:40]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[06:29:39]] [SUCCESS] Screenshot refreshed
[[06:29:39]] [INFO] Refreshing screenshot...
[[06:29:39]] [INFO] JRheeTvpJf=pass
[[06:29:38]] [SUCCESS] Screenshot refreshed successfully
[[06:29:38]] [SUCCESS] Screenshot refreshed successfully
[[06:29:38]] [INFO] JRheeTvpJf=running
[[06:29:38]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[06:29:37]] [SUCCESS] Screenshot refreshed
[[06:29:37]] [INFO] Refreshing screenshot...
[[06:29:37]] [INFO] o1gHFWhXTL=pass
[[06:29:33]] [SUCCESS] Screenshot refreshed successfully
[[06:29:33]] [SUCCESS] Screenshot refreshed successfully
[[06:29:32]] [INFO] o1gHFWhXTL=running
[[06:29:32]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[06:29:31]] [SUCCESS] Screenshot refreshed
[[06:29:31]] [INFO] Refreshing screenshot...
[[06:29:31]] [INFO] RLznb7o3ag=pass
[[06:29:27]] [SUCCESS] Screenshot refreshed successfully
[[06:29:27]] [SUCCESS] Screenshot refreshed successfully
[[06:29:25]] [INFO] RLznb7o3ag=running
[[06:29:25]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[06:29:25]] [SUCCESS] Screenshot refreshed
[[06:29:25]] [INFO] Refreshing screenshot...
[[06:29:25]] [SUCCESS] Screenshot refreshed
[[06:29:25]] [INFO] Refreshing screenshot...
[[06:29:22]] [SUCCESS] Screenshot refreshed successfully
[[06:29:22]] [SUCCESS] Screenshot refreshed successfully
[[06:29:22]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[06:29:21]] [SUCCESS] Screenshot refreshed
[[06:29:21]] [INFO] Refreshing screenshot...
[[06:29:07]] [SUCCESS] Screenshot refreshed successfully
[[06:29:07]] [SUCCESS] Screenshot refreshed successfully
[[06:29:07]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[06:29:06]] [SUCCESS] Screenshot refreshed
[[06:29:06]] [INFO] Refreshing screenshot...
[[06:29:04]] [SUCCESS] Screenshot refreshed successfully
[[06:29:04]] [SUCCESS] Screenshot refreshed successfully
[[06:29:04]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[06:29:03]] [SUCCESS] Screenshot refreshed
[[06:29:03]] [INFO] Refreshing screenshot...
[[06:29:00]] [SUCCESS] Screenshot refreshed successfully
[[06:29:00]] [SUCCESS] Screenshot refreshed successfully
[[06:28:38]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[06:28:38]] [SUCCESS] Screenshot refreshed
[[06:28:38]] [INFO] Refreshing screenshot...
[[06:28:35]] [SUCCESS] Screenshot refreshed successfully
[[06:28:35]] [SUCCESS] Screenshot refreshed successfully
[[06:28:35]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[06:28:35]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[06:28:35]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[06:28:35]] [INFO] g052Oo1Gcl=running
[[06:28:35]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[06:28:35]] [SUCCESS] Screenshot refreshed
[[06:28:35]] [INFO] Refreshing screenshot...
[[06:28:35]] [INFO] J9loj6Zs95K=pass
[[06:28:33]] [SUCCESS] Screenshot refreshed successfully
[[06:28:33]] [SUCCESS] Screenshot refreshed successfully
[[06:28:33]] [INFO] J9loj6Zs95K=running
[[06:28:33]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[06:28:32]] [SUCCESS] Screenshot refreshed
[[06:28:32]] [INFO] Refreshing screenshot...
[[06:28:32]] [INFO] Y8v5g7AJD1i=pass
[[06:28:25]] [INFO] Y8v5g7AJD1i=running
[[06:28:25]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[06:28:25]] [SUCCESS] Screenshot refreshed successfully
[[06:28:25]] [SUCCESS] Screenshot refreshed successfully
[[06:28:25]] [SUCCESS] Screenshot refreshed
[[06:28:25]] [INFO] Refreshing screenshot...
[[06:28:25]] [INFO] eqHB0Nj1He=pass
[[06:28:22]] [SUCCESS] Screenshot refreshed successfully
[[06:28:22]] [SUCCESS] Screenshot refreshed successfully
[[06:28:22]] [INFO] eqHB0Nj1He=running
[[06:28:22]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[06:28:21]] [SUCCESS] Screenshot refreshed
[[06:28:21]] [INFO] Refreshing screenshot...
[[06:28:21]] [INFO] H9fkkqcFbZ=pass
[[06:28:19]] [INFO] H9fkkqcFbZ=running
[[06:28:19]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[06:28:19]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[06:28:19]] [SUCCESS] Cleared 70 screenshots from database
[[06:28:19]] [INFO] Clearing screenshots from database before execution...
[[06:28:19]] [SUCCESS] All screenshots deleted successfully
[[06:28:19]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[06:28:19]] [INFO] Skipping report initialization - single test case execution
[[06:28:18]] [SUCCESS] All screenshots deleted successfully
[[06:28:18]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[06:28:18]] [SUCCESS] Added action: multiStep
[[06:28:18]] [SUCCESS] Added action: tapIfLocatorExists
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: waitTill
[[06:28:18]] [SUCCESS] Added action: text
[[06:28:18]] [SUCCESS] Added action: tapOnText
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: swipe
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: swipe
[[06:28:18]] [SUCCESS] Added action: wait
[[06:28:18]] [SUCCESS] Added action: tapIfLocatorExists
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: waitTill
[[06:28:18]] [SUCCESS] Added action: text
[[06:28:18]] [SUCCESS] Added action: tapOnText
[[06:28:18]] [SUCCESS] Added action: waitTill
[[06:28:18]] [SUCCESS] Added action: multiStep
[[06:28:18]] [SUCCESS] Added action: waitTill
[[06:28:18]] [SUCCESS] Added action: tap
[[06:28:18]] [SUCCESS] Added action: launchApp
[[06:28:18]] [SUCCESS] Added action: terminateApp
[[06:28:18]] [INFO] All actions cleared
[[06:28:18]] [INFO] Cleaning up screenshots...
[[06:28:11]] [SUCCESS] Screenshot refreshed successfully
[[06:28:11]] [SUCCESS] Screenshot refreshed
[[06:28:11]] [INFO] Refreshing screenshot...
[[06:28:10]] [SUCCESS] Connected to device: ************:33739
[[06:28:10]] [INFO] Device info updated: RMX2151
[[06:28:05]] [INFO] Connecting to device: ************:33739 (Platform: Android)...
[[06:28:03]] [SUCCESS] Found 1 device(s)
[[06:28:02]] [INFO] Refreshing device list...
