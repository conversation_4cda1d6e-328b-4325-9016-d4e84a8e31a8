Action Log - 2025-07-13 07:42:35
================================================================================

[[07:42:35]] [INFO] Generating execution report...
[[07:42:35]] [SUCCESS] All tests passed successfully!
[[07:42:35]] [INFO] Moving to the next test case after failure (server will handle retry)
[[07:42:35]] [ERROR] Multi Step action step 38 failed: Text 'Check' not found within timeout (30s)
[[07:42:01]] [SUCCESS] Screenshot refreshed successfully
[[07:42:01]] [SUCCESS] Screenshot refreshed successfully
[[07:42:01]] [INFO] Executing Multi Step action step 38/52: Tap on Text: "Check"
[[07:42:00]] [SUCCESS] Screenshot refreshed
[[07:42:00]] [INFO] Refreshing screenshot...
[[07:41:27]] [SUCCESS] Screenshot refreshed successfully
[[07:41:27]] [SUCCESS] Screenshot refreshed successfully
[[07:41:27]] [INFO] Executing Multi Step action step 37/52: Wait for 5 ms
[[07:41:27]] [SUCCESS] Screenshot refreshed
[[07:41:27]] [INFO] Refreshing screenshot...
[[07:41:21]] [SUCCESS] Screenshot refreshed successfully
[[07:41:21]] [SUCCESS] Screenshot refreshed successfully
[[07:41:21]] [INFO] Executing Multi Step action step 36/52: Tap on image: afterpay-chkbox-android.png
[[07:41:20]] [SUCCESS] Screenshot refreshed
[[07:41:20]] [INFO] Refreshing screenshot...
[[07:41:05]] [SUCCESS] Screenshot refreshed successfully
[[07:41:05]] [SUCCESS] Screenshot refreshed successfully
[[07:41:05]] [INFO] Executing Multi Step action step 35/52: Wait for 5 ms
[[07:41:04]] [SUCCESS] Screenshot refreshed
[[07:41:04]] [INFO] Refreshing screenshot...
[[07:40:08]] [SUCCESS] Screenshot refreshed successfully
[[07:40:08]] [SUCCESS] Screenshot refreshed successfully
[[07:40:08]] [INFO] Executing Multi Step action step 34/52: Tap on image: paypal-close-btn-android.png
[[07:40:08]] [SUCCESS] Screenshot refreshed
[[07:40:08]] [INFO] Refreshing screenshot...
[[07:40:01]] [SUCCESS] Screenshot refreshed successfully
[[07:40:01]] [SUCCESS] Screenshot refreshed successfully
[[07:40:01]] [INFO] Executing Multi Step action step 33/52: Tap on image: Payin4-btn-Android.png
[[07:40:01]] [SUCCESS] Screenshot refreshed
[[07:40:01]] [INFO] Refreshing screenshot...
[[07:39:29]] [SUCCESS] Screenshot refreshed successfully
[[07:39:29]] [SUCCESS] Screenshot refreshed successfully
[[07:39:29]] [INFO] Executing Multi Step action step 32/52: Wait for 5 ms
[[07:39:28]] [SUCCESS] Screenshot refreshed
[[07:39:28]] [INFO] Refreshing screenshot...
[[07:39:22]] [SUCCESS] Screenshot refreshed successfully
[[07:39:22]] [SUCCESS] Screenshot refreshed successfully
[[07:39:22]] [INFO] Executing Multi Step action step 31/52: Tap on image: PaypalIn4-Chkbox-Android.png
[[07:39:22]] [SUCCESS] Screenshot refreshed
[[07:39:22]] [INFO] Refreshing screenshot...
[[07:39:05]] [SUCCESS] Screenshot refreshed successfully
[[07:39:05]] [SUCCESS] Screenshot refreshed successfully
[[07:39:05]] [INFO] Executing Multi Step action step 30/52: Wait for 5 ms
[[07:39:04]] [SUCCESS] Screenshot refreshed
[[07:39:04]] [INFO] Refreshing screenshot...
[[07:38:08]] [SUCCESS] Screenshot refreshed successfully
[[07:38:08]] [SUCCESS] Screenshot refreshed successfully
[[07:38:08]] [INFO] Executing Multi Step action step 29/52: Tap on image: paypal-close-btn-android.png
[[07:38:07]] [SUCCESS] Screenshot refreshed
[[07:38:07]] [INFO] Refreshing screenshot...
[[07:38:01]] [SUCCESS] Screenshot refreshed successfully
[[07:38:01]] [SUCCESS] Screenshot refreshed successfully
[[07:38:01]] [INFO] Executing Multi Step action step 28/52: Wait for 5 ms
[[07:38:00]] [SUCCESS] Screenshot refreshed
[[07:38:00]] [INFO] Refreshing screenshot...
[[07:37:27]] [SUCCESS] Screenshot refreshed successfully
[[07:37:27]] [SUCCESS] Screenshot refreshed successfully
[[07:37:27]] [INFO] Executing Multi Step action step 27/52: Tap on image: paypal-payment-btn-android.png
[[07:37:26]] [SUCCESS] Screenshot refreshed
[[07:37:26]] [INFO] Refreshing screenshot...
[[07:37:21]] [SUCCESS] Screenshot refreshed successfully
[[07:37:21]] [SUCCESS] Screenshot refreshed successfully
[[07:37:20]] [INFO] Executing Multi Step action step 26/52: Swipe from (50%, 70%) to (50%, 40%)
[[07:37:20]] [SUCCESS] Screenshot refreshed
[[07:37:20]] [INFO] Refreshing screenshot...
[[07:37:16]] [SUCCESS] Screenshot refreshed successfully
[[07:37:16]] [SUCCESS] Screenshot refreshed successfully
[[07:37:16]] [INFO] Executing Multi Step action step 25/52: Tap on Text: "PayPal"
[[07:37:16]] [SUCCESS] Screenshot refreshed
[[07:37:16]] [INFO] Refreshing screenshot...
[[07:37:12]] [SUCCESS] Screenshot refreshed successfully
[[07:37:12]] [SUCCESS] Screenshot refreshed successfully
[[07:37:12]] [INFO] Executing Multi Step action step 24/52: Tap on Text: "Continue"
[[07:37:11]] [SUCCESS] Screenshot refreshed
[[07:37:11]] [INFO] Refreshing screenshot...
[[07:37:06]] [SUCCESS] Screenshot refreshed successfully
[[07:37:06]] [SUCCESS] Screenshot refreshed successfully
[[07:37:06]] [INFO] Executing Multi Step action step 23/52: Swipe from (50%, 70%) to (50%, 40%)
[[07:37:05]] [SUCCESS] Screenshot refreshed
[[07:37:05]] [INFO] Refreshing screenshot...
[[07:37:03]] [SUCCESS] Screenshot refreshed successfully
[[07:37:03]] [SUCCESS] Screenshot refreshed successfully
[[07:37:03]] [INFO] Executing Multi Step action step 22/52: Android Function: send_key_event - Key Event: ENTER
[[07:37:02]] [SUCCESS] Screenshot refreshed
[[07:37:02]] [INFO] Refreshing screenshot...
[[07:36:56]] [SUCCESS] Screenshot refreshed successfully
[[07:36:56]] [SUCCESS] Screenshot refreshed successfully
[[07:36:56]] [INFO] Executing Multi Step action step 21/52: Tap on image: delivery-address-options-android.png
[[07:36:56]] [SUCCESS] Screenshot refreshed
[[07:36:56]] [INFO] Refreshing screenshot...
[[07:36:54]] [SUCCESS] Screenshot refreshed successfully
[[07:36:54]] [SUCCESS] Screenshot refreshed successfully
[[07:36:53]] [INFO] Executing Multi Step action step 20/52: Input text: "305 238 Flinders Street"
[[07:36:53]] [SUCCESS] Screenshot refreshed
[[07:36:53]] [INFO] Refreshing screenshot...
[[07:36:49]] [SUCCESS] Screenshot refreshed successfully
[[07:36:49]] [SUCCESS] Screenshot refreshed successfully
[[07:36:49]] [INFO] Executing Multi Step action step 19/52: Tap on Text: "address"
[[07:36:48]] [SUCCESS] Screenshot refreshed
[[07:36:48]] [INFO] Refreshing screenshot...
[[07:36:44]] [SUCCESS] Screenshot refreshed successfully
[[07:36:44]] [SUCCESS] Screenshot refreshed successfully
[[07:36:44]] [INFO] Executing Multi Step action step 18/52: Tap on Text: "Continue"
[[07:36:44]] [SUCCESS] Screenshot refreshed
[[07:36:44]] [INFO] Refreshing screenshot...
[[07:36:40]] [SUCCESS] Screenshot refreshed successfully
[[07:36:40]] [SUCCESS] Screenshot refreshed successfully
[[07:36:40]] [INFO] Executing Multi Step action step 17/52: Swipe from (50%, 70%) to (50%, 50%)
[[07:36:39]] [SUCCESS] Screenshot refreshed
[[07:36:39]] [INFO] Refreshing screenshot...
[[07:36:37]] [SUCCESS] Screenshot refreshed successfully
[[07:36:37]] [SUCCESS] Screenshot refreshed successfully
[[07:36:37]] [INFO] Executing Multi Step action step 16/52: Input text: "0400000000"
[[07:36:37]] [SUCCESS] Screenshot refreshed
[[07:36:37]] [INFO] Refreshing screenshot...
[[07:36:35]] [SUCCESS] Screenshot refreshed successfully
[[07:36:35]] [SUCCESS] Screenshot refreshed successfully
[[07:36:35]] [INFO] Executing Multi Step action step 15/52: Clear Text (auto)
[[07:36:34]] [SUCCESS] Screenshot refreshed
[[07:36:34]] [INFO] Refreshing screenshot...
[[07:36:30]] [SUCCESS] Screenshot refreshed successfully
[[07:36:30]] [SUCCESS] Screenshot refreshed successfully
[[07:36:30]] [INFO] Executing Multi Step action step 14/52: Tap on Text: "Mobile"
[[07:36:30]] [SUCCESS] Screenshot refreshed
[[07:36:30]] [INFO] Refreshing screenshot...
[[07:36:28]] [SUCCESS] Screenshot refreshed successfully
[[07:36:28]] [SUCCESS] Screenshot refreshed successfully
[[07:36:27]] [INFO] Executing Multi Step action step 13/52: Input text: "<EMAIL>"
[[07:36:27]] [SUCCESS] Screenshot refreshed
[[07:36:27]] [INFO] Refreshing screenshot...
[[07:36:25]] [SUCCESS] Screenshot refreshed successfully
[[07:36:25]] [SUCCESS] Screenshot refreshed successfully
[[07:36:25]] [INFO] Executing Multi Step action step 12/52: Clear Text (auto)
[[07:36:24]] [SUCCESS] Screenshot refreshed
[[07:36:24]] [INFO] Refreshing screenshot...
[[07:36:20]] [SUCCESS] Screenshot refreshed successfully
[[07:36:20]] [SUCCESS] Screenshot refreshed successfully
[[07:36:20]] [INFO] Executing Multi Step action step 11/52: Tap on Text: "Email"
[[07:36:19]] [SUCCESS] Screenshot refreshed
[[07:36:19]] [INFO] Refreshing screenshot...
[[07:36:18]] [SUCCESS] Screenshot refreshed successfully
[[07:36:18]] [SUCCESS] Screenshot refreshed successfully
[[07:36:17]] [INFO] Executing Multi Step action step 10/52: Input text: "LastName"
[[07:36:17]] [SUCCESS] Screenshot refreshed
[[07:36:17]] [INFO] Refreshing screenshot...
[[07:36:15]] [SUCCESS] Screenshot refreshed successfully
[[07:36:15]] [SUCCESS] Screenshot refreshed successfully
[[07:36:15]] [INFO] Executing Multi Step action step 9/52: Clear Text (auto)
[[07:36:14]] [SUCCESS] Screenshot refreshed
[[07:36:14]] [INFO] Refreshing screenshot...
[[07:36:11]] [SUCCESS] Screenshot refreshed successfully
[[07:36:11]] [SUCCESS] Screenshot refreshed successfully
[[07:36:10]] [INFO] Executing Multi Step action step 8/52: Tap on Text: "Last"
[[07:36:10]] [SUCCESS] Screenshot refreshed
[[07:36:10]] [INFO] Refreshing screenshot...
[[07:36:07]] [SUCCESS] Screenshot refreshed successfully
[[07:36:07]] [SUCCESS] Screenshot refreshed successfully
[[07:36:07]] [INFO] Executing Multi Step action step 7/52: Input text: "FirstName"
[[07:36:07]] [SUCCESS] Screenshot refreshed
[[07:36:07]] [INFO] Refreshing screenshot...
[[07:36:05]] [SUCCESS] Screenshot refreshed successfully
[[07:36:05]] [SUCCESS] Screenshot refreshed successfully
[[07:36:04]] [INFO] Executing Multi Step action step 6/52: Clear Text (auto)
[[07:36:04]] [SUCCESS] Screenshot refreshed
[[07:36:04]] [INFO] Refreshing screenshot...
[[07:36:00]] [SUCCESS] Screenshot refreshed successfully
[[07:36:00]] [SUCCESS] Screenshot refreshed successfully
[[07:36:00]] [INFO] Executing Multi Step action step 5/52: Tap on Text: "First"
[[07:36:00]] [SUCCESS] Screenshot refreshed
[[07:36:00]] [INFO] Refreshing screenshot...
[[07:35:53]] [SUCCESS] Screenshot refreshed successfully
[[07:35:53]] [SUCCESS] Screenshot refreshed successfully
[[07:35:53]] [INFO] Executing Multi Step action step 4/52: Tap on image: continue-to-details-android.png
[[07:35:53]] [SUCCESS] Screenshot refreshed
[[07:35:53]] [INFO] Refreshing screenshot...
[[07:35:47]] [SUCCESS] Screenshot refreshed successfully
[[07:35:47]] [SUCCESS] Screenshot refreshed successfully
[[07:35:47]] [INFO] Executing Multi Step action step 3/52: Swipe from (50%, 70%) to (50%, 30%)
[[07:35:47]] [SUCCESS] Screenshot refreshed
[[07:35:47]] [INFO] Refreshing screenshot...
[[07:35:45]] [SUCCESS] Screenshot refreshed successfully
[[07:35:45]] [SUCCESS] Screenshot refreshed successfully
[[07:35:44]] [INFO] Executing Multi Step action step 2/52: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[07:35:44]] [SUCCESS] Screenshot refreshed
[[07:35:44]] [INFO] Refreshing screenshot...
[[07:35:39]] [SUCCESS] Screenshot refreshed successfully
[[07:35:39]] [SUCCESS] Screenshot refreshed successfully
[[07:35:38]] [INFO] Executing Multi Step action step 1/52: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[07:35:38]] [INFO] Loaded 52 steps from test case: Delivery Buy Steps_AU-ANDROID
[[07:35:38]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[07:35:38]] [INFO] 3MoIdpOeEI=running
[[07:35:38]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (52 steps)
[[07:35:38]] [SUCCESS] Screenshot refreshed
[[07:35:38]] [INFO] Refreshing screenshot...
[[07:35:38]] [INFO] zrdO3PVkX3=pass
[[07:35:35]] [SUCCESS] Screenshot refreshed successfully
[[07:35:35]] [SUCCESS] Screenshot refreshed successfully
[[07:35:35]] [INFO] zrdO3PVkX3=running
[[07:35:35]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[07:35:34]] [SUCCESS] Screenshot refreshed
[[07:35:34]] [INFO] Refreshing screenshot...
[[07:35:34]] [INFO] F1olhgKhUt=pass
[[07:35:32]] [SUCCESS] Screenshot refreshed successfully
[[07:35:32]] [SUCCESS] Screenshot refreshed successfully
[[07:35:31]] [INFO] F1olhgKhUt=running
[[07:35:31]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[07:35:31]] [SUCCESS] Screenshot refreshed
[[07:35:31]] [INFO] Refreshing screenshot...
[[07:35:31]] [INFO] FnrbyHq7bU=pass
[[07:35:28]] [SUCCESS] Screenshot refreshed successfully
[[07:35:28]] [SUCCESS] Screenshot refreshed successfully
[[07:35:27]] [INFO] FnrbyHq7bU=running
[[07:35:27]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[07:35:26]] [SUCCESS] Screenshot refreshed
[[07:35:26]] [INFO] Refreshing screenshot...
[[07:35:26]] [INFO] nAB6Q8LAdv=pass
[[07:35:19]] [SUCCESS] Screenshot refreshed successfully
[[07:35:19]] [SUCCESS] Screenshot refreshed successfully
[[07:35:19]] [INFO] nAB6Q8LAdv=running
[[07:35:19]] [INFO] Executing action 23/27: Wait till text appears: "Filter"
[[07:35:19]] [SUCCESS] Screenshot refreshed
[[07:35:19]] [INFO] Refreshing screenshot...
[[07:35:19]] [INFO] JRheeTvpJf=pass
[[07:35:16]] [SUCCESS] Screenshot refreshed successfully
[[07:35:16]] [SUCCESS] Screenshot refreshed successfully
[[07:35:16]] [INFO] JRheeTvpJf=running
[[07:35:16]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[07:35:15]] [SUCCESS] Screenshot refreshed
[[07:35:15]] [INFO] Refreshing screenshot...
[[07:35:15]] [INFO] o1gHFWhXTL=pass
[[07:35:10]] [SUCCESS] Screenshot refreshed successfully
[[07:35:10]] [SUCCESS] Screenshot refreshed successfully
[[07:35:09]] [INFO] o1gHFWhXTL=running
[[07:35:09]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[07:35:09]] [SUCCESS] Screenshot refreshed
[[07:35:09]] [INFO] Refreshing screenshot...
[[07:35:09]] [INFO] cKNu2QoRC1=pass
[[07:35:06]] [SUCCESS] Screenshot refreshed successfully
[[07:35:06]] [SUCCESS] Screenshot refreshed successfully
[[07:35:05]] [INFO] cKNu2QoRC1=running
[[07:35:05]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[07:35:05]] [SUCCESS] Screenshot refreshed
[[07:35:05]] [INFO] Refreshing screenshot...
[[07:35:05]] [INFO] OyUowAaBzD=pass
[[07:35:02]] [SUCCESS] Screenshot refreshed successfully
[[07:35:02]] [SUCCESS] Screenshot refreshed successfully
[[07:35:02]] [INFO] OyUowAaBzD=running
[[07:35:02]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[07:35:01]] [SUCCESS] Screenshot refreshed
[[07:35:01]] [INFO] Refreshing screenshot...
[[07:35:01]] [INFO] Ob26qqcA0p=pass
[[07:34:56]] [SUCCESS] Screenshot refreshed successfully
[[07:34:56]] [SUCCESS] Screenshot refreshed successfully
[[07:34:55]] [INFO] Ob26qqcA0p=running
[[07:34:55]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[07:34:55]] [SUCCESS] Screenshot refreshed
[[07:34:55]] [INFO] Refreshing screenshot...
[[07:34:55]] [INFO] k3mu9Mt7Ec=pass
[[07:34:51]] [SUCCESS] Screenshot refreshed successfully
[[07:34:51]] [SUCCESS] Screenshot refreshed successfully
[[07:34:51]] [INFO] k3mu9Mt7Ec=running
[[07:34:51]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[07:34:51]] [SUCCESS] Screenshot refreshed
[[07:34:51]] [INFO] Refreshing screenshot...
[[07:34:51]] [INFO] FFM0CCo6Qg=pass
[[07:34:45]] [SUCCESS] Screenshot refreshed successfully
[[07:34:45]] [SUCCESS] Screenshot refreshed successfully
[[07:34:45]] [INFO] FFM0CCo6Qg=running
[[07:34:45]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[07:34:44]] [SUCCESS] Screenshot refreshed
[[07:34:44]] [INFO] Refreshing screenshot...
[[07:34:44]] [INFO] LWXWKZE4UV=pass
[[07:34:29]] [SUCCESS] Screenshot refreshed successfully
[[07:34:29]] [SUCCESS] Screenshot refreshed successfully
[[07:34:29]] [INFO] LWXWKZE4UV=running
[[07:34:29]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[07:34:28]] [SUCCESS] Screenshot refreshed
[[07:34:28]] [INFO] Refreshing screenshot...
[[07:34:28]] [INFO] Qbg9bipTGs=pass
[[07:34:23]] [SUCCESS] Screenshot refreshed successfully
[[07:34:23]] [SUCCESS] Screenshot refreshed successfully
[[07:34:23]] [INFO] Qbg9bipTGs=running
[[07:34:23]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[07:34:22]] [SUCCESS] Screenshot refreshed
[[07:34:22]] [INFO] Refreshing screenshot...
[[07:34:22]] [INFO] 7SpDO20tS2=pass
[[07:34:11]] [SUCCESS] Screenshot refreshed successfully
[[07:34:11]] [SUCCESS] Screenshot refreshed successfully
[[07:34:11]] [INFO] 7SpDO20tS2=running
[[07:34:11]] [INFO] Executing action 13/27: Wait for 10 ms
[[07:34:10]] [SUCCESS] Screenshot refreshed
[[07:34:10]] [INFO] Refreshing screenshot...
[[07:34:10]] [INFO] drbQBpgBfM=pass
[[07:34:07]] [SUCCESS] Screenshot refreshed successfully
[[07:34:07]] [SUCCESS] Screenshot refreshed successfully
[[07:34:07]] [INFO] drbQBpgBfM=running
[[07:34:07]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[07:34:06]] [SUCCESS] Screenshot refreshed
[[07:34:06]] [INFO] Refreshing screenshot...
[[07:34:06]] [INFO] F1olhgKhUt=pass
[[07:34:04]] [SUCCESS] Screenshot refreshed successfully
[[07:34:04]] [SUCCESS] Screenshot refreshed successfully
[[07:34:03]] [INFO] F1olhgKhUt=running
[[07:34:03]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[07:34:03]] [SUCCESS] Screenshot refreshed
[[07:34:03]] [INFO] Refreshing screenshot...
[[07:34:03]] [INFO] FnrbyHq7bU=pass
[[07:34:00]] [SUCCESS] Screenshot refreshed successfully
[[07:34:00]] [SUCCESS] Screenshot refreshed successfully
[[07:33:59]] [INFO] FnrbyHq7bU=running
[[07:33:59]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[07:33:58]] [SUCCESS] Screenshot refreshed
[[07:33:58]] [INFO] Refreshing screenshot...
[[07:33:58]] [INFO] nAB6Q8LAdv=pass
[[07:33:52]] [SUCCESS] Screenshot refreshed successfully
[[07:33:52]] [SUCCESS] Screenshot refreshed successfully
[[07:33:51]] [INFO] nAB6Q8LAdv=running
[[07:33:51]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[07:33:51]] [SUCCESS] Screenshot refreshed
[[07:33:51]] [INFO] Refreshing screenshot...
[[07:33:51]] [INFO] JRheeTvpJf=pass
[[07:33:49]] [SUCCESS] Screenshot refreshed successfully
[[07:33:49]] [SUCCESS] Screenshot refreshed successfully
[[07:33:49]] [INFO] JRheeTvpJf=running
[[07:33:49]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[07:33:48]] [SUCCESS] Screenshot refreshed
[[07:33:48]] [INFO] Refreshing screenshot...
[[07:33:48]] [INFO] o1gHFWhXTL=pass
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:43]] [INFO] o1gHFWhXTL=running
[[07:33:43]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[07:33:42]] [SUCCESS] Screenshot refreshed
[[07:33:42]] [INFO] Refreshing screenshot...
[[07:33:42]] [INFO] RLznb7o3ag=pass
[[07:33:38]] [SUCCESS] Screenshot refreshed successfully
[[07:33:38]] [SUCCESS] Screenshot refreshed successfully
[[07:33:36]] [INFO] RLznb7o3ag=running
[[07:33:36]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[07:33:36]] [SUCCESS] Screenshot refreshed
[[07:33:36]] [INFO] Refreshing screenshot...
[[07:33:36]] [SUCCESS] Screenshot refreshed
[[07:33:36]] [INFO] Refreshing screenshot...
[[07:33:33]] [SUCCESS] Screenshot refreshed successfully
[[07:33:33]] [SUCCESS] Screenshot refreshed successfully
[[07:33:33]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[07:33:33]] [SUCCESS] Screenshot refreshed
[[07:33:33]] [INFO] Refreshing screenshot...
[[07:33:30]] [SUCCESS] Screenshot refreshed successfully
[[07:33:30]] [SUCCESS] Screenshot refreshed successfully
[[07:33:30]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[07:33:30]] [SUCCESS] Screenshot refreshed
[[07:33:30]] [INFO] Refreshing screenshot...
[[07:33:27]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[07:33:27]] [SUCCESS] Screenshot refreshed successfully
[[07:33:27]] [SUCCESS] Screenshot refreshed successfully
[[07:33:27]] [SUCCESS] Screenshot refreshed
[[07:33:27]] [INFO] Refreshing screenshot...
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:25]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[07:33:24]] [SUCCESS] Screenshot refreshed
[[07:33:24]] [INFO] Refreshing screenshot...
[[07:33:22]] [SUCCESS] Screenshot refreshed successfully
[[07:33:22]] [SUCCESS] Screenshot refreshed successfully
[[07:33:22]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:33:22]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[07:33:22]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[07:33:22]] [INFO] g052Oo1Gcl=running
[[07:33:22]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[07:33:22]] [SUCCESS] Screenshot refreshed
[[07:33:22]] [INFO] Refreshing screenshot...
[[07:33:22]] [INFO] J9loj6Zs95K=pass
[[07:33:19]] [SUCCESS] Screenshot refreshed successfully
[[07:33:19]] [SUCCESS] Screenshot refreshed successfully
[[07:33:19]] [INFO] J9loj6Zs95K=running
[[07:33:19]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:33:19]] [SUCCESS] Screenshot refreshed
[[07:33:19]] [INFO] Refreshing screenshot...
[[07:33:18]] [INFO] Y8v5g7AJD1i=pass
[[07:33:12]] [INFO] Y8v5g7AJD1i=running
[[07:33:12]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:33:12]] [SUCCESS] Screenshot refreshed successfully
[[07:33:12]] [SUCCESS] Screenshot refreshed successfully
[[07:33:12]] [SUCCESS] Screenshot refreshed
[[07:33:12]] [INFO] Refreshing screenshot...
[[07:33:12]] [INFO] eqHB0Nj1He=pass
[[07:33:11]] [SUCCESS] Screenshot refreshed successfully
[[07:33:11]] [SUCCESS] Screenshot refreshed successfully
[[07:33:09]] [INFO] eqHB0Nj1He=running
[[07:33:09]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[07:33:08]] [SUCCESS] Screenshot refreshed
[[07:33:08]] [INFO] Refreshing screenshot...
[[07:33:08]] [INFO] H9fkkqcFbZ=pass
[[07:33:04]] [INFO] H9fkkqcFbZ=running
[[07:33:04]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[07:33:04]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[07:33:04]] [SUCCESS] Cleared 108 screenshots from database
[[07:33:04]] [INFO] Clearing screenshots from database before execution...
[[07:33:04]] [SUCCESS] All screenshots deleted successfully
[[07:33:04]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:33:04]] [INFO] Skipping report initialization - single test case execution
[[07:30:32]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[07:30:32]] [SUCCESS] Action logs saved successfully
[[07:30:32]] [SUCCESS] Execution completed successfully.
[[07:30:32]] [INFO] Saving 375 action log entries to file...
[[07:30:32]] [INFO] Generating execution report...
[[07:30:32]] [SUCCESS] All tests passed successfully!
[[07:30:32]] [INFO] Moving to the next test case after failure (server will handle retry)
[[07:30:32]] [ERROR] Multi Step action step 24 failed: Text 'Continue' not found within timeout (30s)
[[07:29:59]] [SUCCESS] Screenshot refreshed successfully
[[07:29:59]] [SUCCESS] Screenshot refreshed successfully
[[07:29:59]] [INFO] Executing Multi Step action step 24/52: Tap on Text: "Continue"
[[07:29:59]] [SUCCESS] Screenshot refreshed
[[07:29:59]] [INFO] Refreshing screenshot...
[[07:29:53]] [SUCCESS] Screenshot refreshed successfully
[[07:29:53]] [SUCCESS] Screenshot refreshed successfully
[[07:29:53]] [INFO] Executing Multi Step action step 23/52: Swipe from (50%, 70%) to (50%, 40%)
[[07:29:53]] [SUCCESS] Screenshot refreshed
[[07:29:53]] [INFO] Refreshing screenshot...
[[07:29:51]] [SUCCESS] Screenshot refreshed successfully
[[07:29:51]] [SUCCESS] Screenshot refreshed successfully
[[07:29:51]] [INFO] Executing Multi Step action step 22/52: Android Function: send_key_event - Key Event: ENTER
[[07:29:50]] [SUCCESS] Screenshot refreshed
[[07:29:50]] [INFO] Refreshing screenshot...
[[07:29:44]] [SUCCESS] Screenshot refreshed successfully
[[07:29:44]] [SUCCESS] Screenshot refreshed successfully
[[07:29:44]] [INFO] Executing Multi Step action step 21/52: Tap on image: delivery-address-options-android.png
[[07:29:43]] [SUCCESS] Screenshot refreshed
[[07:29:43]] [INFO] Refreshing screenshot...
[[07:29:41]] [SUCCESS] Screenshot refreshed successfully
[[07:29:41]] [SUCCESS] Screenshot refreshed successfully
[[07:29:41]] [INFO] Executing Multi Step action step 20/52: Input text: "305 238 Flinders Street"
[[07:29:41]] [SUCCESS] Screenshot refreshed
[[07:29:41]] [INFO] Refreshing screenshot...
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [INFO] Executing Multi Step action step 19/52: Tap on Text: "address"
[[07:29:37]] [SUCCESS] Screenshot refreshed
[[07:29:37]] [INFO] Refreshing screenshot...
[[07:29:33]] [SUCCESS] Screenshot refreshed successfully
[[07:29:33]] [SUCCESS] Screenshot refreshed successfully
[[07:29:33]] [INFO] Executing Multi Step action step 18/52: Tap on Text: "Continue"
[[07:29:32]] [SUCCESS] Screenshot refreshed
[[07:29:32]] [INFO] Refreshing screenshot...
[[07:29:29]] [SUCCESS] Screenshot refreshed successfully
[[07:29:29]] [SUCCESS] Screenshot refreshed successfully
[[07:29:28]] [INFO] Executing Multi Step action step 17/52: Swipe from (50%, 70%) to (50%, 50%)
[[07:29:28]] [SUCCESS] Screenshot refreshed
[[07:29:28]] [INFO] Refreshing screenshot...
[[07:29:26]] [SUCCESS] Screenshot refreshed successfully
[[07:29:26]] [SUCCESS] Screenshot refreshed successfully
[[07:29:26]] [INFO] Executing Multi Step action step 16/52: Input text: "0400000000"
[[07:29:25]] [SUCCESS] Screenshot refreshed
[[07:29:25]] [INFO] Refreshing screenshot...
[[07:29:23]] [SUCCESS] Screenshot refreshed successfully
[[07:29:23]] [SUCCESS] Screenshot refreshed successfully
[[07:29:23]] [INFO] Executing Multi Step action step 15/52: Clear Text (auto)
[[07:29:23]] [SUCCESS] Screenshot refreshed
[[07:29:23]] [INFO] Refreshing screenshot...
[[07:29:19]] [SUCCESS] Screenshot refreshed successfully
[[07:29:19]] [SUCCESS] Screenshot refreshed successfully
[[07:29:19]] [INFO] Executing Multi Step action step 14/52: Tap on Text: "Mobile"
[[07:29:18]] [SUCCESS] Screenshot refreshed
[[07:29:18]] [INFO] Refreshing screenshot...
[[07:29:16]] [SUCCESS] Screenshot refreshed successfully
[[07:29:16]] [SUCCESS] Screenshot refreshed successfully
[[07:29:16]] [INFO] Executing Multi Step action step 13/52: Input text: "<EMAIL>"
[[07:29:16]] [SUCCESS] Screenshot refreshed
[[07:29:16]] [INFO] Refreshing screenshot...
[[07:29:14]] [SUCCESS] Screenshot refreshed successfully
[[07:29:14]] [SUCCESS] Screenshot refreshed successfully
[[07:29:13]] [INFO] Executing Multi Step action step 12/52: Clear Text (auto)
[[07:29:13]] [SUCCESS] Screenshot refreshed
[[07:29:13]] [INFO] Refreshing screenshot...
[[07:29:08]] [SUCCESS] Screenshot refreshed successfully
[[07:29:08]] [SUCCESS] Screenshot refreshed successfully
[[07:29:08]] [INFO] Executing Multi Step action step 11/52: Tap on Text: "Email"
[[07:29:07]] [SUCCESS] Screenshot refreshed
[[07:29:07]] [INFO] Refreshing screenshot...
[[07:29:06]] [SUCCESS] Screenshot refreshed successfully
[[07:29:06]] [SUCCESS] Screenshot refreshed successfully
[[07:29:05]] [INFO] Executing Multi Step action step 10/52: Input text: "LastName"
[[07:29:05]] [SUCCESS] Screenshot refreshed
[[07:29:05]] [INFO] Refreshing screenshot...
[[07:29:03]] [SUCCESS] Screenshot refreshed successfully
[[07:29:03]] [SUCCESS] Screenshot refreshed successfully
[[07:29:03]] [INFO] Executing Multi Step action step 9/52: Clear Text (auto)
[[07:29:02]] [SUCCESS] Screenshot refreshed
[[07:29:02]] [INFO] Refreshing screenshot...
[[07:28:30]] [SUCCESS] Screenshot refreshed successfully
[[07:28:30]] [SUCCESS] Screenshot refreshed successfully
[[07:28:30]] [INFO] Executing Multi Step action step 8/52: Tap on Text: "Last"
[[07:28:29]] [SUCCESS] Screenshot refreshed
[[07:28:29]] [INFO] Refreshing screenshot...
[[07:28:27]] [SUCCESS] Screenshot refreshed successfully
[[07:28:27]] [SUCCESS] Screenshot refreshed successfully
[[07:28:27]] [INFO] Executing Multi Step action step 7/52: Input text: "FirstName"
[[07:28:26]] [SUCCESS] Screenshot refreshed
[[07:28:26]] [INFO] Refreshing screenshot...
[[07:28:24]] [SUCCESS] Screenshot refreshed successfully
[[07:28:24]] [SUCCESS] Screenshot refreshed successfully
[[07:28:24]] [INFO] Executing Multi Step action step 6/52: Clear Text (auto)
[[07:28:23]] [SUCCESS] Screenshot refreshed
[[07:28:23]] [INFO] Refreshing screenshot...
[[07:28:09]] [INFO] Executing Multi Step action step 5/52: Tap on Text: "First"
[[07:28:09]] [SUCCESS] Screenshot refreshed successfully
[[07:28:09]] [SUCCESS] Screenshot refreshed successfully
[[07:28:08]] [SUCCESS] Screenshot refreshed
[[07:28:08]] [INFO] Refreshing screenshot...
[[07:28:02]] [SUCCESS] Screenshot refreshed successfully
[[07:28:02]] [SUCCESS] Screenshot refreshed successfully
[[07:28:02]] [INFO] Executing Multi Step action step 4/52: Tap on image: continue-to-details-android.png
[[07:28:01]] [SUCCESS] Screenshot refreshed
[[07:28:01]] [INFO] Refreshing screenshot...
[[07:27:19]] [SUCCESS] Screenshot refreshed successfully
[[07:27:19]] [SUCCESS] Screenshot refreshed successfully
[[07:27:19]] [INFO] Executing Multi Step action step 3/52: Swipe from (50%, 70%) to (50%, 30%)
[[07:27:18]] [SUCCESS] Screenshot refreshed
[[07:27:18]] [INFO] Refreshing screenshot...
[[07:27:16]] [SUCCESS] Screenshot refreshed successfully
[[07:27:16]] [SUCCESS] Screenshot refreshed successfully
[[07:27:16]] [INFO] Executing Multi Step action step 2/52: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[07:27:15]] [SUCCESS] Screenshot refreshed
[[07:27:15]] [INFO] Refreshing screenshot...
[[07:27:10]] [INFO] Executing Multi Step action step 1/52: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[07:27:10]] [INFO] Loaded 52 steps from test case: Delivery Buy Steps_AU-ANDROID
[[07:27:10]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[07:27:10]] [INFO] 3MoIdpOeEI=running
[[07:27:10]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (52 steps)
[[07:27:10]] [SUCCESS] Screenshot refreshed successfully
[[07:27:10]] [SUCCESS] Screenshot refreshed successfully
[[07:27:10]] [SUCCESS] Screenshot refreshed
[[07:27:10]] [INFO] Refreshing screenshot...
[[07:27:10]] [INFO] zrdO3PVkX3=pass
[[07:27:06]] [SUCCESS] Screenshot refreshed successfully
[[07:27:06]] [SUCCESS] Screenshot refreshed successfully
[[07:27:06]] [INFO] zrdO3PVkX3=running
[[07:27:06]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[07:27:06]] [SUCCESS] Screenshot refreshed
[[07:27:06]] [INFO] Refreshing screenshot...
[[07:27:06]] [INFO] F1olhgKhUt=pass
[[07:27:02]] [SUCCESS] Screenshot refreshed successfully
[[07:27:02]] [SUCCESS] Screenshot refreshed successfully
[[07:27:01]] [INFO] F1olhgKhUt=running
[[07:27:01]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[07:27:01]] [SUCCESS] Screenshot refreshed
[[07:27:01]] [INFO] Refreshing screenshot...
[[07:27:01]] [INFO] FnrbyHq7bU=pass
[[07:26:58]] [SUCCESS] Screenshot refreshed successfully
[[07:26:58]] [SUCCESS] Screenshot refreshed successfully
[[07:26:57]] [INFO] FnrbyHq7bU=running
[[07:26:57]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[07:26:57]] [SUCCESS] Screenshot refreshed
[[07:26:57]] [INFO] Refreshing screenshot...
[[07:26:57]] [INFO] nAB6Q8LAdv=pass
[[07:26:51]] [SUCCESS] Screenshot refreshed successfully
[[07:26:51]] [SUCCESS] Screenshot refreshed successfully
[[07:26:50]] [INFO] nAB6Q8LAdv=running
[[07:26:50]] [INFO] Executing action 23/27: Wait till text appears: "Filter"
[[07:26:50]] [SUCCESS] Screenshot refreshed
[[07:26:50]] [INFO] Refreshing screenshot...
[[07:26:50]] [INFO] JRheeTvpJf=pass
[[07:26:47]] [SUCCESS] Screenshot refreshed successfully
[[07:26:47]] [SUCCESS] Screenshot refreshed successfully
[[07:26:47]] [INFO] JRheeTvpJf=running
[[07:26:47]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[07:26:46]] [SUCCESS] Screenshot refreshed
[[07:26:46]] [INFO] Refreshing screenshot...
[[07:26:46]] [INFO] o1gHFWhXTL=pass
[[07:26:42]] [SUCCESS] Screenshot refreshed successfully
[[07:26:42]] [SUCCESS] Screenshot refreshed successfully
[[07:26:41]] [INFO] o1gHFWhXTL=running
[[07:26:41]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[07:26:40]] [SUCCESS] Screenshot refreshed
[[07:26:40]] [INFO] Refreshing screenshot...
[[07:26:40]] [INFO] cKNu2QoRC1=pass
[[07:26:37]] [SUCCESS] Screenshot refreshed successfully
[[07:26:37]] [SUCCESS] Screenshot refreshed successfully
[[07:26:36]] [INFO] cKNu2QoRC1=running
[[07:26:36]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[07:26:36]] [SUCCESS] Screenshot refreshed
[[07:26:36]] [INFO] Refreshing screenshot...
[[07:26:36]] [INFO] OyUowAaBzD=pass
[[07:26:31]] [SUCCESS] Screenshot refreshed successfully
[[07:26:31]] [SUCCESS] Screenshot refreshed successfully
[[07:26:31]] [INFO] OyUowAaBzD=running
[[07:26:31]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[07:26:31]] [SUCCESS] Screenshot refreshed
[[07:26:31]] [INFO] Refreshing screenshot...
[[07:26:31]] [INFO] Ob26qqcA0p=pass
[[07:26:25]] [SUCCESS] Screenshot refreshed successfully
[[07:26:25]] [SUCCESS] Screenshot refreshed successfully
[[07:26:25]] [INFO] Ob26qqcA0p=running
[[07:26:25]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[07:26:25]] [SUCCESS] Screenshot refreshed
[[07:26:25]] [INFO] Refreshing screenshot...
[[07:26:25]] [INFO] k3mu9Mt7Ec=pass
[[07:26:21]] [SUCCESS] Screenshot refreshed successfully
[[07:26:21]] [SUCCESS] Screenshot refreshed successfully
[[07:26:21]] [INFO] k3mu9Mt7Ec=running
[[07:26:21]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[07:26:20]] [SUCCESS] Screenshot refreshed
[[07:26:20]] [INFO] Refreshing screenshot...
[[07:26:20]] [INFO] FFM0CCo6Qg=pass
[[07:26:15]] [SUCCESS] Screenshot refreshed successfully
[[07:26:15]] [SUCCESS] Screenshot refreshed successfully
[[07:26:15]] [INFO] FFM0CCo6Qg=running
[[07:26:15]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[07:26:14]] [SUCCESS] Screenshot refreshed
[[07:26:14]] [INFO] Refreshing screenshot...
[[07:26:14]] [INFO] LWXWKZE4UV=pass
[[07:26:02]] [SUCCESS] Screenshot refreshed successfully
[[07:26:02]] [SUCCESS] Screenshot refreshed successfully
[[07:26:01]] [INFO] LWXWKZE4UV=running
[[07:26:01]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[07:26:01]] [SUCCESS] Screenshot refreshed
[[07:26:01]] [INFO] Refreshing screenshot...
[[07:26:01]] [INFO] Qbg9bipTGs=pass
[[07:25:48]] [SUCCESS] Screenshot refreshed successfully
[[07:25:48]] [SUCCESS] Screenshot refreshed successfully
[[07:25:47]] [INFO] Qbg9bipTGs=running
[[07:25:47]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[07:25:47]] [SUCCESS] Screenshot refreshed
[[07:25:47]] [INFO] Refreshing screenshot...
[[07:25:47]] [INFO] 7SpDO20tS2=pass
[[07:25:35]] [SUCCESS] Screenshot refreshed successfully
[[07:25:35]] [SUCCESS] Screenshot refreshed successfully
[[07:25:35]] [INFO] 7SpDO20tS2=running
[[07:25:35]] [INFO] Executing action 13/27: Wait for 10 ms
[[07:25:35]] [SUCCESS] Screenshot refreshed
[[07:25:35]] [INFO] Refreshing screenshot...
[[07:25:35]] [INFO] drbQBpgBfM=pass
[[07:25:31]] [SUCCESS] Screenshot refreshed successfully
[[07:25:31]] [SUCCESS] Screenshot refreshed successfully
[[07:25:31]] [INFO] drbQBpgBfM=running
[[07:25:31]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[07:25:30]] [SUCCESS] Screenshot refreshed
[[07:25:30]] [INFO] Refreshing screenshot...
[[07:25:30]] [INFO] F1olhgKhUt=pass
[[07:25:28]] [SUCCESS] Screenshot refreshed successfully
[[07:25:28]] [SUCCESS] Screenshot refreshed successfully
[[07:25:27]] [INFO] F1olhgKhUt=running
[[07:25:27]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[07:25:27]] [SUCCESS] Screenshot refreshed
[[07:25:27]] [INFO] Refreshing screenshot...
[[07:25:27]] [INFO] FnrbyHq7bU=pass
[[07:25:24]] [SUCCESS] Screenshot refreshed successfully
[[07:25:24]] [SUCCESS] Screenshot refreshed successfully
[[07:25:23]] [INFO] FnrbyHq7bU=running
[[07:25:23]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[07:25:22]] [SUCCESS] Screenshot refreshed
[[07:25:22]] [INFO] Refreshing screenshot...
[[07:25:22]] [INFO] nAB6Q8LAdv=pass
[[07:25:09]] [SUCCESS] Screenshot refreshed successfully
[[07:25:09]] [SUCCESS] Screenshot refreshed successfully
[[07:25:09]] [INFO] nAB6Q8LAdv=running
[[07:25:09]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[07:25:08]] [SUCCESS] Screenshot refreshed
[[07:25:08]] [INFO] Refreshing screenshot...
[[07:25:08]] [INFO] JRheeTvpJf=pass
[[07:25:07]] [SUCCESS] Screenshot refreshed successfully
[[07:25:07]] [SUCCESS] Screenshot refreshed successfully
[[07:25:06]] [INFO] JRheeTvpJf=running
[[07:25:06]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[07:25:06]] [SUCCESS] Screenshot refreshed
[[07:25:06]] [INFO] Refreshing screenshot...
[[07:25:06]] [INFO] o1gHFWhXTL=pass
[[07:25:01]] [SUCCESS] Screenshot refreshed successfully
[[07:25:01]] [SUCCESS] Screenshot refreshed successfully
[[07:25:00]] [INFO] o1gHFWhXTL=running
[[07:25:00]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[07:25:00]] [SUCCESS] Screenshot refreshed
[[07:25:00]] [INFO] Refreshing screenshot...
[[07:25:00]] [INFO] RLznb7o3ag=pass
[[07:24:55]] [SUCCESS] Screenshot refreshed successfully
[[07:24:55]] [SUCCESS] Screenshot refreshed successfully
[[07:24:53]] [INFO] RLznb7o3ag=running
[[07:24:53]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[07:24:53]] [SUCCESS] Screenshot refreshed
[[07:24:53]] [INFO] Refreshing screenshot...
[[07:24:53]] [SUCCESS] Screenshot refreshed
[[07:24:53]] [INFO] Refreshing screenshot...
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:50]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[07:24:50]] [SUCCESS] Screenshot refreshed
[[07:24:50]] [INFO] Refreshing screenshot...
[[07:24:47]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[07:24:47]] [SUCCESS] Screenshot refreshed successfully
[[07:24:47]] [SUCCESS] Screenshot refreshed successfully
[[07:24:47]] [SUCCESS] Screenshot refreshed
[[07:24:47]] [INFO] Refreshing screenshot...
[[07:24:44]] [SUCCESS] Screenshot refreshed successfully
[[07:24:44]] [SUCCESS] Screenshot refreshed successfully
[[07:24:44]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[07:24:44]] [SUCCESS] Screenshot refreshed
[[07:24:44]] [INFO] Refreshing screenshot...
[[07:24:42]] [SUCCESS] Screenshot refreshed successfully
[[07:24:42]] [SUCCESS] Screenshot refreshed successfully
[[07:24:41]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[07:24:41]] [SUCCESS] Screenshot refreshed
[[07:24:41]] [INFO] Refreshing screenshot...
[[07:24:39]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:24:39]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[07:24:39]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[07:24:39]] [INFO] g052Oo1Gcl=running
[[07:24:39]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[07:24:39]] [SUCCESS] Screenshot refreshed successfully
[[07:24:39]] [SUCCESS] Screenshot refreshed successfully
[[07:24:39]] [SUCCESS] Screenshot refreshed
[[07:24:39]] [INFO] Refreshing screenshot...
[[07:24:39]] [INFO] J9loj6Zs95K=pass
[[07:24:37]] [INFO] J9loj6Zs95K=running
[[07:24:37]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:36]] [SUCCESS] Screenshot refreshed
[[07:24:36]] [INFO] Refreshing screenshot...
[[07:24:36]] [INFO] Y8v5g7AJD1i=pass
[[07:24:30]] [INFO] Y8v5g7AJD1i=running
[[07:24:30]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:24:30]] [SUCCESS] Screenshot refreshed successfully
[[07:24:30]] [SUCCESS] Screenshot refreshed successfully
[[07:24:29]] [SUCCESS] Screenshot refreshed
[[07:24:29]] [INFO] Refreshing screenshot...
[[07:24:29]] [INFO] eqHB0Nj1He=pass
[[07:24:27]] [SUCCESS] Screenshot refreshed successfully
[[07:24:27]] [SUCCESS] Screenshot refreshed successfully
[[07:24:26]] [INFO] eqHB0Nj1He=running
[[07:24:26]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[07:24:26]] [SUCCESS] Screenshot refreshed
[[07:24:26]] [INFO] Refreshing screenshot...
[[07:24:26]] [INFO] H9fkkqcFbZ=pass
[[07:24:24]] [INFO] H9fkkqcFbZ=running
[[07:24:24]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[07:24:24]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[07:24:24]] [SUCCESS] Cleared 63 screenshots from database
[[07:24:24]] [INFO] Clearing screenshots from database before execution...
[[07:24:24]] [SUCCESS] All screenshots deleted successfully
[[07:24:24]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:24:24]] [INFO] Skipping report initialization - single test case execution
[[07:24:22]] [SUCCESS] All screenshots deleted successfully
[[07:24:22]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[07:24:22]] [SUCCESS] Added action: multiStep
[[07:24:22]] [SUCCESS] Added action: tapIfLocatorExists
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: waitTill
[[07:24:22]] [SUCCESS] Added action: text
[[07:24:22]] [SUCCESS] Added action: tapOnText
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: swipe
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: swipe
[[07:24:22]] [SUCCESS] Added action: wait
[[07:24:22]] [SUCCESS] Added action: tapIfLocatorExists
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: waitTill
[[07:24:22]] [SUCCESS] Added action: text
[[07:24:22]] [SUCCESS] Added action: tapOnText
[[07:24:22]] [SUCCESS] Added action: waitTill
[[07:24:22]] [SUCCESS] Added action: multiStep
[[07:24:22]] [SUCCESS] Added action: waitTill
[[07:24:22]] [SUCCESS] Added action: tap
[[07:24:22]] [SUCCESS] Added action: launchApp
[[07:24:22]] [SUCCESS] Added action: terminateApp
[[07:24:22]] [INFO] All actions cleared
[[07:24:22]] [INFO] Cleaning up screenshots...
[[07:24:19]] [SUCCESS] Screenshot refreshed successfully
[[07:24:18]] [SUCCESS] Screenshot refreshed
[[07:24:18]] [INFO] Refreshing screenshot...
[[07:24:17]] [SUCCESS] Connected to device: ************:33739
[[07:24:17]] [INFO] Device info updated: RMX2151
[[07:24:12]] [INFO] Connecting to device: ************:33739 (Platform: Android)...
[[07:24:09]] [SUCCESS] Found 1 device(s)
[[07:24:09]] [INFO] Refreshing device list...
