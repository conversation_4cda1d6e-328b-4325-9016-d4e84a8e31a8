Action Log - 2025-07-12 21:41:52
================================================================================

[[21:41:52]] [INFO] Generating execution report...
[[21:41:52]] [SUCCESS] All tests passed successfully!
[[21:41:52]] [INFO] Moving to the next test case after failure (server will handle retry)
[[21:41:52]] [ERROR] Multi Step action step 1 failed: Failed to execute wait_till_element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[21:41:50]] [SUCCESS] Screenshot refreshed successfully
[[21:41:50]] [SUCCESS] Screenshot refreshed successfully
[[21:41:50]] [INFO] Executing Multi Step action step 1/48: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[21:41:50]] [INFO] Loaded 48 steps from test case: Delivery Buy Steps_AU-ANDROID
[[21:41:50]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[21:41:50]] [INFO] e5zwMRuhB1=running
[[21:41:50]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (48 steps)
[[21:41:49]] [SUCCESS] Screenshot refreshed
[[21:41:49]] [INFO] Refreshing screenshot...
[[21:41:49]] [INFO] zrdO3PVkX3=pass
[[21:41:43]] [INFO] zrdO3PVkX3=running
[[21:41:43]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[21:41:43]] [INFO] F1olhgKhUt=fail
[[21:41:43]] [ERROR] Action 25 failed: Error tapping element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[21:41:41]] [INFO] F1olhgKhUt=running
[[21:41:41]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:41:41]] [INFO] FnrbyHq7bU=fail
[[21:41:41]] [ERROR] Action 24 failed: Error tapping element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[21:41:39]] [INFO] FnrbyHq7bU=running
[[21:41:39]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[21:41:39]] [INFO] nAB6Q8LAdv=fail
[[21:41:39]] [ERROR] Action 23 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[21:41:07]] [SUCCESS] Screenshot refreshed successfully
[[21:41:07]] [SUCCESS] Screenshot refreshed successfully
[[21:41:07]] [INFO] nAB6Q8LAdv=running
[[21:41:07]] [INFO] Executing action 23/27: Wait till xpath=//android.widget.Button[@text="Filter"]
[[21:41:06]] [SUCCESS] Screenshot refreshed
[[21:41:06]] [INFO] Refreshing screenshot...
[[21:41:06]] [INFO] JRheeTvpJf=pass
[[21:41:04]] [SUCCESS] Screenshot refreshed successfully
[[21:41:04]] [SUCCESS] Screenshot refreshed successfully
[[21:41:04]] [INFO] JRheeTvpJf=running
[[21:41:04]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[21:41:03]] [SUCCESS] Screenshot refreshed
[[21:41:03]] [INFO] Refreshing screenshot...
[[21:41:03]] [INFO] o1gHFWhXTL=pass
[[21:40:59]] [SUCCESS] Screenshot refreshed successfully
[[21:40:59]] [SUCCESS] Screenshot refreshed successfully
[[21:40:58]] [INFO] o1gHFWhXTL=running
[[21:40:58]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[21:40:58]] [SUCCESS] Screenshot refreshed
[[21:40:58]] [INFO] Refreshing screenshot...
[[21:40:58]] [INFO] cKNu2QoRC1=pass
[[21:40:55]] [SUCCESS] Screenshot refreshed successfully
[[21:40:55]] [SUCCESS] Screenshot refreshed successfully
[[21:40:55]] [INFO] cKNu2QoRC1=running
[[21:40:55]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[21:40:54]] [SUCCESS] Screenshot refreshed
[[21:40:54]] [INFO] Refreshing screenshot...
[[21:40:54]] [INFO] OyUowAaBzD=pass
[[21:40:51]] [SUCCESS] Screenshot refreshed successfully
[[21:40:51]] [SUCCESS] Screenshot refreshed successfully
[[21:40:51]] [INFO] OyUowAaBzD=running
[[21:40:51]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[21:40:50]] [SUCCESS] Screenshot refreshed
[[21:40:50]] [INFO] Refreshing screenshot...
[[21:40:50]] [INFO] Ob26qqcA0p=pass
[[21:40:45]] [SUCCESS] Screenshot refreshed successfully
[[21:40:45]] [SUCCESS] Screenshot refreshed successfully
[[21:40:45]] [INFO] Ob26qqcA0p=running
[[21:40:45]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[21:40:44]] [SUCCESS] Screenshot refreshed
[[21:40:44]] [INFO] Refreshing screenshot...
[[21:40:44]] [INFO] k3mu9Mt7Ec=pass
[[21:40:08]] [INFO] k3mu9Mt7Ec=running
[[21:40:08]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[21:40:08]] [SUCCESS] Screenshot refreshed successfully
[[21:40:08]] [SUCCESS] Screenshot refreshed successfully
[[21:40:08]] [SUCCESS] Screenshot refreshed
[[21:40:08]] [INFO] Refreshing screenshot...
[[21:40:08]] [INFO] FFM0CCo6Qg=pass
[[21:40:02]] [INFO] FFM0CCo6Qg=running
[[21:40:02]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[21:40:02]] [SUCCESS] Screenshot refreshed successfully
[[21:40:02]] [SUCCESS] Screenshot refreshed successfully
[[21:40:02]] [SUCCESS] Screenshot refreshed
[[21:40:02]] [INFO] Refreshing screenshot...
[[21:40:02]] [INFO] LWXWKZE4UV=pass
[[21:39:48]] [SUCCESS] Screenshot refreshed successfully
[[21:39:48]] [SUCCESS] Screenshot refreshed successfully
[[21:39:48]] [INFO] LWXWKZE4UV=running
[[21:39:48]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[21:39:48]] [SUCCESS] Screenshot refreshed
[[21:39:48]] [INFO] Refreshing screenshot...
[[21:39:48]] [INFO] Qbg9bipTGs=pass
[[21:39:20]] [SUCCESS] Screenshot refreshed successfully
[[21:39:20]] [SUCCESS] Screenshot refreshed successfully
[[21:39:19]] [INFO] Qbg9bipTGs=running
[[21:39:19]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[21:39:19]] [SUCCESS] Screenshot refreshed
[[21:39:19]] [INFO] Refreshing screenshot...
[[21:39:19]] [INFO] 7SpDO20tS2=pass
[[21:39:07]] [INFO] 7SpDO20tS2=running
[[21:39:07]] [INFO] Executing action 13/27: Wait for 10 ms
[[21:39:07]] [SUCCESS] Screenshot refreshed successfully
[[21:39:07]] [SUCCESS] Screenshot refreshed successfully
[[21:39:07]] [SUCCESS] Screenshot refreshed
[[21:39:07]] [INFO] Refreshing screenshot...
[[21:39:07]] [INFO] drbQBpgBfM=pass
[[21:39:04]] [SUCCESS] Screenshot refreshed successfully
[[21:39:04]] [SUCCESS] Screenshot refreshed successfully
[[21:39:04]] [INFO] drbQBpgBfM=running
[[21:39:04]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[21:39:03]] [SUCCESS] Screenshot refreshed
[[21:39:03]] [INFO] Refreshing screenshot...
[[21:39:03]] [INFO] F1olhgKhUt=pass
[[21:39:01]] [SUCCESS] Screenshot refreshed successfully
[[21:39:01]] [SUCCESS] Screenshot refreshed successfully
[[21:39:01]] [INFO] F1olhgKhUt=running
[[21:39:01]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:39:01]] [SUCCESS] Screenshot refreshed
[[21:39:01]] [INFO] Refreshing screenshot...
[[21:39:01]] [INFO] FnrbyHq7bU=pass
[[21:38:58]] [SUCCESS] Screenshot refreshed successfully
[[21:38:58]] [SUCCESS] Screenshot refreshed successfully
[[21:38:57]] [INFO] FnrbyHq7bU=running
[[21:38:57]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[21:38:57]] [SUCCESS] Screenshot refreshed
[[21:38:57]] [INFO] Refreshing screenshot...
[[21:38:57]] [INFO] nAB6Q8LAdv=pass
[[21:38:50]] [INFO] nAB6Q8LAdv=running
[[21:38:50]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[21:38:50]] [SUCCESS] Screenshot refreshed successfully
[[21:38:50]] [SUCCESS] Screenshot refreshed successfully
[[21:38:49]] [SUCCESS] Screenshot refreshed
[[21:38:49]] [INFO] Refreshing screenshot...
[[21:38:49]] [INFO] JRheeTvpJf=pass
[[21:38:47]] [SUCCESS] Screenshot refreshed successfully
[[21:38:47]] [SUCCESS] Screenshot refreshed successfully
[[21:38:47]] [INFO] JRheeTvpJf=running
[[21:38:47]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[21:38:47]] [SUCCESS] Screenshot refreshed
[[21:38:47]] [INFO] Refreshing screenshot...
[[21:38:47]] [INFO] o1gHFWhXTL=pass
[[21:38:43]] [SUCCESS] Screenshot refreshed successfully
[[21:38:43]] [SUCCESS] Screenshot refreshed successfully
[[21:38:42]] [INFO] o1gHFWhXTL=running
[[21:38:42]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[21:38:42]] [SUCCESS] Screenshot refreshed
[[21:38:42]] [INFO] Refreshing screenshot...
[[21:38:42]] [INFO] RLznb7o3ag=pass
[[21:38:38]] [SUCCESS] Screenshot refreshed successfully
[[21:38:38]] [SUCCESS] Screenshot refreshed successfully
[[21:38:37]] [INFO] RLznb7o3ag=running
[[21:38:37]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[21:38:37]] [SUCCESS] Screenshot refreshed
[[21:38:37]] [INFO] Refreshing screenshot...
[[21:38:36]] [SUCCESS] Screenshot refreshed
[[21:38:36]] [INFO] Refreshing screenshot...
[[21:38:34]] [SUCCESS] Screenshot refreshed successfully
[[21:38:34]] [SUCCESS] Screenshot refreshed successfully
[[21:38:34]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[21:38:34]] [SUCCESS] Screenshot refreshed
[[21:38:34]] [INFO] Refreshing screenshot...
[[21:38:31]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[21:38:31]] [SUCCESS] Screenshot refreshed successfully
[[21:38:31]] [SUCCESS] Screenshot refreshed successfully
[[21:38:31]] [SUCCESS] Screenshot refreshed
[[21:38:31]] [INFO] Refreshing screenshot...
[[21:38:29]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[21:38:28]] [SUCCESS] Screenshot refreshed successfully
[[21:38:28]] [SUCCESS] Screenshot refreshed successfully
[[21:38:28]] [SUCCESS] Screenshot refreshed
[[21:38:28]] [INFO] Refreshing screenshot...
[[21:38:26]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[21:38:26]] [SUCCESS] Screenshot refreshed successfully
[[21:38:26]] [SUCCESS] Screenshot refreshed successfully
[[21:38:26]] [SUCCESS] Screenshot refreshed
[[21:38:26]] [INFO] Refreshing screenshot...
[[21:38:24]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:38:24]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[21:38:24]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[21:38:24]] [INFO] g052Oo1Gcl=running
[[21:38:24]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[21:38:24]] [SUCCESS] Screenshot refreshed successfully
[[21:38:24]] [SUCCESS] Screenshot refreshed successfully
[[21:38:23]] [SUCCESS] Screenshot refreshed
[[21:38:23]] [INFO] Refreshing screenshot...
[[21:38:23]] [INFO] J9loj6Zs95K=pass
[[21:38:21]] [INFO] J9loj6Zs95K=running
[[21:38:21]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:38:21]] [SUCCESS] Screenshot refreshed successfully
[[21:38:21]] [SUCCESS] Screenshot refreshed successfully
[[21:38:21]] [SUCCESS] Screenshot refreshed
[[21:38:21]] [INFO] Refreshing screenshot...
[[21:38:21]] [INFO] Y8v5g7AJD1i=pass
[[21:38:14]] [INFO] Y8v5g7AJD1i=running
[[21:38:14]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:38:14]] [SUCCESS] Screenshot refreshed successfully
[[21:38:14]] [SUCCESS] Screenshot refreshed successfully
[[21:38:14]] [SUCCESS] Screenshot refreshed
[[21:38:14]] [INFO] Refreshing screenshot...
[[21:38:14]] [INFO] eqHB0Nj1He=pass
[[21:38:11]] [SUCCESS] Screenshot refreshed successfully
[[21:38:11]] [SUCCESS] Screenshot refreshed successfully
[[21:38:11]] [INFO] eqHB0Nj1He=running
[[21:38:11]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[21:38:10]] [SUCCESS] Screenshot refreshed
[[21:38:10]] [INFO] Refreshing screenshot...
[[21:38:10]] [INFO] H9fkkqcFbZ=pass
[[21:38:08]] [INFO] H9fkkqcFbZ=running
[[21:38:08]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[21:38:08]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[21:38:08]] [SUCCESS] Cleared 18 screenshots from database
[[21:38:08]] [INFO] Clearing screenshots from database before execution...
[[21:38:08]] [SUCCESS] All screenshots deleted successfully
[[21:38:08]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:38:08]] [INFO] Skipping report initialization - single test case execution
[[21:38:05]] [SUCCESS] All screenshots deleted successfully
[[21:38:05]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[21:38:05]] [SUCCESS] Added action: multiStep
[[21:38:05]] [SUCCESS] Added action: tapIfLocatorExists
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: waitTill
[[21:38:05]] [SUCCESS] Added action: text
[[21:38:05]] [SUCCESS] Added action: tapOnText
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: swipe
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: swipe
[[21:38:05]] [SUCCESS] Added action: wait
[[21:38:05]] [SUCCESS] Added action: tapIfLocatorExists
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: waitTill
[[21:38:05]] [SUCCESS] Added action: text
[[21:38:05]] [SUCCESS] Added action: tapOnText
[[21:38:05]] [SUCCESS] Added action: waitTill
[[21:38:05]] [SUCCESS] Added action: multiStep
[[21:38:05]] [SUCCESS] Added action: waitTill
[[21:38:05]] [SUCCESS] Added action: tap
[[21:38:05]] [SUCCESS] Added action: launchApp
[[21:38:05]] [SUCCESS] Added action: terminateApp
[[21:38:05]] [INFO] All actions cleared
[[21:38:05]] [INFO] Cleaning up screenshots...
[[21:38:03]] [SUCCESS] Screenshot refreshed successfully
[[21:38:03]] [SUCCESS] Screenshot refreshed
[[21:38:03]] [INFO] Refreshing screenshot...
[[21:38:02]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[21:38:02]] [INFO] Device info updated: RMX2151
[[21:37:57]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[21:37:54]] [SUCCESS] Found 1 device(s)
[[21:37:54]] [INFO] Refreshing device list...
