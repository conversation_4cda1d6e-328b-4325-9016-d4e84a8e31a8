Action Log - 2025-07-13 09:25:33
================================================================================

[[09:25:33]] [INFO] Generating execution report...
[[09:25:33]] [SUCCESS] All tests passed successfully!
[[09:25:33]] [SUCCESS] Screenshot refreshed
[[09:25:33]] [INFO] Refreshing screenshot...
[[09:25:32]] [SUCCESS] Screenshot refreshed
[[09:25:32]] [INFO] Refreshing screenshot...
[[09:25:30]] [SUCCESS] Screenshot refreshed successfully
[[09:25:30]] [SUCCESS] Screenshot refreshed successfully
[[09:25:29]] [INFO] Executing Multi Step action step 52/52: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[09:25:29]] [SUCCESS] Screenshot refreshed
[[09:25:29]] [INFO] Refreshing screenshot...
[[09:25:10]] [SUCCESS] Screenshot refreshed successfully
[[09:25:10]] [SUCCESS] Screenshot refreshed successfully
[[09:25:10]] [INFO] Executing Multi Step action step 51/52: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[09:25:09]] [SUCCESS] Screenshot refreshed
[[09:25:09]] [INFO] Refreshing screenshot...
[[09:25:04]] [SUCCESS] Screenshot refreshed successfully
[[09:25:04]] [SUCCESS] Screenshot refreshed successfully
[[09:25:04]] [INFO] Executing Multi Step action step 50/52: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Remove")]" is visible
[[09:25:03]] [SUCCESS] Screenshot refreshed
[[09:25:03]] [INFO] Refreshing screenshot...
[[09:24:39]] [SUCCESS] Screenshot refreshed successfully
[[09:24:39]] [SUCCESS] Screenshot refreshed successfully
[[09:24:39]] [INFO] Executing Multi Step action step 49/52: Wait till xpath=//android.widget.TextView[@text="Click & Collect"]
[[09:24:38]] [SUCCESS] Screenshot refreshed
[[09:24:38]] [INFO] Refreshing screenshot...
[[09:24:36]] [SUCCESS] Screenshot refreshed successfully
[[09:24:36]] [SUCCESS] Screenshot refreshed successfully
[[09:24:35]] [INFO] Executing Multi Step action step 48/52: Tap on element with xpath: //android.widget.Button[@content-desc="Checkout"]
[[09:24:35]] [SUCCESS] Screenshot refreshed
[[09:24:35]] [INFO] Refreshing screenshot...
[[09:24:33]] [SUCCESS] Screenshot refreshed successfully
[[09:24:33]] [SUCCESS] Screenshot refreshed successfully
[[09:24:32]] [INFO] Executing Multi Step action step 47/52: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[09:24:32]] [SUCCESS] Screenshot refreshed
[[09:24:32]] [INFO] Refreshing screenshot...
[[09:24:09]] [SUCCESS] Screenshot refreshed successfully
[[09:24:09]] [SUCCESS] Screenshot refreshed successfully
[[09:24:09]] [INFO] Executing Multi Step action step 46/52: Wait for 5 ms
[[09:24:08]] [SUCCESS] Screenshot refreshed
[[09:24:08]] [INFO] Refreshing screenshot...
[[09:24:02]] [SUCCESS] Screenshot refreshed successfully
[[09:24:02]] [SUCCESS] Screenshot refreshed successfully
[[09:24:01]] [INFO] Executing Multi Step action step 45/52: Tap on image: bag-close-android.png
[[09:24:01]] [SUCCESS] Screenshot refreshed
[[09:24:01]] [INFO] Refreshing screenshot...
[[09:23:36]] [SUCCESS] Screenshot refreshed successfully
[[09:23:36]] [SUCCESS] Screenshot refreshed successfully
[[09:23:36]] [INFO] Executing Multi Step action step 44/52: Wait for 5 ms
[[09:23:35]] [SUCCESS] Screenshot refreshed
[[09:23:35]] [INFO] Refreshing screenshot...
[[09:23:31]] [SUCCESS] Screenshot refreshed successfully
[[09:23:31]] [SUCCESS] Screenshot refreshed successfully
[[09:23:10]] [INFO] Executing Multi Step action step 43/52: Tap on Text: "Check"
[[09:23:10]] [SUCCESS] Screenshot refreshed
[[09:23:10]] [INFO] Refreshing screenshot...
[[09:23:03]] [SUCCESS] Screenshot refreshed successfully
[[09:23:03]] [SUCCESS] Screenshot refreshed successfully
[[09:23:03]] [INFO] Executing Multi Step action step 42/52: Wait for 5 ms
[[09:23:02]] [SUCCESS] Screenshot refreshed
[[09:23:02]] [INFO] Refreshing screenshot...
[[09:22:41]] [SUCCESS] Screenshot refreshed successfully
[[09:22:41]] [SUCCESS] Screenshot refreshed successfully
[[09:22:41]] [INFO] Executing Multi Step action step 41/52: Tap on image: zippay-chkbox-btn.png
[[09:22:40]] [SUCCESS] Screenshot refreshed
[[09:22:40]] [INFO] Refreshing screenshot...
[[09:22:33]] [SUCCESS] Screenshot refreshed successfully
[[09:22:33]] [SUCCESS] Screenshot refreshed successfully
[[09:22:33]] [INFO] Executing Multi Step action step 40/52: Wait for 5 ms
[[09:22:33]] [SUCCESS] Screenshot refreshed
[[09:22:33]] [INFO] Refreshing screenshot...
[[09:22:19]] [SUCCESS] Screenshot refreshed successfully
[[09:22:19]] [SUCCESS] Screenshot refreshed successfully
[[09:22:19]] [INFO] Executing Multi Step action step 39/52: Tap on image: bag-close-android.png
[[09:22:19]] [SUCCESS] Screenshot refreshed
[[09:22:19]] [INFO] Refreshing screenshot...
[[09:22:15]] [SUCCESS] Screenshot refreshed successfully
[[09:22:15]] [SUCCESS] Screenshot refreshed successfully
[[09:22:10]] [INFO] Executing Multi Step action step 38/52: Tap on Text: "Check"
[[09:22:10]] [SUCCESS] Screenshot refreshed
[[09:22:10]] [INFO] Refreshing screenshot...
[[09:22:03]] [SUCCESS] Screenshot refreshed successfully
[[09:22:03]] [SUCCESS] Screenshot refreshed successfully
[[09:22:03]] [INFO] Executing Multi Step action step 37/52: Wait for 5 ms
[[09:22:02]] [SUCCESS] Screenshot refreshed
[[09:22:02]] [INFO] Refreshing screenshot...
[[09:22:00]] [SUCCESS] Screenshot refreshed successfully
[[09:22:00]] [SUCCESS] Screenshot refreshed successfully
[[09:21:41]] [INFO] Executing Multi Step action step 36/52: Tap on image: afterpay-chkbox-android.png
[[09:21:41]] [SUCCESS] Screenshot refreshed
[[09:21:41]] [INFO] Refreshing screenshot...
[[09:21:34]] [SUCCESS] Screenshot refreshed successfully
[[09:21:34]] [SUCCESS] Screenshot refreshed successfully
[[09:21:34]] [INFO] Executing Multi Step action step 35/52: Wait for 5 ms
[[09:21:33]] [SUCCESS] Screenshot refreshed
[[09:21:33]] [INFO] Refreshing screenshot...
[[09:21:09]] [SUCCESS] Screenshot refreshed successfully
[[09:21:09]] [SUCCESS] Screenshot refreshed successfully
[[09:21:08]] [INFO] Executing Multi Step action step 34/52: Tap on image: paypal-close-btn-android.png
[[09:21:08]] [SUCCESS] Screenshot refreshed
[[09:21:08]] [INFO] Refreshing screenshot...
[[09:21:02]] [SUCCESS] Screenshot refreshed successfully
[[09:21:02]] [SUCCESS] Screenshot refreshed successfully
[[09:21:01]] [INFO] Executing Multi Step action step 33/52: Tap on image: Payin4-btn-Android.png
[[09:21:01]] [SUCCESS] Screenshot refreshed
[[09:21:01]] [INFO] Refreshing screenshot...
[[09:20:40]] [SUCCESS] Screenshot refreshed successfully
[[09:20:40]] [SUCCESS] Screenshot refreshed successfully
[[09:20:39]] [INFO] Executing Multi Step action step 32/52: Wait for 5 ms
[[09:20:39]] [SUCCESS] Screenshot refreshed
[[09:20:39]] [INFO] Refreshing screenshot...
[[09:20:33]] [SUCCESS] Screenshot refreshed successfully
[[09:20:33]] [SUCCESS] Screenshot refreshed successfully
[[09:20:32]] [INFO] Executing Multi Step action step 31/52: Tap on image: PaypalIn4-Chkbox-Android.png
[[09:20:32]] [SUCCESS] Screenshot refreshed
[[09:20:32]] [INFO] Refreshing screenshot...
[[09:20:08]] [SUCCESS] Screenshot refreshed successfully
[[09:20:08]] [SUCCESS] Screenshot refreshed successfully
[[09:20:08]] [INFO] Executing Multi Step action step 30/52: Wait for 5 ms
[[09:20:08]] [SUCCESS] Screenshot refreshed
[[09:20:08]] [INFO] Refreshing screenshot...
[[09:20:01]] [SUCCESS] Screenshot refreshed successfully
[[09:20:01]] [SUCCESS] Screenshot refreshed successfully
[[09:20:01]] [INFO] Executing Multi Step action step 29/52: Tap on image: paypal-close-btn-android.png
[[09:20:01]] [SUCCESS] Screenshot refreshed
[[09:20:01]] [INFO] Refreshing screenshot...
[[09:19:37]] [SUCCESS] Screenshot refreshed successfully
[[09:19:37]] [SUCCESS] Screenshot refreshed successfully
[[09:19:37]] [INFO] Executing Multi Step action step 28/52: Wait for 5 ms
[[09:19:37]] [SUCCESS] Screenshot refreshed
[[09:19:37]] [INFO] Refreshing screenshot...
[[09:19:30]] [SUCCESS] Screenshot refreshed successfully
[[09:19:30]] [SUCCESS] Screenshot refreshed successfully
[[09:19:30]] [INFO] Executing Multi Step action step 27/52: Tap on image: paypal-payment-btn-android.png
[[09:19:30]] [SUCCESS] Screenshot refreshed
[[09:19:30]] [INFO] Refreshing screenshot...
[[09:19:24]] [SUCCESS] Screenshot refreshed successfully
[[09:19:24]] [SUCCESS] Screenshot refreshed successfully
[[09:19:24]] [INFO] Executing Multi Step action step 26/52: Swipe from (50%, 70%) to (50%, 40%)
[[09:19:24]] [SUCCESS] Screenshot refreshed
[[09:19:24]] [INFO] Refreshing screenshot...
[[09:19:20]] [SUCCESS] Screenshot refreshed successfully
[[09:19:20]] [SUCCESS] Screenshot refreshed successfully
[[09:19:20]] [INFO] Executing Multi Step action step 25/52: Tap on Text: "PayPal"
[[09:19:20]] [SUCCESS] Screenshot refreshed
[[09:19:20]] [INFO] Refreshing screenshot...
[[09:19:16]] [SUCCESS] Screenshot refreshed successfully
[[09:19:16]] [SUCCESS] Screenshot refreshed successfully
[[09:19:16]] [INFO] Executing Multi Step action step 24/52: Tap on Text: "Continue"
[[09:19:16]] [SUCCESS] Screenshot refreshed
[[09:19:16]] [INFO] Refreshing screenshot...
[[09:19:11]] [SUCCESS] Screenshot refreshed successfully
[[09:19:11]] [SUCCESS] Screenshot refreshed successfully
[[09:19:11]] [INFO] Executing Multi Step action step 23/52: Swipe from (50%, 70%) to (50%, 40%)
[[09:19:10]] [SUCCESS] Screenshot refreshed
[[09:19:10]] [INFO] Refreshing screenshot...
[[09:19:09]] [SUCCESS] Screenshot refreshed successfully
[[09:19:09]] [SUCCESS] Screenshot refreshed successfully
[[09:19:08]] [INFO] Executing Multi Step action step 22/52: Android Function: send_key_event - Key Event: ENTER
[[09:19:08]] [SUCCESS] Screenshot refreshed
[[09:19:08]] [INFO] Refreshing screenshot...
[[09:18:57]] [SUCCESS] Screenshot refreshed successfully
[[09:18:57]] [SUCCESS] Screenshot refreshed successfully
[[09:18:57]] [INFO] Executing Multi Step action step 21/52: Tap on image: delivery-address-options-android.png
[[09:18:56]] [SUCCESS] Screenshot refreshed
[[09:18:56]] [INFO] Refreshing screenshot...
[[09:18:54]] [SUCCESS] Screenshot refreshed successfully
[[09:18:54]] [SUCCESS] Screenshot refreshed successfully
[[09:18:54]] [INFO] Executing Multi Step action step 20/52: Input text: "305 238 Flinders Street"
[[09:18:54]] [SUCCESS] Screenshot refreshed
[[09:18:54]] [INFO] Refreshing screenshot...
[[09:18:49]] [SUCCESS] Screenshot refreshed successfully
[[09:18:49]] [SUCCESS] Screenshot refreshed successfully
[[09:18:49]] [INFO] Executing Multi Step action step 19/52: Tap on Text: "address"
[[09:18:49]] [SUCCESS] Screenshot refreshed
[[09:18:49]] [INFO] Refreshing screenshot...
[[09:18:45]] [SUCCESS] Screenshot refreshed successfully
[[09:18:45]] [SUCCESS] Screenshot refreshed successfully
[[09:18:45]] [INFO] Executing Multi Step action step 18/52: Tap on Text: "Continue"
[[09:18:44]] [SUCCESS] Screenshot refreshed
[[09:18:44]] [INFO] Refreshing screenshot...
[[09:18:40]] [SUCCESS] Screenshot refreshed successfully
[[09:18:40]] [SUCCESS] Screenshot refreshed successfully
[[09:18:40]] [INFO] Executing Multi Step action step 17/52: Swipe from (50%, 70%) to (50%, 50%)
[[09:18:39]] [SUCCESS] Screenshot refreshed
[[09:18:39]] [INFO] Refreshing screenshot...
[[09:18:38]] [SUCCESS] Screenshot refreshed successfully
[[09:18:38]] [SUCCESS] Screenshot refreshed successfully
[[09:18:37]] [INFO] Executing Multi Step action step 16/52: Input text: "0400000000"
[[09:18:37]] [SUCCESS] Screenshot refreshed
[[09:18:37]] [INFO] Refreshing screenshot...
[[09:18:35]] [SUCCESS] Screenshot refreshed successfully
[[09:18:35]] [SUCCESS] Screenshot refreshed successfully
[[09:18:35]] [INFO] Executing Multi Step action step 15/52: Clear Text (auto)
[[09:18:34]] [SUCCESS] Screenshot refreshed
[[09:18:34]] [INFO] Refreshing screenshot...
[[09:18:10]] [SUCCESS] Screenshot refreshed successfully
[[09:18:10]] [SUCCESS] Screenshot refreshed successfully
[[09:18:09]] [INFO] Executing Multi Step action step 14/52: Tap on Text: "Mobile"
[[09:18:09]] [SUCCESS] Screenshot refreshed
[[09:18:09]] [INFO] Refreshing screenshot...
[[09:18:07]] [SUCCESS] Screenshot refreshed successfully
[[09:18:07]] [SUCCESS] Screenshot refreshed successfully
[[09:18:07]] [INFO] Executing Multi Step action step 13/52: Input text: "<EMAIL>"
[[09:18:06]] [SUCCESS] Screenshot refreshed
[[09:18:06]] [INFO] Refreshing screenshot...
[[09:18:05]] [SUCCESS] Screenshot refreshed successfully
[[09:18:05]] [SUCCESS] Screenshot refreshed successfully
[[09:18:04]] [INFO] Executing Multi Step action step 12/52: Clear Text (auto)
[[09:18:04]] [SUCCESS] Screenshot refreshed
[[09:18:04]] [INFO] Refreshing screenshot...
[[09:18:00]] [SUCCESS] Screenshot refreshed successfully
[[09:18:00]] [SUCCESS] Screenshot refreshed successfully
[[09:17:41]] [INFO] Executing Multi Step action step 11/52: Tap on Text: "Email"
[[09:17:41]] [SUCCESS] Screenshot refreshed
[[09:17:41]] [INFO] Refreshing screenshot...
[[09:17:39]] [SUCCESS] Screenshot refreshed successfully
[[09:17:39]] [SUCCESS] Screenshot refreshed successfully
[[09:17:38]] [INFO] Executing Multi Step action step 10/52: Input text: "LastName"
[[09:17:38]] [SUCCESS] Screenshot refreshed
[[09:17:38]] [INFO] Refreshing screenshot...
[[09:17:36]] [SUCCESS] Screenshot refreshed successfully
[[09:17:36]] [SUCCESS] Screenshot refreshed successfully
[[09:17:36]] [INFO] Executing Multi Step action step 9/52: Clear Text (auto)
[[09:17:35]] [SUCCESS] Screenshot refreshed
[[09:17:35]] [INFO] Refreshing screenshot...
[[09:17:31]] [SUCCESS] Screenshot refreshed successfully
[[09:17:31]] [SUCCESS] Screenshot refreshed successfully
[[09:17:10]] [INFO] Executing Multi Step action step 8/52: Tap on Text: "Last"
[[09:17:10]] [SUCCESS] Screenshot refreshed
[[09:17:10]] [INFO] Refreshing screenshot...
[[09:17:07]] [SUCCESS] Screenshot refreshed successfully
[[09:17:07]] [SUCCESS] Screenshot refreshed successfully
[[09:17:07]] [INFO] Executing Multi Step action step 7/52: Input text: "FirstName"
[[09:17:06]] [SUCCESS] Screenshot refreshed
[[09:17:06]] [INFO] Refreshing screenshot...
[[09:17:05]] [SUCCESS] Screenshot refreshed successfully
[[09:17:05]] [SUCCESS] Screenshot refreshed successfully
[[09:17:04]] [INFO] Executing Multi Step action step 6/52: Clear Text (auto)
[[09:17:04]] [SUCCESS] Screenshot refreshed
[[09:17:04]] [INFO] Refreshing screenshot...
[[09:17:00]] [SUCCESS] Screenshot refreshed successfully
[[09:17:00]] [SUCCESS] Screenshot refreshed successfully
[[09:16:41]] [INFO] Executing Multi Step action step 5/52: Tap on Text: "First"
[[09:16:41]] [SUCCESS] Screenshot refreshed
[[09:16:41]] [INFO] Refreshing screenshot...
[[09:16:35]] [SUCCESS] Screenshot refreshed successfully
[[09:16:35]] [SUCCESS] Screenshot refreshed successfully
[[09:16:35]] [INFO] Executing Multi Step action step 4/52: Tap on image: continue-to-details-android.png
[[09:16:34]] [SUCCESS] Screenshot refreshed
[[09:16:34]] [INFO] Refreshing screenshot...
[[09:16:19]] [SUCCESS] Screenshot refreshed successfully
[[09:16:19]] [SUCCESS] Screenshot refreshed successfully
[[09:16:19]] [INFO] Executing Multi Step action step 3/52: Swipe from (50%, 70%) to (50%, 30%)
[[09:16:19]] [SUCCESS] Screenshot refreshed
[[09:16:19]] [INFO] Refreshing screenshot...
[[09:16:16]] [SUCCESS] Screenshot refreshed successfully
[[09:16:16]] [SUCCESS] Screenshot refreshed successfully
[[09:16:16]] [INFO] Executing Multi Step action step 2/52: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[09:16:16]] [SUCCESS] Screenshot refreshed
[[09:16:16]] [INFO] Refreshing screenshot...
[[09:16:10]] [SUCCESS] Screenshot refreshed successfully
[[09:16:10]] [SUCCESS] Screenshot refreshed successfully
[[09:16:10]] [INFO] Executing Multi Step action step 1/52: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[09:16:10]] [INFO] Loaded 52 steps from test case: Delivery Buy Steps_AU-ANDROID
[[09:16:10]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[09:16:10]] [INFO] AfD5QcCD90=running
[[09:16:10]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (52 steps)
[[09:16:10]] [SUCCESS] Screenshot refreshed
[[09:16:10]] [INFO] Refreshing screenshot...
[[09:16:10]] [INFO] zrdO3PVkX3=pass
[[09:16:06]] [SUCCESS] Screenshot refreshed successfully
[[09:16:06]] [SUCCESS] Screenshot refreshed successfully
[[09:16:06]] [INFO] zrdO3PVkX3=running
[[09:16:06]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[09:16:06]] [SUCCESS] Screenshot refreshed
[[09:16:06]] [INFO] Refreshing screenshot...
[[09:16:06]] [INFO] F1olhgKhUt=pass
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [INFO] F1olhgKhUt=running
[[09:16:03]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[09:16:02]] [SUCCESS] Screenshot refreshed
[[09:16:02]] [INFO] Refreshing screenshot...
[[09:16:02]] [INFO] FnrbyHq7bU=pass
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:58]] [INFO] FnrbyHq7bU=running
[[09:15:58]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[09:15:58]] [SUCCESS] Screenshot refreshed
[[09:15:58]] [INFO] Refreshing screenshot...
[[09:15:58]] [INFO] nAB6Q8LAdv=pass
[[09:15:52]] [SUCCESS] Screenshot refreshed successfully
[[09:15:52]] [SUCCESS] Screenshot refreshed successfully
[[09:15:51]] [INFO] nAB6Q8LAdv=running
[[09:15:51]] [INFO] Executing action 23/27: Wait till text appears: "Filter"
[[09:15:51]] [SUCCESS] Screenshot refreshed
[[09:15:51]] [INFO] Refreshing screenshot...
[[09:15:51]] [INFO] JRheeTvpJf=pass
[[09:15:48]] [SUCCESS] Screenshot refreshed successfully
[[09:15:48]] [SUCCESS] Screenshot refreshed successfully
[[09:15:48]] [INFO] JRheeTvpJf=running
[[09:15:48]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[09:15:48]] [SUCCESS] Screenshot refreshed
[[09:15:48]] [INFO] Refreshing screenshot...
[[09:15:48]] [INFO] o1gHFWhXTL=pass
[[09:15:43]] [SUCCESS] Screenshot refreshed successfully
[[09:15:43]] [SUCCESS] Screenshot refreshed successfully
[[09:15:42]] [INFO] o1gHFWhXTL=running
[[09:15:42]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[09:15:41]] [SUCCESS] Screenshot refreshed
[[09:15:41]] [INFO] Refreshing screenshot...
[[09:15:41]] [INFO] cKNu2QoRC1=pass
[[09:15:38]] [SUCCESS] Screenshot refreshed successfully
[[09:15:38]] [SUCCESS] Screenshot refreshed successfully
[[09:15:37]] [INFO] cKNu2QoRC1=running
[[09:15:37]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[09:15:36]] [SUCCESS] Screenshot refreshed
[[09:15:36]] [INFO] Refreshing screenshot...
[[09:15:36]] [INFO] OyUowAaBzD=pass
[[09:15:32]] [SUCCESS] Screenshot refreshed successfully
[[09:15:32]] [SUCCESS] Screenshot refreshed successfully
[[09:15:32]] [INFO] OyUowAaBzD=running
[[09:15:32]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[09:15:31]] [SUCCESS] Screenshot refreshed
[[09:15:31]] [INFO] Refreshing screenshot...
[[09:15:31]] [INFO] Ob26qqcA0p=pass
[[09:14:59]] [INFO] Ob26qqcA0p=running
[[09:14:59]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[09:14:59]] [SUCCESS] Screenshot refreshed successfully
[[09:14:59]] [SUCCESS] Screenshot refreshed successfully
[[09:14:58]] [SUCCESS] Screenshot refreshed
[[09:14:58]] [INFO] Refreshing screenshot...
[[09:14:58]] [INFO] k3mu9Mt7Ec=pass
[[09:14:56]] [INFO] k3mu9Mt7Ec=running
[[09:14:56]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[09:14:56]] [SUCCESS] Screenshot refreshed successfully
[[09:14:56]] [SUCCESS] Screenshot refreshed successfully
[[09:14:55]] [SUCCESS] Screenshot refreshed
[[09:14:55]] [INFO] Refreshing screenshot...
[[09:14:55]] [INFO] FFM0CCo6Qg=pass
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [INFO] FFM0CCo6Qg=running
[[09:14:49]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[09:14:49]] [SUCCESS] Screenshot refreshed
[[09:14:49]] [INFO] Refreshing screenshot...
[[09:14:49]] [INFO] LWXWKZE4UV=pass
[[09:14:37]] [SUCCESS] Screenshot refreshed successfully
[[09:14:37]] [SUCCESS] Screenshot refreshed successfully
[[09:14:37]] [INFO] LWXWKZE4UV=running
[[09:14:37]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[09:14:37]] [SUCCESS] Screenshot refreshed
[[09:14:37]] [INFO] Refreshing screenshot...
[[09:14:37]] [INFO] Qbg9bipTGs=pass
[[09:14:32]] [SUCCESS] Screenshot refreshed successfully
[[09:14:32]] [SUCCESS] Screenshot refreshed successfully
[[09:14:32]] [INFO] Qbg9bipTGs=running
[[09:14:32]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[09:14:31]] [SUCCESS] Screenshot refreshed
[[09:14:31]] [INFO] Refreshing screenshot...
[[09:14:31]] [INFO] 7SpDO20tS2=pass
[[09:14:19]] [SUCCESS] Screenshot refreshed successfully
[[09:14:19]] [SUCCESS] Screenshot refreshed successfully
[[09:14:19]] [INFO] 7SpDO20tS2=running
[[09:14:19]] [INFO] Executing action 13/27: Wait for 10 ms
[[09:14:19]] [SUCCESS] Screenshot refreshed
[[09:14:19]] [INFO] Refreshing screenshot...
[[09:14:19]] [INFO] drbQBpgBfM=pass
[[09:14:16]] [SUCCESS] Screenshot refreshed successfully
[[09:14:16]] [SUCCESS] Screenshot refreshed successfully
[[09:14:15]] [INFO] drbQBpgBfM=running
[[09:14:15]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[09:14:15]] [SUCCESS] Screenshot refreshed
[[09:14:15]] [INFO] Refreshing screenshot...
[[09:14:15]] [INFO] F1olhgKhUt=pass
[[09:14:13]] [SUCCESS] Screenshot refreshed successfully
[[09:14:13]] [SUCCESS] Screenshot refreshed successfully
[[09:14:12]] [INFO] F1olhgKhUt=running
[[09:14:12]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[09:14:11]] [SUCCESS] Screenshot refreshed
[[09:14:11]] [INFO] Refreshing screenshot...
[[09:14:11]] [INFO] FnrbyHq7bU=pass
[[09:14:08]] [SUCCESS] Screenshot refreshed successfully
[[09:14:08]] [SUCCESS] Screenshot refreshed successfully
[[09:14:07]] [INFO] FnrbyHq7bU=running
[[09:14:07]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[09:14:07]] [SUCCESS] Screenshot refreshed
[[09:14:07]] [INFO] Refreshing screenshot...
[[09:14:07]] [INFO] nAB6Q8LAdv=pass
[[09:13:59]] [SUCCESS] Screenshot refreshed successfully
[[09:13:59]] [SUCCESS] Screenshot refreshed successfully
[[09:13:59]] [INFO] nAB6Q8LAdv=running
[[09:13:59]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[09:13:59]] [SUCCESS] Screenshot refreshed
[[09:13:59]] [INFO] Refreshing screenshot...
[[09:13:59]] [INFO] JRheeTvpJf=pass
[[09:13:57]] [SUCCESS] Screenshot refreshed successfully
[[09:13:57]] [SUCCESS] Screenshot refreshed successfully
[[09:13:57]] [INFO] JRheeTvpJf=running
[[09:13:57]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[09:13:56]] [SUCCESS] Screenshot refreshed
[[09:13:56]] [INFO] Refreshing screenshot...
[[09:13:56]] [INFO] o1gHFWhXTL=pass
[[09:13:52]] [SUCCESS] Screenshot refreshed successfully
[[09:13:52]] [SUCCESS] Screenshot refreshed successfully
[[09:13:51]] [INFO] o1gHFWhXTL=running
[[09:13:51]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[09:13:51]] [SUCCESS] Screenshot refreshed
[[09:13:51]] [INFO] Refreshing screenshot...
[[09:13:51]] [INFO] RLznb7o3ag=pass
[[09:13:47]] [SUCCESS] Screenshot refreshed successfully
[[09:13:47]] [SUCCESS] Screenshot refreshed successfully
[[09:13:46]] [INFO] RLznb7o3ag=running
[[09:13:46]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[09:13:46]] [SUCCESS] Screenshot refreshed
[[09:13:46]] [INFO] Refreshing screenshot...
[[09:13:45]] [SUCCESS] Screenshot refreshed
[[09:13:45]] [INFO] Refreshing screenshot...
[[09:13:43]] [SUCCESS] Screenshot refreshed successfully
[[09:13:43]] [SUCCESS] Screenshot refreshed successfully
[[09:13:43]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[09:13:42]] [SUCCESS] Screenshot refreshed
[[09:13:42]] [INFO] Refreshing screenshot...
[[09:13:40]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[09:13:40]] [SUCCESS] Screenshot refreshed successfully
[[09:13:40]] [SUCCESS] Screenshot refreshed successfully
[[09:13:39]] [SUCCESS] Screenshot refreshed
[[09:13:39]] [INFO] Refreshing screenshot...
[[09:13:37]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[09:13:37]] [SUCCESS] Screenshot refreshed successfully
[[09:13:37]] [SUCCESS] Screenshot refreshed successfully
[[09:13:36]] [SUCCESS] Screenshot refreshed
[[09:13:36]] [INFO] Refreshing screenshot...
[[09:13:34]] [SUCCESS] Screenshot refreshed successfully
[[09:13:34]] [SUCCESS] Screenshot refreshed successfully
[[09:13:34]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[09:13:33]] [SUCCESS] Screenshot refreshed
[[09:13:33]] [INFO] Refreshing screenshot...
[[09:13:31]] [SUCCESS] Screenshot refreshed successfully
[[09:13:31]] [SUCCESS] Screenshot refreshed successfully
[[09:13:31]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:13:31]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[09:13:31]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[09:13:31]] [INFO] g052Oo1Gcl=running
[[09:13:31]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[09:13:31]] [SUCCESS] Screenshot refreshed
[[09:13:31]] [INFO] Refreshing screenshot...
[[09:13:31]] [INFO] J9loj6Zs95K=pass
[[09:13:29]] [SUCCESS] Screenshot refreshed successfully
[[09:13:29]] [SUCCESS] Screenshot refreshed successfully
[[09:13:29]] [INFO] J9loj6Zs95K=running
[[09:13:29]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:13:28]] [SUCCESS] Screenshot refreshed
[[09:13:28]] [INFO] Refreshing screenshot...
[[09:13:28]] [INFO] Y8v5g7AJD1i=pass
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [INFO] Y8v5g7AJD1i=running
[[09:13:22]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:13:21]] [SUCCESS] Screenshot refreshed
[[09:13:21]] [INFO] Refreshing screenshot...
[[09:13:21]] [INFO] eqHB0Nj1He=pass
[[09:13:20]] [SUCCESS] Screenshot refreshed successfully
[[09:13:20]] [SUCCESS] Screenshot refreshed successfully
[[09:13:18]] [INFO] eqHB0Nj1He=running
[[09:13:18]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[09:13:18]] [SUCCESS] Screenshot refreshed
[[09:13:18]] [INFO] Refreshing screenshot...
[[09:13:18]] [INFO] H9fkkqcFbZ=pass
[[09:13:13]] [INFO] H9fkkqcFbZ=running
[[09:13:13]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[09:13:13]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[09:13:13]] [SUCCESS] Cleared 165 screenshots from database
[[09:13:13]] [INFO] Clearing screenshots from database before execution...
[[09:13:13]] [SUCCESS] All screenshots deleted successfully
[[09:13:13]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:13:13]] [INFO] Skipping report initialization - single test case execution
[[09:13:12]] [SUCCESS] All screenshots deleted successfully
[[09:13:12]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[09:13:12]] [SUCCESS] Added action: multiStep
[[09:13:12]] [SUCCESS] Added action: tapIfLocatorExists
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: waitTill
[[09:13:12]] [SUCCESS] Added action: text
[[09:13:12]] [SUCCESS] Added action: tapOnText
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: swipe
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: swipe
[[09:13:12]] [SUCCESS] Added action: wait
[[09:13:12]] [SUCCESS] Added action: tapIfLocatorExists
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: waitTill
[[09:13:12]] [SUCCESS] Added action: text
[[09:13:12]] [SUCCESS] Added action: tapOnText
[[09:13:12]] [SUCCESS] Added action: waitTill
[[09:13:12]] [SUCCESS] Added action: multiStep
[[09:13:12]] [SUCCESS] Added action: waitTill
[[09:13:12]] [SUCCESS] Added action: tap
[[09:13:12]] [SUCCESS] Added action: launchApp
[[09:13:12]] [SUCCESS] Added action: terminateApp
[[09:13:12]] [INFO] All actions cleared
[[09:13:12]] [INFO] Cleaning up screenshots...
[[09:13:09]] [SUCCESS] Screenshot refreshed successfully
[[09:13:08]] [SUCCESS] Screenshot refreshed
[[09:13:08]] [INFO] Refreshing screenshot...
[[09:13:07]] [SUCCESS] Connected to device: ************:33739
[[09:13:07]] [INFO] Device info updated: RMX2151
[[09:12:52]] [INFO] Connecting to device: ************:33739 (Platform: Android)...
[[09:12:51]] [SUCCESS] Found 1 device(s)
[[09:12:50]] [INFO] Refreshing device list...
