Action Log - 2025-07-12 20:54:45
================================================================================

[[20:54:45]] [INFO] Generating execution report...
[[20:54:45]] [SUCCESS] All tests passed successfully!
[[20:54:45]] [INFO] Moving to the next test case after failure (server will handle retry)
[[20:54:45]] [ERROR] Multi Step action step 3 failed: Element not visible after 2 swipe(s)
[[20:52:06]] [SUCCESS] Screenshot refreshed successfully
[[20:52:06]] [SUCCESS] Screenshot refreshed successfully
[[20:52:05]] [INFO] Executing Multi Step action step 3/48: Swipe up till element xpath: "//android.widget.Button[@text="Continue to details"]" is visible
[[20:52:05]] [SUCCESS] Screenshot refreshed
[[20:52:05]] [INFO] Refreshing screenshot...
[[20:52:03]] [INFO] Executing Multi Step action step 2/48: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[20:52:03]] [SUCCESS] Screenshot refreshed successfully
[[20:52:03]] [SUCCESS] Screenshot refreshed successfully
[[20:52:02]] [SUCCESS] Screenshot refreshed
[[20:52:02]] [INFO] Refreshing screenshot...
[[20:51:56]] [SUCCESS] Screenshot refreshed successfully
[[20:51:56]] [SUCCESS] Screenshot refreshed successfully
[[20:51:56]] [INFO] Executing Multi Step action step 1/48: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[20:51:56]] [INFO] Loaded 48 steps from test case: Delivery Buy Steps_AU-ANDROID
[[20:51:56]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[20:51:56]] [INFO] 24xQv87uTq=running
[[20:51:56]] [INFO] Executing action 27/27: Execute Test Case: Delivery Buy Steps_AU-ANDROID (47 steps)
[[20:51:55]] [SUCCESS] Screenshot refreshed
[[20:51:55]] [INFO] Refreshing screenshot...
[[20:51:55]] [INFO] zrdO3PVkX3=pass
[[20:51:53]] [SUCCESS] Screenshot refreshed successfully
[[20:51:53]] [SUCCESS] Screenshot refreshed successfully
[[20:51:53]] [INFO] zrdO3PVkX3=running
[[20:51:53]] [INFO] Executing action 26/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[20:51:52]] [SUCCESS] Screenshot refreshed
[[20:51:52]] [INFO] Refreshing screenshot...
[[20:51:52]] [INFO] F1olhgKhUt=pass
[[20:51:50]] [SUCCESS] Screenshot refreshed successfully
[[20:51:50]] [SUCCESS] Screenshot refreshed successfully
[[20:51:50]] [INFO] F1olhgKhUt=running
[[20:51:50]] [INFO] Executing action 25/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[20:51:49]] [SUCCESS] Screenshot refreshed
[[20:51:49]] [INFO] Refreshing screenshot...
[[20:51:49]] [INFO] FnrbyHq7bU=pass
[[20:51:47]] [SUCCESS] Screenshot refreshed successfully
[[20:51:47]] [SUCCESS] Screenshot refreshed successfully
[[20:51:46]] [INFO] FnrbyHq7bU=running
[[20:51:46]] [INFO] Executing action 24/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[20:51:46]] [SUCCESS] Screenshot refreshed
[[20:51:46]] [INFO] Refreshing screenshot...
[[20:51:46]] [INFO] nAB6Q8LAdv=pass
[[20:51:14]] [SUCCESS] Screenshot refreshed successfully
[[20:51:14]] [SUCCESS] Screenshot refreshed successfully
[[20:51:14]] [INFO] nAB6Q8LAdv=running
[[20:51:14]] [INFO] Executing action 23/27: Wait till xpath=//android.widget.Button[@text="Filter"]
[[20:51:13]] [SUCCESS] Screenshot refreshed
[[20:51:13]] [INFO] Refreshing screenshot...
[[20:51:13]] [INFO] JRheeTvpJf=pass
[[20:51:11]] [SUCCESS] Screenshot refreshed successfully
[[20:51:11]] [SUCCESS] Screenshot refreshed successfully
[[20:51:11]] [INFO] JRheeTvpJf=running
[[20:51:11]] [INFO] Executing action 22/27: Input text: "Uno Card"
[[20:51:10]] [SUCCESS] Screenshot refreshed
[[20:51:10]] [INFO] Refreshing screenshot...
[[20:51:10]] [INFO] o1gHFWhXTL=pass
[[20:51:06]] [SUCCESS] Screenshot refreshed successfully
[[20:51:06]] [SUCCESS] Screenshot refreshed successfully
[[20:51:05]] [INFO] o1gHFWhXTL=running
[[20:51:05]] [INFO] Executing action 21/27: Tap on Text: "Find"
[[20:51:05]] [SUCCESS] Screenshot refreshed
[[20:51:05]] [INFO] Refreshing screenshot...
[[20:51:05]] [INFO] cKNu2QoRC1=pass
[[20:51:02]] [SUCCESS] Screenshot refreshed successfully
[[20:51:02]] [SUCCESS] Screenshot refreshed successfully
[[20:51:02]] [INFO] cKNu2QoRC1=running
[[20:51:02]] [INFO] Executing action 20/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[20:51:01]] [SUCCESS] Screenshot refreshed
[[20:51:01]] [INFO] Refreshing screenshot...
[[20:51:01]] [INFO] OyUowAaBzD=pass
[[20:50:50]] [SUCCESS] Screenshot refreshed successfully
[[20:50:50]] [SUCCESS] Screenshot refreshed successfully
[[20:50:50]] [INFO] OyUowAaBzD=running
[[20:50:50]] [INFO] Executing action 19/27: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[20:50:49]] [SUCCESS] Screenshot refreshed
[[20:50:49]] [INFO] Refreshing screenshot...
[[20:50:49]] [INFO] Ob26qqcA0p=pass
[[20:50:44]] [SUCCESS] Screenshot refreshed successfully
[[20:50:44]] [SUCCESS] Screenshot refreshed successfully
[[20:50:44]] [INFO] Ob26qqcA0p=running
[[20:50:44]] [INFO] Executing action 18/27: Swipe from (50%, 70%) to (50%, 30%)
[[20:50:44]] [SUCCESS] Screenshot refreshed
[[20:50:44]] [INFO] Refreshing screenshot...
[[20:50:44]] [INFO] k3mu9Mt7Ec=pass
[[20:50:08]] [INFO] k3mu9Mt7Ec=running
[[20:50:08]] [INFO] Executing action 17/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[20:50:08]] [SUCCESS] Screenshot refreshed successfully
[[20:50:08]] [SUCCESS] Screenshot refreshed successfully
[[20:50:08]] [SUCCESS] Screenshot refreshed
[[20:50:08]] [INFO] Refreshing screenshot...
[[20:50:08]] [INFO] FFM0CCo6Qg=pass
[[20:50:02]] [INFO] FFM0CCo6Qg=running
[[20:50:02]] [INFO] Executing action 16/27: Tap on image: bag-close-android.png
[[20:50:02]] [SUCCESS] Screenshot refreshed successfully
[[20:50:02]] [SUCCESS] Screenshot refreshed successfully
[[20:50:02]] [SUCCESS] Screenshot refreshed
[[20:50:02]] [INFO] Refreshing screenshot...
[[20:50:02]] [INFO] LWXWKZE4UV=pass
[[20:49:50]] [SUCCESS] Screenshot refreshed successfully
[[20:49:50]] [SUCCESS] Screenshot refreshed successfully
[[20:49:50]] [INFO] LWXWKZE4UV=running
[[20:49:50]] [INFO] Executing action 15/27: Tap on image: remove-btn-android.png
[[20:49:49]] [SUCCESS] Screenshot refreshed
[[20:49:49]] [INFO] Refreshing screenshot...
[[20:49:49]] [INFO] Qbg9bipTGs=pass
[[20:49:45]] [SUCCESS] Screenshot refreshed successfully
[[20:49:45]] [SUCCESS] Screenshot refreshed successfully
[[20:49:45]] [INFO] Qbg9bipTGs=running
[[20:49:45]] [INFO] Executing action 14/27: Swipe from (50%, 70%) to (50%, 50%)
[[20:49:44]] [SUCCESS] Screenshot refreshed
[[20:49:44]] [INFO] Refreshing screenshot...
[[20:49:44]] [INFO] 7SpDO20tS2=pass
[[20:49:33]] [INFO] 7SpDO20tS2=running
[[20:49:33]] [INFO] Executing action 13/27: Wait for 10 ms
[[20:49:32]] [SUCCESS] Screenshot refreshed successfully
[[20:49:32]] [SUCCESS] Screenshot refreshed successfully
[[20:49:32]] [SUCCESS] Screenshot refreshed
[[20:49:32]] [INFO] Refreshing screenshot...
[[20:49:32]] [INFO] drbQBpgBfM=pass
[[20:49:29]] [SUCCESS] Screenshot refreshed successfully
[[20:49:29]] [SUCCESS] Screenshot refreshed successfully
[[20:49:29]] [INFO] drbQBpgBfM=running
[[20:49:29]] [INFO] Executing action 12/27: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[20:49:28]] [SUCCESS] Screenshot refreshed
[[20:49:28]] [INFO] Refreshing screenshot...
[[20:49:28]] [INFO] F1olhgKhUt=pass
[[20:49:25]] [SUCCESS] Screenshot refreshed successfully
[[20:49:25]] [SUCCESS] Screenshot refreshed successfully
[[20:49:25]] [INFO] F1olhgKhUt=running
[[20:49:25]] [INFO] Executing action 11/27: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[20:49:24]] [SUCCESS] Screenshot refreshed
[[20:49:24]] [INFO] Refreshing screenshot...
[[20:49:24]] [INFO] FnrbyHq7bU=pass
[[20:49:22]] [SUCCESS] Screenshot refreshed successfully
[[20:49:22]] [SUCCESS] Screenshot refreshed successfully
[[20:49:21]] [INFO] FnrbyHq7bU=running
[[20:49:21]] [INFO] Executing action 10/27: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[20:49:21]] [SUCCESS] Screenshot refreshed
[[20:49:21]] [INFO] Refreshing screenshot...
[[20:49:21]] [INFO] nAB6Q8LAdv=pass
[[20:49:14]] [INFO] nAB6Q8LAdv=running
[[20:49:14]] [INFO] Executing action 9/27: Wait till text appears: "Filter"
[[20:49:14]] [SUCCESS] Screenshot refreshed successfully
[[20:49:14]] [SUCCESS] Screenshot refreshed successfully
[[20:49:14]] [SUCCESS] Screenshot refreshed
[[20:49:14]] [INFO] Refreshing screenshot...
[[20:49:14]] [INFO] JRheeTvpJf=pass
[[20:49:12]] [SUCCESS] Screenshot refreshed successfully
[[20:49:12]] [SUCCESS] Screenshot refreshed successfully
[[20:49:12]] [INFO] JRheeTvpJf=running
[[20:49:12]] [INFO] Executing action 8/27: Input text: "Uno Card"
[[20:49:12]] [SUCCESS] Screenshot refreshed
[[20:49:12]] [INFO] Refreshing screenshot...
[[20:49:12]] [INFO] o1gHFWhXTL=pass
[[20:49:08]] [SUCCESS] Screenshot refreshed successfully
[[20:49:08]] [SUCCESS] Screenshot refreshed successfully
[[20:49:07]] [INFO] o1gHFWhXTL=running
[[20:49:07]] [INFO] Executing action 7/27: Tap on Text: "Find"
[[20:49:06]] [SUCCESS] Screenshot refreshed
[[20:49:06]] [INFO] Refreshing screenshot...
[[20:49:06]] [INFO] RLznb7o3ag=pass
[[20:48:55]] [SUCCESS] Screenshot refreshed successfully
[[20:48:55]] [SUCCESS] Screenshot refreshed successfully
[[20:48:54]] [INFO] RLznb7o3ag=running
[[20:48:54]] [INFO] Executing action 6/27: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[20:48:54]] [SUCCESS] Screenshot refreshed
[[20:48:54]] [INFO] Refreshing screenshot...
[[20:48:54]] [SUCCESS] Screenshot refreshed
[[20:48:54]] [INFO] Refreshing screenshot...
[[20:48:51]] [SUCCESS] Screenshot refreshed successfully
[[20:48:51]] [SUCCESS] Screenshot refreshed successfully
[[20:48:51]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[20:48:51]] [SUCCESS] Screenshot refreshed
[[20:48:51]] [INFO] Refreshing screenshot...
[[20:48:48]] [SUCCESS] Screenshot refreshed successfully
[[20:48:48]] [SUCCESS] Screenshot refreshed successfully
[[20:48:48]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[20:48:48]] [SUCCESS] Screenshot refreshed
[[20:48:48]] [INFO] Refreshing screenshot...
[[20:48:45]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[20:48:45]] [SUCCESS] Screenshot refreshed successfully
[[20:48:45]] [SUCCESS] Screenshot refreshed successfully
[[20:48:45]] [SUCCESS] Screenshot refreshed
[[20:48:45]] [INFO] Refreshing screenshot...
[[20:48:43]] [SUCCESS] Screenshot refreshed successfully
[[20:48:43]] [SUCCESS] Screenshot refreshed successfully
[[20:48:43]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[20:48:42]] [SUCCESS] Screenshot refreshed
[[20:48:42]] [INFO] Refreshing screenshot...
[[20:48:18]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[20:48:18]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[20:48:18]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[20:48:18]] [INFO] g052Oo1Gcl=running
[[20:48:18]] [INFO] Executing action 5/27: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[20:48:18]] [SUCCESS] Screenshot refreshed successfully
[[20:48:18]] [SUCCESS] Screenshot refreshed successfully
[[20:48:17]] [SUCCESS] Screenshot refreshed
[[20:48:17]] [INFO] Refreshing screenshot...
[[20:48:17]] [INFO] J9loj6Zs95K=pass
[[20:48:16]] [INFO] J9loj6Zs95K=running
[[20:48:16]] [INFO] Executing action 4/27: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[20:48:15]] [SUCCESS] Screenshot refreshed successfully
[[20:48:15]] [SUCCESS] Screenshot refreshed successfully
[[20:48:15]] [SUCCESS] Screenshot refreshed
[[20:48:15]] [INFO] Refreshing screenshot...
[[20:48:15]] [INFO] Y8v5g7AJD1i=pass
[[20:48:09]] [INFO] Y8v5g7AJD1i=running
[[20:48:09]] [INFO] Executing action 3/27: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[20:48:08]] [SUCCESS] Screenshot refreshed successfully
[[20:48:08]] [SUCCESS] Screenshot refreshed successfully
[[20:48:08]] [SUCCESS] Screenshot refreshed
[[20:48:08]] [INFO] Refreshing screenshot...
[[20:48:08]] [INFO] eqHB0Nj1He=pass
[[20:48:05]] [SUCCESS] Screenshot refreshed successfully
[[20:48:05]] [SUCCESS] Screenshot refreshed successfully
[[20:48:05]] [INFO] eqHB0Nj1He=running
[[20:48:05]] [INFO] Executing action 2/27: Launch app: au.com.kmart
[[20:48:05]] [SUCCESS] Screenshot refreshed
[[20:48:05]] [INFO] Refreshing screenshot...
[[20:48:05]] [INFO] H9fkkqcFbZ=pass
[[20:48:03]] [INFO] H9fkkqcFbZ=running
[[20:48:03]] [INFO] Executing action 1/27: Terminate app: au.com.kmart
[[20:48:03]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[20:48:03]] [SUCCESS] Cleared 34 screenshots from database
[[20:48:03]] [INFO] Clearing screenshots from database before execution...
[[20:48:03]] [SUCCESS] All screenshots deleted successfully
[[20:48:03]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:48:03]] [INFO] Skipping report initialization - single test case execution
[[20:48:01]] [SUCCESS] All screenshots deleted successfully
[[20:48:01]] [SUCCESS] Loaded test case "Delivery & CNC_AU-ANDROID" with 27 actions
[[20:48:01]] [SUCCESS] Added action: multiStep
[[20:48:01]] [SUCCESS] Added action: tapIfLocatorExists
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: waitTill
[[20:48:01]] [SUCCESS] Added action: text
[[20:48:01]] [SUCCESS] Added action: tapOnText
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: swipe
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: swipe
[[20:48:01]] [SUCCESS] Added action: wait
[[20:48:01]] [SUCCESS] Added action: tapIfLocatorExists
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: waitTill
[[20:48:01]] [SUCCESS] Added action: text
[[20:48:01]] [SUCCESS] Added action: tapOnText
[[20:48:01]] [SUCCESS] Added action: waitTill
[[20:48:01]] [SUCCESS] Added action: multiStep
[[20:48:01]] [SUCCESS] Added action: waitTill
[[20:48:01]] [SUCCESS] Added action: tap
[[20:48:01]] [SUCCESS] Added action: launchApp
[[20:48:01]] [SUCCESS] Added action: terminateApp
[[20:48:01]] [INFO] All actions cleared
[[20:48:01]] [INFO] Cleaning up screenshots...
[[20:47:54]] [SUCCESS] Screenshot refreshed successfully
[[20:47:53]] [SUCCESS] Screenshot refreshed
[[20:47:53]] [INFO] Refreshing screenshot...
[[20:47:52]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[20:47:52]] [INFO] Device info updated: RMX2151
[[20:47:46]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[20:47:44]] [SUCCESS] Found 1 device(s)
[[20:47:44]] [INFO] Refreshing device list...
