{"name": "UI Execution 11/07/2025, 16:02:24", "testCases": [{"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            55 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3471ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2691ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1811ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2367ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1549ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2601ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "3128ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "3003ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2131ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "2431ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show (\")]", "status": "passed", "duration": "2384ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1720ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2694ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3074ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10018ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2169ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2739ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2352ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "2000ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2926ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Swipe up till element accessibilityid: \"Learn more about AfterPay\" is visible", "status": "passed", "duration": "7079ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "4971ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "unknown", "duration": "13773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2369ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "4968ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "1535ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2847ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "5022ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2542ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "18951ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1462ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2225ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3278ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2472ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2339ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2665ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2188ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2757ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2746ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2387ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"mat\"", "status": "passed", "duration": "2591ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "3491ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2723ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2772ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "19799ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3088ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3539ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5270ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "4074ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2517ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1077ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            30 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2186ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5634ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1184ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "3775ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4388ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2893ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2112ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "passed", "duration": "5018ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1667ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3464ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3483ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "passed", "duration": "1535ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2525ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2976ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"Brunswick\"", "status": "passed", "duration": "3336ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10029ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2413ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2357ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "4122ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2959ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2565ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2255ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2357ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5514ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "3142ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2538ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (34 steps)", "status": "passed", "duration": "0ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            98 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2185ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6387ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1201ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2204ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2767ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3343ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2777ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3960ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2002ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2623ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5528ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2446ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2579ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "2422ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1186ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2165ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2785ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3342ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2793ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "4806ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtStart Shopping\"]\" exists", "status": "passed", "duration": "1406ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtStart Shopping\"]", "status": "passed", "duration": "2362ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2604ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2042ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "6482ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2439ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3215ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4374ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2884ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "3695ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2720ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2491ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeStaticText[@name=\"Already a member?\"]\" is visible", "status": "passed", "duration": "15821ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3841ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1184ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2318ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2922ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname1]\"", "status": "passed", "duration": "3463ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "4811ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3152ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "4069ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3278ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5502ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "3619ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3229ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2528ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2367ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1186ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2431ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "6055ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2457ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2593ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4416ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2746ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2130ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2690ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibility_id: \"Add to bag\" is visible", "status": "passed", "duration": "6612ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5739ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5020ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2969ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3631ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1969ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2581ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "7016ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4644ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3448ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1192ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2807ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3311ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "4423ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3070ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "5391ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1980ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2549ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2543ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2414ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2348ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5675ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2469ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3237ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "3951ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "6960ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "5823ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Tap on Text: \"in\"", "status": "passed", "duration": "3434ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1160ms", "action_id": "XOaZPEqzKU", "screenshot_filename": "XOaZPEqzKU.png", "report_screenshot": "XOaZPEqzKU.png", "resolved_screenshot": "screenshots/XOaZPEqzKU.png", "clean_action_id": "XOaZPEqzKU", "prefixed_action_id": "al_XOaZPEqzKU", "action_id_screenshot": "screenshots/XOaZPEqzKU.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "4968ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "4198ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2551ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2346ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2368ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5482ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2543ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2188ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "7202ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1182ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2238ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2762ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname-op]\"", "status": "passed", "duration": "3327ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2786ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@6\"", "status": "passed", "duration": "4195ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4355ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2761ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2151ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "4354ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2391ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5634ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2991ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "12944ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2698ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6079ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "3233ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3409ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4366ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "iOS Function: text - Text: \"P_43386093\"", "status": "passed", "duration": "2707ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2513ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2526ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6579ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2983ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2955ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1631ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2388ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "3043ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "4401ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2985ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "2370ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2793ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3249ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3481ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4294ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2026ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2567ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2332ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2272ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "3926ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "3673ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "3796ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2887ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2321ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5454ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "3928ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod-Signin\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2195ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5238ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1180ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2174ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2759ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3356ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "4454ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3108ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1588ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2608ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5518ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2446ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2153ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4490ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1193ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2216ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3080ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "3402ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2894ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3435ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "3020ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3198ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2861ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3741ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4286ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2456ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2163ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4527ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1187ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2230ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3087ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "3525ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10016ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "1939ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1666ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1606ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1584ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1637ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1602ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1653ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "6270ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5504ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2548ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2152ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6039ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1165ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2186ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Sign in with Google\"]\" is visible", "status": "passed", "duration": "5258ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2731ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2949ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "3028ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5593ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1118ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            62 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2185ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4437ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1217ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2805ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1915ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "3045ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5028ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2619ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "14704ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2545ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "6238ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "4958ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2367ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2632ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1499ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "2499ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "1541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2367ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "11449ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "3321ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2717ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2443ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "3709ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2334ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "3080ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2312ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "3079ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2291ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2781ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "3062ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "3313ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "4285ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "4248ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "4934ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "3192ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "4298ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "passed", "duration": "4295ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1636ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4398ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "4288ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2321ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "2085ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5473ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "2951ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3732ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "passed", "duration": "5346ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3663ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "passed", "duration": "1756ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2316ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Tap on Text: \"Invite\"", "status": "passed", "duration": "3599ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "passed", "duration": "1800ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2878ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2968ms", "action_id": "XOaZPEqzKU", "screenshot_filename": "XOaZPEqzKU.png", "report_screenshot": "XOaZPEqzKU.png", "resolved_screenshot": "screenshots/XOaZPEqzKU.png", "clean_action_id": "XOaZPEqzKU", "prefixed_action_id": "al_XOaZPEqzKU", "action_id_screenshot": "screenshots/XOaZPEqzKU.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2262ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5493ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2972ms", "action_id": "sdqCYvk2Du", "screenshot_filename": "sdqCYvk2Du.png", "report_screenshot": "sdqCYvk2Du.png", "resolved_screenshot": "screenshots/sdqCYvk2Du.png", "clean_action_id": "sdqCYvk2Du", "prefixed_action_id": "al_sdqCYvk2Du", "action_id_screenshot": "screenshots/sdqCYvk2Du.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2191ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "2896ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "510ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "1378ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "1416ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1407ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "3729ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2566ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "passed", "duration": "2384ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "passed", "duration": "2390ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "passed", "duration": "1649ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2498ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[uname-op]\"", "status": "passed", "duration": "1867ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "passed", "duration": "2517ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "passed", "duration": "1537ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2284ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1194ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2811ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname-op]\"", "status": "passed", "duration": "4972ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2780ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd-op]\"", "status": "passed", "duration": "3101ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "1510ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "passed", "duration": "2422ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "1458ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2399ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "3073ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "3269ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "passed", "duration": "1640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "passed", "duration": "2005ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "3217ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3234ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "4225ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "3264ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"btnUpdate\"]", "status": "passed", "duration": "20857ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2352ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4279ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "iOS Function: text - Text: \"P_42691341\"", "status": "passed", "duration": "2671ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2456ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "4328ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on image if exists: add-to-bag-ip14.png", "status": "passed", "duration": "40ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap if locator exists: accessibility_id=\"Add to bag\"", "status": "passed", "duration": "7424ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Save my location\"]", "status": "passed", "duration": "11238ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2962ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3441ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery Buy Steps (41 steps)", "status": "passed", "duration": "0ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3339ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2879ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "4029ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1062ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23226ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2375ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5163ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1182ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2148ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2250ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "5070ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3372ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "3246ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3387ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3270ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "8179ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4310ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2704ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2115ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3935ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "passed", "duration": "4825ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "3321ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3373ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3245ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with text=\"Tarneit\" exists", "status": "passed", "duration": "15359ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2220ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2723ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2909ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3973ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4381ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3525ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "3191ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3364ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3342ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "14029ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "18277ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "2113ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3304ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3484ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[@name=\"Delivery options\"]/XCUIElementTypeButton[3]", "status": "passed", "duration": "2326ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "4040ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "2244ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3658ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "5076ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "passed", "duration": "5325ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3428ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4900ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2993ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2621ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "1872ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "3898ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"Melbourne\" exists", "status": "passed", "duration": "16828ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3397ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "6982ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            67 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2194ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "3209ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1137ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "194ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1263ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2983ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5018ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "988ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3297ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "293ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "681ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "247ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "692ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "261ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "690ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "284ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "122ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "814ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5025ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3272ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "3418ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2849ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2933ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "2216ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1372ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart au\"", "status": "passed", "duration": "1734ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1152ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1620ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "2553ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "2105ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "3073ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21062ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2840ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "5671ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3314ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "4857ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2644ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "3112ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21467ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2937ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4624ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "18409ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "3167ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3554ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "11993ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "4291ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "5783ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2444ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2337ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]\" is visible", "status": "passed", "duration": "4988ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "2467ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "2530ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2267ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "5897ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "If exists: accessibility_id=\"Add to bag\" (timeout: 15s) → Then tap at (0, 0)", "status": "passed", "duration": "4762ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3381ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2637ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "3484ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "11837ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "If exists: id=\"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" (timeout: 20s) → Then tap at (0, 0)", "status": "passed", "duration": "21085ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            66 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2190ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2958ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "3106ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2860ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "30008ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "4179ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "3198ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2610ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "130ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2421ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4400ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "4145ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Execute Test Case: Click_Paginations (10 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "5090ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2762ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2976ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2941ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "3546ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "4058ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3825ms", "action_id": "OQ1fr8NUlV", "screenshot_filename": "OQ1fr8NUlV.png", "report_screenshot": "OQ1fr8NUlV.png", "resolved_screenshot": "screenshots/OQ1fr8NUlV.png", "clean_action_id": "OQ1fr8NUlV", "prefixed_action_id": "al_OQ1fr8NUlV", "action_id_screenshot": "screenshots/OQ1fr8NUlV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2379ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4483ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1176ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2873ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3369ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2797ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3143ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "3135ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4359ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "iOS Function: text - Text: \"enn[cooker-id]\"", "status": "passed", "duration": "2834ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2147ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2728ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "4607ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"(\"]/following-sibling::*[1]", "status": "passed", "duration": "3023ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeStaticText[@name=\"Value\"])[1]\" exists", "status": "passed", "duration": "1948ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "14141ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2989ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2227ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2228ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2338ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2425ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2991ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2607ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "status": "passed", "duration": "2591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1180ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1735ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Cancel\"", "status": "passed", "duration": "3766ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "5052ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Tap on Text: \"Click\"", "status": "passed", "duration": "3294ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "status": "passed", "duration": "2632ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2593ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]\" is visible", "status": "passed", "duration": "11643ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "status": "passed", "duration": "2618ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2325ms", "action_id": "XOaZPEqzKU", "screenshot_filename": "XOaZPEqzKU.png", "report_screenshot": "XOaZPEqzKU.png", "resolved_screenshot": "screenshots/XOaZPEqzKU.png", "clean_action_id": "XOaZPEqzKU", "prefixed_action_id": "al_XOaZPEqzKU", "action_id_screenshot": "screenshots/XOaZPEqzKU.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"About KHub Stores\"]", "status": "passed", "duration": "2609ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2736ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "Swipe from (50%, 30%) to (50%, 70%)", "status": "passed", "duration": "3004ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2601ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2248ms", "action_id": "sdqCYvk2Du", "screenshot_filename": "sdqCYvk2Du.png", "report_screenshot": "sdqCYvk2Du.png", "resolved_screenshot": "screenshots/sdqCYvk2Du.png", "clean_action_id": "sdqCYvk2Du", "prefixed_action_id": "al_sdqCYvk2Du", "action_id_screenshot": "screenshots/sdqCYvk2Du.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2308ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1189ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1711ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1063ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}], "passed": 10, "failed": 0, "skipped": 0, "status": "passed"}