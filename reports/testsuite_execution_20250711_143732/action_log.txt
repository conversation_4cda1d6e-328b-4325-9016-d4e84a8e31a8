Action Log - 2025-07-11 16:02:24
================================================================================

[[16:02:23]] [INFO] Generating execution report...
[[16:02:23]] [SUCCESS] All tests passed successfully!
[[16:02:23]] [SUCCESS] Screenshot refreshed
[[16:02:23]] [INFO] Refreshing screenshot...
[[16:02:23]] [SUCCESS] Screenshot refreshed
[[16:02:23]] [INFO] Refreshing screenshot...
[[16:02:20]] [SUCCESS] Screenshot refreshed successfully
[[16:02:20]] [SUCCESS] Screenshot refreshed successfully
[[16:02:20]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[16:02:19]] [SUCCESS] Screenshot refreshed
[[16:02:19]] [INFO] Refreshing screenshot...
[[16:02:08]] [SUCCESS] Screenshot refreshed successfully
[[16:02:08]] [SUCCESS] Screenshot refreshed successfully
[[16:02:07]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[16:02:07]] [SUCCESS] Screenshot refreshed
[[16:02:07]] [INFO] Refreshing screenshot...
[[16:02:02]] [SUCCESS] Screenshot refreshed successfully
[[16:02:02]] [SUCCESS] Screenshot refreshed successfully
[[16:02:02]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[16:02:01]] [SUCCESS] Screenshot refreshed
[[16:02:01]] [INFO] Refreshing screenshot...
[[16:01:58]] [SUCCESS] Screenshot refreshed successfully
[[16:01:58]] [SUCCESS] Screenshot refreshed successfully
[[16:01:57]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[16:01:57]] [SUCCESS] Screenshot refreshed
[[16:01:57]] [INFO] Refreshing screenshot...
[[16:01:50]] [SUCCESS] Screenshot refreshed successfully
[[16:01:50]] [SUCCESS] Screenshot refreshed successfully
[[16:01:49]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[16:01:49]] [SUCCESS] Screenshot refreshed
[[16:01:49]] [INFO] Refreshing screenshot...
[[16:01:43]] [SUCCESS] Screenshot refreshed successfully
[[16:01:43]] [SUCCESS] Screenshot refreshed successfully
[[16:01:43]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[16:01:42]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[16:01:42]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[16:01:42]] [INFO] Ll4UlkE3L9=running
[[16:01:42]] [INFO] Executing action 591/591: cleanupSteps action
[[16:01:42]] [SUCCESS] Screenshot refreshed
[[16:01:42]] [INFO] Refreshing screenshot...
[[16:01:42]] [INFO] 25UEKPIknm=pass
[[16:01:39]] [SUCCESS] Screenshot refreshed successfully
[[16:01:39]] [SUCCESS] Screenshot refreshed successfully
[[16:01:38]] [INFO] 25UEKPIknm=running
[[16:01:38]] [INFO] Executing action 590/591: Terminate app: env[appid]
[[16:01:38]] [SUCCESS] Screenshot refreshed
[[16:01:38]] [INFO] Refreshing screenshot...
[[16:01:38]] [INFO] UqgDn5CuPY=pass
[[16:01:35]] [SUCCESS] Screenshot refreshed successfully
[[16:01:35]] [SUCCESS] Screenshot refreshed successfully
[[16:01:34]] [INFO] UqgDn5CuPY=running
[[16:01:34]] [INFO] Executing action 589/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[16:01:34]] [SUCCESS] Screenshot refreshed
[[16:01:34]] [INFO] Refreshing screenshot...
[[16:01:34]] [INFO] VfTTTtrliQ=pass
[[16:01:31]] [SUCCESS] Screenshot refreshed successfully
[[16:01:31]] [SUCCESS] Screenshot refreshed successfully
[[16:01:30]] [INFO] VfTTTtrliQ=running
[[16:01:30]] [INFO] Executing action 588/591: iOS Function: alert_accept
[[16:01:30]] [SUCCESS] Screenshot refreshed
[[16:01:30]] [INFO] Refreshing screenshot...
[[16:01:30]] [INFO] ipT2XD9io6=pass
[[16:01:26]] [SUCCESS] Screenshot refreshed successfully
[[16:01:26]] [SUCCESS] Screenshot refreshed successfully
[[16:01:26]] [INFO] ipT2XD9io6=running
[[16:01:26]] [INFO] Executing action 587/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[16:01:25]] [SUCCESS] Screenshot refreshed
[[16:01:25]] [INFO] Refreshing screenshot...
[[16:01:25]] [INFO] OKCHAK6HCJ=pass
[[16:01:21]] [INFO] OKCHAK6HCJ=running
[[16:01:21]] [INFO] Executing action 586/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[16:01:21]] [SUCCESS] Screenshot refreshed successfully
[[16:01:21]] [SUCCESS] Screenshot refreshed successfully
[[16:01:21]] [SUCCESS] Screenshot refreshed
[[16:01:21]] [INFO] Refreshing screenshot...
[[16:01:21]] [INFO] AEnFqnkOa1=pass
[[16:01:17]] [INFO] AEnFqnkOa1=running
[[16:01:17]] [INFO] Executing action 585/591: Tap on image: banner-close-updated.png
[[16:01:17]] [SUCCESS] Screenshot refreshed successfully
[[16:01:17]] [SUCCESS] Screenshot refreshed successfully
[[16:01:16]] [SUCCESS] Screenshot refreshed
[[16:01:16]] [INFO] Refreshing screenshot...
[[16:01:16]] [INFO] z1CfcW4xYT=pass
[[16:01:12]] [SUCCESS] Screenshot refreshed successfully
[[16:01:12]] [SUCCESS] Screenshot refreshed successfully
[[16:01:12]] [INFO] z1CfcW4xYT=running
[[16:01:12]] [INFO] Executing action 584/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[16:01:11]] [SUCCESS] Screenshot refreshed
[[16:01:11]] [INFO] Refreshing screenshot...
[[16:01:11]] [INFO] dJNRgTXoqs=pass
[[16:01:07]] [SUCCESS] Screenshot refreshed successfully
[[16:01:07]] [SUCCESS] Screenshot refreshed successfully
[[16:01:07]] [INFO] dJNRgTXoqs=running
[[16:01:07]] [INFO] Executing action 583/591: Swipe from (50%, 30%) to (50%, 70%)
[[16:01:06]] [SUCCESS] Screenshot refreshed
[[16:01:06]] [INFO] Refreshing screenshot...
[[16:01:06]] [INFO] ceF4VRTJlO=pass
[[16:01:02]] [SUCCESS] Screenshot refreshed successfully
[[16:01:02]] [SUCCESS] Screenshot refreshed successfully
[[16:01:02]] [INFO] ceF4VRTJlO=running
[[16:01:02]] [INFO] Executing action 582/591: Tap on image: banner-close-updated.png
[[16:01:01]] [SUCCESS] Screenshot refreshed
[[16:01:01]] [INFO] Refreshing screenshot...
[[16:01:01]] [INFO] 8hCPyY2zPt=pass
[[16:00:57]] [SUCCESS] Screenshot refreshed successfully
[[16:00:57]] [SUCCESS] Screenshot refreshed successfully
[[16:00:57]] [INFO] 8hCPyY2zPt=running
[[16:00:57]] [INFO] Executing action 581/591: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[16:00:56]] [SUCCESS] Screenshot refreshed
[[16:00:56]] [INFO] Refreshing screenshot...
[[16:00:56]] [INFO] r0FfJ85LFM=pass
[[16:00:52]] [SUCCESS] Screenshot refreshed successfully
[[16:00:52]] [SUCCESS] Screenshot refreshed successfully
[[16:00:52]] [INFO] r0FfJ85LFM=running
[[16:00:52]] [INFO] Executing action 580/591: Tap on image: banner-close-updated.png
[[16:00:52]] [SUCCESS] Screenshot refreshed
[[16:00:52]] [INFO] Refreshing screenshot...
[[16:00:52]] [INFO] 2QEdm5WM18=pass
[[16:00:48]] [SUCCESS] Screenshot refreshed successfully
[[16:00:48]] [SUCCESS] Screenshot refreshed successfully
[[16:00:47]] [INFO] 2QEdm5WM18=running
[[16:00:47]] [INFO] Executing action 579/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[16:00:47]] [SUCCESS] Screenshot refreshed
[[16:00:47]] [INFO] Refreshing screenshot...
[[16:00:47]] [INFO] NW6M15JbAy=pass
[[16:00:34]] [SUCCESS] Screenshot refreshed successfully
[[16:00:34]] [SUCCESS] Screenshot refreshed successfully
[[16:00:34]] [INFO] NW6M15JbAy=running
[[16:00:34]] [INFO] Executing action 578/591: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[16:00:33]] [SUCCESS] Screenshot refreshed
[[16:00:33]] [INFO] Refreshing screenshot...
[[16:00:33]] [INFO] njiHWyVooT=pass
[[16:00:28]] [SUCCESS] Screenshot refreshed successfully
[[16:00:28]] [SUCCESS] Screenshot refreshed successfully
[[16:00:28]] [INFO] njiHWyVooT=running
[[16:00:28]] [INFO] Executing action 577/591: Tap on image: banner-close-updated.png
[[16:00:27]] [SUCCESS] Screenshot refreshed
[[16:00:27]] [INFO] Refreshing screenshot...
[[16:00:27]] [INFO] 93bAew9Y4Y=pass
[[16:00:23]] [SUCCESS] Screenshot refreshed successfully
[[16:00:23]] [SUCCESS] Screenshot refreshed successfully
[[16:00:23]] [INFO] 93bAew9Y4Y=running
[[16:00:23]] [INFO] Executing action 576/591: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[16:00:22]] [SUCCESS] Screenshot refreshed
[[16:00:22]] [INFO] Refreshing screenshot...
[[16:00:22]] [INFO] rPQ5EkTza1=pass
[[16:00:18]] [SUCCESS] Screenshot refreshed successfully
[[16:00:18]] [SUCCESS] Screenshot refreshed successfully
[[16:00:17]] [INFO] rPQ5EkTza1=running
[[16:00:17]] [INFO] Executing action 575/591: Tap on Text: "Click"
[[16:00:17]] [SUCCESS] Screenshot refreshed
[[16:00:17]] [INFO] Refreshing screenshot...
[[16:00:17]] [SUCCESS] Screenshot refreshed
[[16:00:17]] [INFO] Refreshing screenshot...
[[16:00:13]] [SUCCESS] Screenshot refreshed successfully
[[16:00:13]] [SUCCESS] Screenshot refreshed successfully
[[16:00:13]] [INFO] Executing Multi Step action step 7/7: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[16:00:13]] [SUCCESS] Screenshot refreshed
[[16:00:13]] [INFO] Refreshing screenshot...
[[16:00:08]] [SUCCESS] Screenshot refreshed successfully
[[16:00:08]] [SUCCESS] Screenshot refreshed successfully
[[16:00:08]] [INFO] Executing Multi Step action step 6/7: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[16:00:07]] [SUCCESS] Screenshot refreshed
[[16:00:07]] [INFO] Refreshing screenshot...
[[16:00:01]] [SUCCESS] Screenshot refreshed successfully
[[16:00:01]] [SUCCESS] Screenshot refreshed successfully
[[16:00:01]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[16:00:00]] [SUCCESS] Screenshot refreshed
[[16:00:00]] [INFO] Refreshing screenshot...
[[15:59:56]] [SUCCESS] Screenshot refreshed successfully
[[15:59:56]] [SUCCESS] Screenshot refreshed successfully
[[15:59:56]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[15:59:55]] [SUCCESS] Screenshot refreshed
[[15:59:55]] [INFO] Refreshing screenshot...
[[15:59:52]] [SUCCESS] Screenshot refreshed successfully
[[15:59:52]] [SUCCESS] Screenshot refreshed successfully
[[15:59:51]] [INFO] Executing Multi Step action step 3/7: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:59:51]] [SUCCESS] Screenshot refreshed
[[15:59:51]] [INFO] Refreshing screenshot...
[[15:59:46]] [SUCCESS] Screenshot refreshed successfully
[[15:59:46]] [SUCCESS] Screenshot refreshed successfully
[[15:59:46]] [INFO] Executing Multi Step action step 2/7: iOS Function: text - Text: "Notebook"
[[15:59:46]] [SUCCESS] Screenshot refreshed
[[15:59:46]] [INFO] Refreshing screenshot...
[[15:59:38]] [SUCCESS] Screenshot refreshed successfully
[[15:59:38]] [SUCCESS] Screenshot refreshed successfully
[[15:59:37]] [INFO] Executing Multi Step action step 1/7: Tap on Text: "Find"
[[15:59:37]] [INFO] Loaded 7 steps from test case: Search and Add (Notebooks)
[[15:59:37]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[15:59:37]] [INFO] 0YgZZfWdYY=running
[[15:59:37]] [INFO] Executing action 574/591: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[15:59:37]] [SUCCESS] Screenshot refreshed
[[15:59:37]] [INFO] Refreshing screenshot...
[[15:59:37]] [INFO] arH1CZCPXh=pass
[[15:59:30]] [SUCCESS] Screenshot refreshed successfully
[[15:59:30]] [SUCCESS] Screenshot refreshed successfully
[[15:59:29]] [INFO] arH1CZCPXh=running
[[15:59:29]] [INFO] Executing action 573/591: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[15:59:29]] [SUCCESS] Screenshot refreshed
[[15:59:29]] [INFO] Refreshing screenshot...
[[15:59:29]] [INFO] JLAJhxPdsl=pass
[[15:59:23]] [SUCCESS] Screenshot refreshed successfully
[[15:59:23]] [SUCCESS] Screenshot refreshed successfully
[[15:59:23]] [INFO] JLAJhxPdsl=running
[[15:59:23]] [INFO] Executing action 572/591: Tap on Text: "Cancel"
[[15:59:22]] [SUCCESS] Screenshot refreshed
[[15:59:22]] [INFO] Refreshing screenshot...
[[15:59:22]] [INFO] UqgDn5CuPY=pass
[[15:59:19]] [SUCCESS] Screenshot refreshed successfully
[[15:59:19]] [SUCCESS] Screenshot refreshed successfully
[[15:59:19]] [INFO] UqgDn5CuPY=running
[[15:59:19]] [INFO] Executing action 571/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[15:59:18]] [SUCCESS] Screenshot refreshed
[[15:59:18]] [INFO] Refreshing screenshot...
[[15:59:18]] [INFO] VfTTTtrliQ=pass
[[15:59:16]] [SUCCESS] Screenshot refreshed successfully
[[15:59:16]] [SUCCESS] Screenshot refreshed successfully
[[15:59:16]] [INFO] VfTTTtrliQ=running
[[15:59:16]] [INFO] Executing action 570/591: iOS Function: alert_accept
[[15:59:15]] [SUCCESS] Screenshot refreshed
[[15:59:15]] [INFO] Refreshing screenshot...
[[15:59:15]] [INFO] ipT2XD9io6=pass
[[15:59:11]] [SUCCESS] Screenshot refreshed successfully
[[15:59:11]] [SUCCESS] Screenshot refreshed successfully
[[15:59:10]] [INFO] ipT2XD9io6=running
[[15:59:10]] [INFO] Executing action 569/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[15:59:10]] [SUCCESS] Screenshot refreshed
[[15:59:10]] [INFO] Refreshing screenshot...
[[15:59:10]] [INFO] OKCHAK6HCJ=pass
[[15:59:06]] [SUCCESS] Screenshot refreshed successfully
[[15:59:06]] [SUCCESS] Screenshot refreshed successfully
[[15:59:05]] [INFO] OKCHAK6HCJ=running
[[15:59:05]] [INFO] Executing action 568/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[15:59:05]] [SUCCESS] Screenshot refreshed
[[15:59:05]] [INFO] Refreshing screenshot...
[[15:59:05]] [INFO] RbD937Xbte=pass
[[15:58:59]] [SUCCESS] Screenshot refreshed successfully
[[15:58:59]] [SUCCESS] Screenshot refreshed successfully
[[15:58:59]] [INFO] RbD937Xbte=running
[[15:58:59]] [INFO] Executing action 567/591: Tap on Text: "out"
[[15:58:58]] [SUCCESS] Screenshot refreshed
[[15:58:58]] [INFO] Refreshing screenshot...
[[15:58:58]] [INFO] ylslyLAYKb=pass
[[15:58:54]] [SUCCESS] Screenshot refreshed successfully
[[15:58:54]] [SUCCESS] Screenshot refreshed successfully
[[15:58:54]] [INFO] ylslyLAYKb=running
[[15:58:54]] [INFO] Executing action 566/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:58:54]] [SUCCESS] Screenshot refreshed
[[15:58:54]] [INFO] Refreshing screenshot...
[[15:58:54]] [INFO] wguGCt7OoB=pass
[[15:58:50]] [SUCCESS] Screenshot refreshed successfully
[[15:58:50]] [SUCCESS] Screenshot refreshed successfully
[[15:58:49]] [INFO] wguGCt7OoB=running
[[15:58:49]] [INFO] Executing action 565/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:58:49]] [SUCCESS] Screenshot refreshed
[[15:58:49]] [INFO] Refreshing screenshot...
[[15:58:49]] [INFO] RDQCFIxjA0=pass
[[15:58:45]] [SUCCESS] Screenshot refreshed successfully
[[15:58:45]] [SUCCESS] Screenshot refreshed successfully
[[15:58:45]] [INFO] RDQCFIxjA0=running
[[15:58:45]] [INFO] Executing action 564/591: Swipe from (90%, 20%) to (30%, 20%)
[[15:58:44]] [SUCCESS] Screenshot refreshed
[[15:58:44]] [INFO] Refreshing screenshot...
[[15:58:44]] [INFO] x4Mid4HQ0Z=pass
[[15:58:41]] [SUCCESS] Screenshot refreshed successfully
[[15:58:41]] [SUCCESS] Screenshot refreshed successfully
[[15:58:40]] [INFO] x4Mid4HQ0Z=running
[[15:58:40]] [INFO] Executing action 563/591: Swipe from (90%, 20%) to (30%, 20%)
[[15:58:40]] [SUCCESS] Screenshot refreshed
[[15:58:40]] [INFO] Refreshing screenshot...
[[15:58:40]] [INFO] OKCHAK6HCJ=pass
[[15:58:35]] [SUCCESS] Screenshot refreshed successfully
[[15:58:35]] [SUCCESS] Screenshot refreshed successfully
[[15:58:35]] [INFO] OKCHAK6HCJ=running
[[15:58:35]] [INFO] Executing action 562/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[15:58:35]] [SUCCESS] Screenshot refreshed
[[15:58:35]] [INFO] Refreshing screenshot...
[[15:58:35]] [INFO] Ef6OumM2eS=pass
[[15:58:18]] [SUCCESS] Screenshot refreshed successfully
[[15:58:18]] [SUCCESS] Screenshot refreshed successfully
[[15:58:18]] [INFO] Ef6OumM2eS=running
[[15:58:18]] [INFO] Executing action 561/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[15:58:17]] [SUCCESS] Screenshot refreshed
[[15:58:17]] [INFO] Refreshing screenshot...
[[15:58:17]] [INFO] QkaF93zxUg=pass
[[15:58:14]] [SUCCESS] Screenshot refreshed successfully
[[15:58:14]] [SUCCESS] Screenshot refreshed successfully
[[15:58:13]] [INFO] QkaF93zxUg=running
[[15:58:13]] [INFO] Executing action 560/591: Check if element with xpath="(//XCUIElementTypeStaticText[@name="Value"])[1]" exists
[[15:58:13]] [SUCCESS] Screenshot refreshed
[[15:58:13]] [INFO] Refreshing screenshot...
[[15:58:13]] [INFO] HZT2s0AzX7=pass
[[15:58:08]] [SUCCESS] Screenshot refreshed successfully
[[15:58:08]] [SUCCESS] Screenshot refreshed successfully
[[15:58:08]] [INFO] HZT2s0AzX7=running
[[15:58:08]] [INFO] Executing action 559/591: Tap on element with xpath: //XCUIElementTypeStaticText[@name="("]/following-sibling::*[1]
[[15:58:08]] [SUCCESS] Screenshot refreshed
[[15:58:08]] [INFO] Refreshing screenshot...
[[15:58:08]] [INFO] 0bnBNoqPt8=pass
[[15:58:01]] [SUCCESS] Screenshot refreshed successfully
[[15:58:01]] [SUCCESS] Screenshot refreshed successfully
[[15:58:01]] [INFO] 0bnBNoqPt8=running
[[15:58:01]] [INFO] Executing action 558/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:58:01]] [SUCCESS] Screenshot refreshed
[[15:58:01]] [INFO] Refreshing screenshot...
[[15:58:01]] [INFO] xmelRkcdVx=pass
[[15:57:57]] [SUCCESS] Screenshot refreshed successfully
[[15:57:57]] [SUCCESS] Screenshot refreshed successfully
[[15:57:56]] [INFO] xmelRkcdVx=running
[[15:57:56]] [INFO] Executing action 557/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:57:56]] [SUCCESS] Screenshot refreshed
[[15:57:56]] [INFO] Refreshing screenshot...
[[15:57:56]] [INFO] ksCBjJiwHZ=pass
[[15:57:52]] [SUCCESS] Screenshot refreshed successfully
[[15:57:52]] [SUCCESS] Screenshot refreshed successfully
[[15:57:52]] [INFO] ksCBjJiwHZ=running
[[15:57:52]] [INFO] Executing action 556/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:57:51]] [SUCCESS] Screenshot refreshed
[[15:57:51]] [INFO] Refreshing screenshot...
[[15:57:51]] [INFO] RuPGkdCdah=pass
[[15:57:47]] [SUCCESS] Screenshot refreshed successfully
[[15:57:47]] [SUCCESS] Screenshot refreshed successfully
[[15:57:47]] [INFO] RuPGkdCdah=running
[[15:57:47]] [INFO] Executing action 555/591: iOS Function: text - Text: "enn[cooker-id]"
[[15:57:46]] [SUCCESS] Screenshot refreshed
[[15:57:46]] [INFO] Refreshing screenshot...
[[15:57:46]] [INFO] ewuLtuqVuo=pass
[[15:57:41]] [SUCCESS] Screenshot refreshed successfully
[[15:57:41]] [SUCCESS] Screenshot refreshed successfully
[[15:57:40]] [INFO] ewuLtuqVuo=running
[[15:57:40]] [INFO] Executing action 554/591: Tap on Text: "Find"
[[15:57:39]] [SUCCESS] Screenshot refreshed
[[15:57:39]] [INFO] Refreshing screenshot...
[[15:57:39]] [INFO] GTXmST3hEA=pass
[[15:57:34]] [INFO] GTXmST3hEA=running
[[15:57:34]] [INFO] Executing action 553/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:57:14]] [SUCCESS] Screenshot refreshed successfully
[[15:57:14]] [SUCCESS] Screenshot refreshed successfully
[[15:57:13]] [SUCCESS] Screenshot refreshed
[[15:57:13]] [INFO] Refreshing screenshot...
[[15:57:13]] [INFO] qkZ5KShdEU=pass
[[15:57:08]] [INFO] qkZ5KShdEU=running
[[15:57:08]] [INFO] Executing action 552/591: iOS Function: text - Text: "env[pwd]"
[[15:56:43]] [SUCCESS] Screenshot refreshed successfully
[[15:56:43]] [SUCCESS] Screenshot refreshed successfully
[[15:56:42]] [SUCCESS] Screenshot refreshed
[[15:56:42]] [INFO] Refreshing screenshot...
[[15:56:42]] [INFO] 7g2LmvjtEZ=pass
[[15:56:38]] [INFO] 7g2LmvjtEZ=running
[[15:56:38]] [INFO] Executing action 551/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:56:37]] [SUCCESS] Screenshot refreshed successfully
[[15:56:37]] [SUCCESS] Screenshot refreshed successfully
[[15:56:36]] [SUCCESS] Screenshot refreshed
[[15:56:36]] [INFO] Refreshing screenshot...
[[15:56:36]] [INFO] OUT2ASweb6=pass
[[15:56:31]] [INFO] OUT2ASweb6=running
[[15:56:31]] [INFO] Executing action 550/591: iOS Function: text - Text: "env[uname]"
[[15:56:21]] [SUCCESS] Screenshot refreshed successfully
[[15:56:21]] [SUCCESS] Screenshot refreshed successfully
[[15:56:20]] [SUCCESS] Screenshot refreshed
[[15:56:20]] [INFO] Refreshing screenshot...
[[15:56:20]] [INFO] TV4kJIIV9v=pass
[[15:56:16]] [INFO] TV4kJIIV9v=running
[[15:56:16]] [INFO] Executing action 549/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:56:06]] [SUCCESS] Screenshot refreshed successfully
[[15:56:06]] [SUCCESS] Screenshot refreshed successfully
[[15:56:05]] [SUCCESS] Screenshot refreshed
[[15:56:05]] [INFO] Refreshing screenshot...
[[15:56:05]] [INFO] kQJbqm7uCi=pass
[[15:56:02]] [SUCCESS] Screenshot refreshed successfully
[[15:56:02]] [SUCCESS] Screenshot refreshed successfully
[[15:56:02]] [INFO] kQJbqm7uCi=running
[[15:56:02]] [INFO] Executing action 548/591: iOS Function: alert_accept
[[15:56:01]] [SUCCESS] Screenshot refreshed
[[15:56:01]] [INFO] Refreshing screenshot...
[[15:56:01]] [INFO] SPE01N6pyp=pass
[[15:55:54]] [INFO] SPE01N6pyp=running
[[15:55:54]] [INFO] Executing action 547/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:55:49]] [SUCCESS] Screenshot refreshed successfully
[[15:55:49]] [SUCCESS] Screenshot refreshed successfully
[[15:55:47]] [SUCCESS] Screenshot refreshed
[[15:55:47]] [INFO] Refreshing screenshot...
[[15:55:47]] [INFO] WEB5St2Mb7=pass
[[15:55:43]] [INFO] WEB5St2Mb7=running
[[15:55:43]] [INFO] Executing action 546/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[15:55:43]] [SUCCESS] Screenshot refreshed successfully
[[15:55:43]] [SUCCESS] Screenshot refreshed successfully
[[15:55:41]] [SUCCESS] Screenshot refreshed
[[15:55:41]] [INFO] Refreshing screenshot...
[[15:55:41]] [INFO] To7bij5MnF=pass
[[15:55:35]] [INFO] To7bij5MnF=running
[[15:55:35]] [INFO] Executing action 545/591: Swipe from (5%, 50%) to (90%, 50%)
[[15:55:25]] [SUCCESS] Screenshot refreshed successfully
[[15:55:25]] [SUCCESS] Screenshot refreshed successfully
[[15:55:25]] [SUCCESS] Screenshot refreshed
[[15:55:25]] [INFO] Refreshing screenshot...
[[15:55:25]] [INFO] NkybTKfs2U=pass
[[15:55:19]] [INFO] NkybTKfs2U=running
[[15:55:19]] [INFO] Executing action 544/591: Swipe from (5%, 50%) to (90%, 50%)
[[15:55:09]] [SUCCESS] Screenshot refreshed successfully
[[15:55:09]] [SUCCESS] Screenshot refreshed successfully
[[15:55:08]] [SUCCESS] Screenshot refreshed
[[15:55:08]] [INFO] Refreshing screenshot...
[[15:55:08]] [INFO] dYEtjrv6lz=pass
[[15:55:03]] [SUCCESS] Screenshot refreshed successfully
[[15:55:03]] [SUCCESS] Screenshot refreshed successfully
[[15:55:03]] [INFO] dYEtjrv6lz=running
[[15:55:03]] [INFO] Executing action 543/591: Tap on Text: "Months"
[[15:55:02]] [SUCCESS] Screenshot refreshed
[[15:55:02]] [INFO] Refreshing screenshot...
[[15:55:02]] [INFO] eGQ7VrKUSo=pass
[[15:54:57]] [INFO] eGQ7VrKUSo=running
[[15:54:57]] [INFO] Executing action 542/591: Tap on Text: "Age"
[[15:54:55]] [SUCCESS] Screenshot refreshed successfully
[[15:54:55]] [SUCCESS] Screenshot refreshed successfully
[[15:54:54]] [SUCCESS] Screenshot refreshed
[[15:54:54]] [INFO] Refreshing screenshot...
[[15:54:54]] [INFO] zNRPvs2cC4=pass
[[15:54:50]] [INFO] zNRPvs2cC4=running
[[15:54:50]] [INFO] Executing action 541/591: Tap on Text: "Toys"
[[15:54:47]] [SUCCESS] Screenshot refreshed successfully
[[15:54:47]] [SUCCESS] Screenshot refreshed successfully
[[15:54:46]] [SUCCESS] Screenshot refreshed
[[15:54:46]] [INFO] Refreshing screenshot...
[[15:54:46]] [INFO] KyyS139agr=pass
[[15:54:42]] [SUCCESS] Screenshot refreshed successfully
[[15:54:42]] [SUCCESS] Screenshot refreshed successfully
[[15:54:42]] [INFO] KyyS139agr=running
[[15:54:42]] [INFO] Executing action 540/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[15:54:41]] [SUCCESS] Screenshot refreshed
[[15:54:41]] [INFO] Refreshing screenshot...
[[15:54:41]] [INFO] 5e4LeoW1YU=pass
[[15:54:34]] [SUCCESS] Screenshot refreshed successfully
[[15:54:34]] [SUCCESS] Screenshot refreshed successfully
[[15:54:33]] [INFO] 5e4LeoW1YU=running
[[15:54:33]] [INFO] Executing action 539/591: Restart app: env[appid]
[[15:54:33]] [SUCCESS] Screenshot refreshed
[[15:54:33]] [INFO] Refreshing screenshot...
[[15:54:23]] [SUCCESS] Screenshot refreshed successfully
[[15:54:23]] [SUCCESS] Screenshot refreshed successfully
[[15:54:22]] [SUCCESS] Screenshot refreshed
[[15:54:22]] [INFO] Refreshing screenshot...
[[15:54:05]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[15:54:01]] [SUCCESS] Screenshot refreshed successfully
[[15:54:01]] [SUCCESS] Screenshot refreshed successfully
[[15:54:00]] [SUCCESS] Screenshot refreshed
[[15:54:00]] [INFO] Refreshing screenshot...
[[15:53:14]] [SUCCESS] Screenshot refreshed successfully
[[15:53:14]] [SUCCESS] Screenshot refreshed successfully
[[15:53:13]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[15:53:13]] [SUCCESS] Screenshot refreshed
[[15:53:13]] [INFO] Refreshing screenshot...
[[15:52:53]] [SUCCESS] Screenshot refreshed successfully
[[15:52:53]] [SUCCESS] Screenshot refreshed successfully
[[15:52:53]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[15:52:52]] [SUCCESS] Screenshot refreshed
[[15:52:52]] [INFO] Refreshing screenshot...
[[15:52:05]] [SUCCESS] Screenshot refreshed successfully
[[15:52:05]] [SUCCESS] Screenshot refreshed successfully
[[15:52:05]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[15:52:04]] [SUCCESS] Screenshot refreshed
[[15:52:04]] [INFO] Refreshing screenshot...
[[15:51:46]] [SUCCESS] Screenshot refreshed successfully
[[15:51:46]] [SUCCESS] Screenshot refreshed successfully
[[15:51:45]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[15:51:45]] [SUCCESS] Screenshot refreshed
[[15:51:45]] [INFO] Refreshing screenshot...
[[15:50:56]] [SUCCESS] Screenshot refreshed successfully
[[15:50:56]] [SUCCESS] Screenshot refreshed successfully
[[15:50:56]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[15:50:55]] [SUCCESS] Screenshot refreshed
[[15:50:55]] [INFO] Refreshing screenshot...
[[15:50:37]] [SUCCESS] Screenshot refreshed successfully
[[15:50:37]] [SUCCESS] Screenshot refreshed successfully
[[15:50:37]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[15:50:37]] [SUCCESS] Screenshot refreshed
[[15:50:37]] [INFO] Refreshing screenshot...
[[15:49:48]] [SUCCESS] Screenshot refreshed successfully
[[15:49:48]] [SUCCESS] Screenshot refreshed successfully
[[15:49:48]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[15:49:47]] [SUCCESS] Screenshot refreshed
[[15:49:47]] [INFO] Refreshing screenshot...
[[15:49:29]] [SUCCESS] Screenshot refreshed successfully
[[15:49:29]] [SUCCESS] Screenshot refreshed successfully
[[15:49:28]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[15:49:27]] [SUCCESS] Screenshot refreshed
[[15:49:27]] [INFO] Refreshing screenshot...
[[15:48:38]] [SUCCESS] Screenshot refreshed successfully
[[15:48:38]] [SUCCESS] Screenshot refreshed successfully
[[15:48:38]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[15:48:38]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[15:48:38]] [INFO] Loading steps for multiStep action: Click_Paginations
[[15:48:38]] [INFO] aqs7O0Yq2p=running
[[15:48:38]] [INFO] Executing action 538/591: Execute Test Case: Click_Paginations (10 steps)
[[15:48:37]] [SUCCESS] Screenshot refreshed
[[15:48:37]] [INFO] Refreshing screenshot...
[[15:48:37]] [INFO] IL6kON0uQ9=pass
[[15:48:32]] [SUCCESS] Screenshot refreshed successfully
[[15:48:32]] [SUCCESS] Screenshot refreshed successfully
[[15:48:31]] [INFO] IL6kON0uQ9=running
[[15:48:31]] [INFO] Executing action 537/591: iOS Function: text - Text: "kids toys"
[[15:48:31]] [SUCCESS] Screenshot refreshed
[[15:48:31]] [INFO] Refreshing screenshot...
[[15:48:31]] [INFO] 6G6P3UE7Uy=pass
[[15:48:25]] [SUCCESS] Screenshot refreshed successfully
[[15:48:25]] [SUCCESS] Screenshot refreshed successfully
[[15:48:25]] [INFO] 6G6P3UE7Uy=running
[[15:48:25]] [INFO] Executing action 536/591: Tap on Text: "Find"
[[15:48:24]] [SUCCESS] Screenshot refreshed
[[15:48:24]] [INFO] Refreshing screenshot...
[[15:48:24]] [INFO] 7xs3GiydGF=pass
[[15:48:20]] [SUCCESS] Screenshot refreshed successfully
[[15:48:20]] [SUCCESS] Screenshot refreshed successfully
[[15:48:20]] [INFO] 7xs3GiydGF=running
[[15:48:20]] [INFO] Executing action 535/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[15:48:19]] [SUCCESS] Screenshot refreshed
[[15:48:19]] [INFO] Refreshing screenshot...
[[15:48:19]] [INFO] VqSa9z9R2Q=pass
[[15:48:17]] [INFO] VqSa9z9R2Q=running
[[15:48:17]] [INFO] Executing action 534/591: Launch app: env[appid]
[[15:48:17]] [SUCCESS] Screenshot refreshed successfully
[[15:48:17]] [SUCCESS] Screenshot refreshed successfully
[[15:48:17]] [SUCCESS] Screenshot refreshed
[[15:48:17]] [INFO] Refreshing screenshot...
[[15:48:17]] [INFO] RHEU77LRMw=pass
[[15:48:13]] [SUCCESS] Screenshot refreshed successfully
[[15:48:13]] [SUCCESS] Screenshot refreshed successfully
[[15:48:13]] [INFO] RHEU77LRMw=running
[[15:48:13]] [INFO] Executing action 533/591: Tap on Text: "+61"
[[15:48:12]] [SUCCESS] Screenshot refreshed
[[15:48:12]] [INFO] Refreshing screenshot...
[[15:48:12]] [INFO] MTRbUlaRvI=pass
[[15:48:07]] [SUCCESS] Screenshot refreshed successfully
[[15:48:07]] [SUCCESS] Screenshot refreshed successfully
[[15:48:07]] [INFO] MTRbUlaRvI=running
[[15:48:07]] [INFO] Executing action 532/591: Tap on Text: "1800"
[[15:48:07]] [SUCCESS] Screenshot refreshed
[[15:48:07]] [INFO] Refreshing screenshot...
[[15:48:07]] [INFO] I0tM87Yjhc=pass
[[15:48:01]] [SUCCESS] Screenshot refreshed successfully
[[15:48:01]] [SUCCESS] Screenshot refreshed successfully
[[15:48:01]] [INFO] I0tM87Yjhc=running
[[15:48:01]] [INFO] Executing action 531/591: Tap on Text: "click"
[[15:48:00]] [SUCCESS] Screenshot refreshed
[[15:48:00]] [INFO] Refreshing screenshot...
[[15:48:00]] [INFO] t6L5vWfBYM=pass
[[15:47:28]] [SUCCESS] Screenshot refreshed successfully
[[15:47:28]] [SUCCESS] Screenshot refreshed successfully
[[15:47:28]] [INFO] t6L5vWfBYM=running
[[15:47:28]] [INFO] Executing action 530/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:47:28]] [SUCCESS] Screenshot refreshed
[[15:47:28]] [INFO] Refreshing screenshot...
[[15:47:28]] [INFO] DhFJzlme9K=pass
[[15:47:23]] [SUCCESS] Screenshot refreshed successfully
[[15:47:23]] [SUCCESS] Screenshot refreshed successfully
[[15:47:23]] [INFO] DhFJzlme9K=running
[[15:47:23]] [INFO] Executing action 529/591: Tap on Text: "FAQ"
[[15:47:23]] [SUCCESS] Screenshot refreshed
[[15:47:23]] [INFO] Refreshing screenshot...
[[15:47:23]] [INFO] g17Boaefhg=pass
[[15:47:18]] [SUCCESS] Screenshot refreshed successfully
[[15:47:18]] [SUCCESS] Screenshot refreshed successfully
[[15:47:18]] [INFO] g17Boaefhg=running
[[15:47:18]] [INFO] Executing action 528/591: Tap on Text: "Help"
[[15:47:17]] [SUCCESS] Screenshot refreshed
[[15:47:17]] [INFO] Refreshing screenshot...
[[15:47:17]] [INFO] SqDiBhmyOG=pass
[[15:47:13]] [SUCCESS] Screenshot refreshed successfully
[[15:47:13]] [SUCCESS] Screenshot refreshed successfully
[[15:47:13]] [INFO] SqDiBhmyOG=running
[[15:47:13]] [INFO] Executing action 527/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:47:12]] [SUCCESS] Screenshot refreshed
[[15:47:12]] [INFO] Refreshing screenshot...
[[15:47:12]] [INFO] OR0SKKnFxy=pass
[[15:46:59]] [SUCCESS] Screenshot refreshed successfully
[[15:46:59]] [SUCCESS] Screenshot refreshed successfully
[[15:46:58]] [INFO] OR0SKKnFxy=running
[[15:46:58]] [INFO] Executing action 526/591: Restart app: env[appid]
[[15:46:58]] [SUCCESS] Screenshot refreshed
[[15:46:58]] [INFO] Refreshing screenshot...
[[15:46:57]] [SUCCESS] Screenshot refreshed
[[15:46:57]] [INFO] Refreshing screenshot...
[[15:46:54]] [SUCCESS] Screenshot refreshed successfully
[[15:46:54]] [SUCCESS] Screenshot refreshed successfully
[[15:46:54]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:46:54]] [SUCCESS] Screenshot refreshed
[[15:46:54]] [INFO] Refreshing screenshot...
[[15:46:42]] [SUCCESS] Screenshot refreshed successfully
[[15:46:42]] [SUCCESS] Screenshot refreshed successfully
[[15:46:42]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:46:42]] [SUCCESS] Screenshot refreshed
[[15:46:42]] [INFO] Refreshing screenshot...
[[15:46:38]] [SUCCESS] Screenshot refreshed successfully
[[15:46:38]] [SUCCESS] Screenshot refreshed successfully
[[15:46:38]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:46:37]] [SUCCESS] Screenshot refreshed
[[15:46:37]] [INFO] Refreshing screenshot...
[[15:46:32]] [SUCCESS] Screenshot refreshed successfully
[[15:46:32]] [SUCCESS] Screenshot refreshed successfully
[[15:46:32]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:46:31]] [SUCCESS] Screenshot refreshed
[[15:46:31]] [INFO] Refreshing screenshot...
[[15:46:25]] [SUCCESS] Screenshot refreshed successfully
[[15:46:25]] [SUCCESS] Screenshot refreshed successfully
[[15:46:24]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:46:24]] [SUCCESS] Screenshot refreshed
[[15:46:24]] [INFO] Refreshing screenshot...
[[15:46:16]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:46:16]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:46:16]] [SUCCESS] Screenshot refreshed successfully
[[15:46:16]] [SUCCESS] Screenshot refreshed successfully
[[15:46:16]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:46:16]] [INFO] kPdSiomhwu=running
[[15:46:16]] [INFO] Executing action 525/591: cleanupSteps action
[[15:46:15]] [SUCCESS] Screenshot refreshed
[[15:46:15]] [INFO] Refreshing screenshot...
[[15:46:15]] [INFO] Qb1AArnpCH=pass
[[15:46:09]] [SUCCESS] Screenshot refreshed successfully
[[15:46:09]] [SUCCESS] Screenshot refreshed successfully
[[15:46:09]] [INFO] Qb1AArnpCH=running
[[15:46:09]] [INFO] Executing action 524/591: Wait for 5 ms
[[15:46:08]] [SUCCESS] Screenshot refreshed
[[15:46:08]] [INFO] Refreshing screenshot...
[[15:46:08]] [INFO] 0SHxVJkq0l=pass
[[15:45:46]] [SUCCESS] Screenshot refreshed successfully
[[15:45:46]] [SUCCESS] Screenshot refreshed successfully
[[15:45:45]] [INFO] 0SHxVJkq0l=running
[[15:45:45]] [INFO] Executing action 523/591: If exists: id="//XCUIElementTypeButton[contains(@name,"Remove")]" (timeout: 20s) → Then tap at (0, 0)
[[15:45:45]] [SUCCESS] Screenshot refreshed
[[15:45:45]] [INFO] Refreshing screenshot...
[[15:45:45]] [INFO] K2w7X1cPdH=pass
[[15:45:31]] [INFO] K2w7X1cPdH=running
[[15:45:31]] [INFO] Executing action 522/591: Swipe from (50%, 50%) to (50%, 30%)
[[15:45:31]] [SUCCESS] Screenshot refreshed successfully
[[15:45:31]] [SUCCESS] Screenshot refreshed successfully
[[15:45:31]] [SUCCESS] Screenshot refreshed
[[15:45:31]] [INFO] Refreshing screenshot...
[[15:45:31]] [INFO] P26OyuqWlb=pass
[[15:45:26]] [SUCCESS] Screenshot refreshed successfully
[[15:45:26]] [SUCCESS] Screenshot refreshed successfully
[[15:45:26]] [INFO] P26OyuqWlb=running
[[15:45:26]] [INFO] Executing action 521/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:45:25]] [SUCCESS] Screenshot refreshed
[[15:45:25]] [INFO] Refreshing screenshot...
[[15:45:25]] [INFO] UpUSVInizv=pass
[[15:45:22]] [SUCCESS] Screenshot refreshed successfully
[[15:45:22]] [SUCCESS] Screenshot refreshed successfully
[[15:45:21]] [INFO] UpUSVInizv=running
[[15:45:21]] [INFO] Executing action 520/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[15:45:20]] [SUCCESS] Screenshot refreshed
[[15:45:20]] [INFO] Refreshing screenshot...
[[15:45:20]] [INFO] c4T3INQkzn=pass
[[15:45:15]] [SUCCESS] Screenshot refreshed successfully
[[15:45:15]] [SUCCESS] Screenshot refreshed successfully
[[15:45:15]] [INFO] c4T3INQkzn=running
[[15:45:15]] [INFO] Executing action 519/591: Restart app: env[appid]
[[15:45:14]] [SUCCESS] Screenshot refreshed
[[15:45:14]] [INFO] Refreshing screenshot...
[[15:45:14]] [INFO] Cr1z26u7Va=pass
[[15:45:08]] [SUCCESS] Screenshot refreshed successfully
[[15:45:08]] [SUCCESS] Screenshot refreshed successfully
[[15:45:08]] [INFO] Cr1z26u7Va=running
[[15:45:08]] [INFO] Executing action 518/591: If exists: accessibility_id="Add to bag" (timeout: 15s) → Then tap at (0, 0)
[[15:45:07]] [SUCCESS] Screenshot refreshed
[[15:45:07]] [INFO] Refreshing screenshot...
[[15:45:07]] [INFO] MA2re5cDWr=pass
[[15:44:58]] [SUCCESS] Screenshot refreshed successfully
[[15:44:58]] [SUCCESS] Screenshot refreshed successfully
[[15:44:58]] [INFO] MA2re5cDWr=running
[[15:44:58]] [INFO] Executing action 517/591: Swipe from (50%, 50%) to (50%, 30%)
[[15:44:58]] [SUCCESS] Screenshot refreshed
[[15:44:58]] [INFO] Refreshing screenshot...
[[15:44:58]] [INFO] 2hGhWulI52=pass
[[15:44:56]] [SUCCESS] Screenshot refreshed successfully
[[15:44:56]] [SUCCESS] Screenshot refreshed successfully
[[15:44:54]] [INFO] 2hGhWulI52=running
[[15:44:54]] [INFO] Executing action 516/591: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[15:44:53]] [SUCCESS] Screenshot refreshed
[[15:44:53]] [INFO] Refreshing screenshot...
[[15:44:53]] [INFO] n57KEWjTea=pass
[[15:44:49]] [SUCCESS] Screenshot refreshed successfully
[[15:44:49]] [SUCCESS] Screenshot refreshed successfully
[[15:44:48]] [INFO] n57KEWjTea=running
[[15:44:48]] [INFO] Executing action 515/591: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[15:44:47]] [SUCCESS] Screenshot refreshed
[[15:44:47]] [INFO] Refreshing screenshot...
[[15:44:47]] [INFO] L59V5hqMX9=pass
[[15:44:43]] [SUCCESS] Screenshot refreshed successfully
[[15:44:43]] [SUCCESS] Screenshot refreshed successfully
[[15:44:43]] [INFO] L59V5hqMX9=running
[[15:44:43]] [INFO] Executing action 514/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[15:44:42]] [SUCCESS] Screenshot refreshed
[[15:44:42]] [INFO] Refreshing screenshot...
[[15:44:42]] [INFO] OKiI82VdnE=pass
[[15:44:36]] [SUCCESS] Screenshot refreshed successfully
[[15:44:36]] [SUCCESS] Screenshot refreshed successfully
[[15:44:35]] [INFO] OKiI82VdnE=running
[[15:44:35]] [INFO] Executing action 513/591: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[15:44:34]] [SUCCESS] Screenshot refreshed
[[15:44:34]] [INFO] Refreshing screenshot...
[[15:44:34]] [INFO] 3KNqlNy6Bj=pass
[[15:44:29]] [INFO] 3KNqlNy6Bj=running
[[15:44:29]] [INFO] Executing action 512/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[15:44:29]] [SUCCESS] Screenshot refreshed successfully
[[15:44:29]] [SUCCESS] Screenshot refreshed successfully
[[15:44:29]] [SUCCESS] Screenshot refreshed
[[15:44:29]] [INFO] Refreshing screenshot...
[[15:44:29]] [INFO] 3NOS1fbxZs=pass
[[15:44:25]] [SUCCESS] Screenshot refreshed successfully
[[15:44:25]] [SUCCESS] Screenshot refreshed successfully
[[15:44:25]] [INFO] 3NOS1fbxZs=running
[[15:44:25]] [INFO] Executing action 511/591: Tap on image: banner-close-updated.png
[[15:44:24]] [SUCCESS] Screenshot refreshed
[[15:44:24]] [INFO] Refreshing screenshot...
[[15:44:24]] [INFO] K0c1gL9UK1=pass
[[15:44:17]] [SUCCESS] Screenshot refreshed successfully
[[15:44:17]] [SUCCESS] Screenshot refreshed successfully
[[15:44:17]] [INFO] K0c1gL9UK1=running
[[15:44:17]] [INFO] Executing action 510/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[15:44:16]] [SUCCESS] Screenshot refreshed
[[15:44:16]] [INFO] Refreshing screenshot...
[[15:44:16]] [INFO] IW6uAwdtiW=pass
[[15:44:10]] [SUCCESS] Screenshot refreshed successfully
[[15:44:10]] [SUCCESS] Screenshot refreshed successfully
[[15:44:10]] [INFO] IW6uAwdtiW=running
[[15:44:10]] [INFO] Executing action 509/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[15:44:10]] [SUCCESS] Screenshot refreshed
[[15:44:10]] [INFO] Refreshing screenshot...
[[15:44:10]] [INFO] DbM0d0m6rU=pass
[[15:43:56]] [INFO] DbM0d0m6rU=running
[[15:43:56]] [INFO] Executing action 508/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[15:43:56]] [SUCCESS] Screenshot refreshed successfully
[[15:43:56]] [SUCCESS] Screenshot refreshed successfully
[[15:43:55]] [SUCCESS] Screenshot refreshed
[[15:43:55]] [INFO] Refreshing screenshot...
[[15:43:55]] [INFO] saiPPHQSPa=pass
[[15:43:50]] [SUCCESS] Screenshot refreshed successfully
[[15:43:50]] [SUCCESS] Screenshot refreshed successfully
[[15:43:50]] [INFO] saiPPHQSPa=running
[[15:43:50]] [INFO] Executing action 507/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:43:50]] [SUCCESS] Screenshot refreshed
[[15:43:50]] [INFO] Refreshing screenshot...
[[15:43:50]] [INFO] UpUSVInizv=pass
[[15:43:45]] [SUCCESS] Screenshot refreshed successfully
[[15:43:45]] [SUCCESS] Screenshot refreshed successfully
[[15:43:45]] [INFO] UpUSVInizv=running
[[15:43:45]] [INFO] Executing action 506/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[15:43:44]] [SUCCESS] Screenshot refreshed
[[15:43:44]] [INFO] Refreshing screenshot...
[[15:43:44]] [INFO] Iab9zCfpqO=pass
[[15:43:24]] [SUCCESS] Screenshot refreshed successfully
[[15:43:24]] [SUCCESS] Screenshot refreshed successfully
[[15:43:24]] [INFO] Iab9zCfpqO=running
[[15:43:24]] [INFO] Executing action 505/591: Tap on element with accessibility_id: Add to bag
[[15:43:23]] [SUCCESS] Screenshot refreshed
[[15:43:23]] [INFO] Refreshing screenshot...
[[15:43:23]] [INFO] Qy0Y0uJchm=pass
[[15:43:20]] [SUCCESS] Screenshot refreshed successfully
[[15:43:20]] [SUCCESS] Screenshot refreshed successfully
[[15:43:19]] [INFO] Qy0Y0uJchm=running
[[15:43:19]] [INFO] Executing action 504/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[15:43:19]] [SUCCESS] Screenshot refreshed
[[15:43:19]] [INFO] Refreshing screenshot...
[[15:43:19]] [INFO] YHaMIjULRf=pass
[[15:43:13]] [SUCCESS] Screenshot refreshed successfully
[[15:43:13]] [SUCCESS] Screenshot refreshed successfully
[[15:43:12]] [INFO] YHaMIjULRf=running
[[15:43:12]] [INFO] Executing action 503/591: Tap on Text: "List"
[[15:43:12]] [SUCCESS] Screenshot refreshed
[[15:43:12]] [INFO] Refreshing screenshot...
[[15:43:12]] [INFO] igReeDqips=pass
[[15:43:07]] [SUCCESS] Screenshot refreshed successfully
[[15:43:07]] [SUCCESS] Screenshot refreshed successfully
[[15:43:06]] [INFO] igReeDqips=running
[[15:43:06]] [INFO] Executing action 502/591: Tap on image: env[catalogue-menu-img]
[[15:43:06]] [SUCCESS] Screenshot refreshed
[[15:43:06]] [INFO] Refreshing screenshot...
[[15:43:06]] [INFO] Xqj9EIVE7g=pass
[[15:42:41]] [SUCCESS] Screenshot refreshed successfully
[[15:42:41]] [SUCCESS] Screenshot refreshed successfully
[[15:42:40]] [INFO] Xqj9EIVE7g=running
[[15:42:40]] [INFO] Executing action 501/591: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[15:42:39]] [SUCCESS] Screenshot refreshed
[[15:42:39]] [INFO] Refreshing screenshot...
[[15:42:39]] [INFO] gkkQzTCmma=pass
[[15:42:34]] [SUCCESS] Screenshot refreshed successfully
[[15:42:34]] [SUCCESS] Screenshot refreshed successfully
[[15:42:33]] [INFO] gkkQzTCmma=running
[[15:42:33]] [INFO] Executing action 500/591: Tap on Text: "Catalogue"
[[15:42:33]] [SUCCESS] Screenshot refreshed
[[15:42:33]] [INFO] Refreshing screenshot...
[[15:42:33]] [INFO] UpUSVInizv=pass
[[15:42:29]] [SUCCESS] Screenshot refreshed successfully
[[15:42:29]] [SUCCESS] Screenshot refreshed successfully
[[15:42:28]] [INFO] UpUSVInizv=running
[[15:42:28]] [INFO] Executing action 499/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[15:42:28]] [SUCCESS] Screenshot refreshed
[[15:42:28]] [INFO] Refreshing screenshot...
[[15:42:28]] [INFO] Cmvm82hiAa=pass
[[15:42:22]] [SUCCESS] Screenshot refreshed successfully
[[15:42:22]] [SUCCESS] Screenshot refreshed successfully
[[15:42:21]] [INFO] Cmvm82hiAa=running
[[15:42:21]] [INFO] Executing action 498/591: Tap on element with accessibility_id: Add to bag
[[15:42:21]] [SUCCESS] Screenshot refreshed
[[15:42:21]] [INFO] Refreshing screenshot...
[[15:42:21]] [INFO] ZZPNqTJ65s=pass
[[15:42:15]] [SUCCESS] Screenshot refreshed successfully
[[15:42:15]] [SUCCESS] Screenshot refreshed successfully
[[15:42:15]] [INFO] ZZPNqTJ65s=running
[[15:42:15]] [INFO] Executing action 497/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:42:15]] [SUCCESS] Screenshot refreshed
[[15:42:15]] [INFO] Refreshing screenshot...
[[15:42:15]] [INFO] JcAR0JctQ6=pass
[[15:42:11]] [SUCCESS] Screenshot refreshed successfully
[[15:42:11]] [SUCCESS] Screenshot refreshed successfully
[[15:42:11]] [INFO] JcAR0JctQ6=running
[[15:42:11]] [INFO] Executing action 496/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[15:42:10]] [SUCCESS] Screenshot refreshed
[[15:42:10]] [INFO] Refreshing screenshot...
[[15:42:10]] [INFO] Pd7cReoJM6=pass
[[15:42:05]] [SUCCESS] Screenshot refreshed successfully
[[15:42:05]] [SUCCESS] Screenshot refreshed successfully
[[15:42:03]] [INFO] Pd7cReoJM6=running
[[15:42:03]] [INFO] Executing action 495/591: Tap on Text: "List"
[[15:42:02]] [SUCCESS] Screenshot refreshed
[[15:42:02]] [INFO] Refreshing screenshot...
[[15:42:02]] [INFO] igReeDqips=pass
[[15:41:57]] [SUCCESS] Screenshot refreshed successfully
[[15:41:57]] [SUCCESS] Screenshot refreshed successfully
[[15:41:56]] [INFO] igReeDqips=running
[[15:41:56]] [INFO] Executing action 494/591: Tap on image: env[catalogue-menu-img]
[[15:41:56]] [SUCCESS] Screenshot refreshed
[[15:41:56]] [INFO] Refreshing screenshot...
[[15:41:56]] [INFO] Xqj9EIVE7g=pass
[[15:41:36]] [SUCCESS] Screenshot refreshed successfully
[[15:41:36]] [SUCCESS] Screenshot refreshed successfully
[[15:41:32]] [INFO] Xqj9EIVE7g=running
[[15:41:32]] [INFO] Executing action 493/591: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[15:41:32]] [SUCCESS] Screenshot refreshed
[[15:41:32]] [INFO] Refreshing screenshot...
[[15:41:32]] [INFO] gkkQzTCmma=pass
[[15:41:26]] [SUCCESS] Screenshot refreshed successfully
[[15:41:26]] [SUCCESS] Screenshot refreshed successfully
[[15:41:26]] [INFO] gkkQzTCmma=running
[[15:41:26]] [INFO] Executing action 492/591: Tap on Text: "Catalogue"
[[15:41:25]] [SUCCESS] Screenshot refreshed
[[15:41:25]] [INFO] Refreshing screenshot...
[[15:41:25]] [INFO] QUeGIASAxV=pass
[[15:41:21]] [SUCCESS] Screenshot refreshed successfully
[[15:41:21]] [SUCCESS] Screenshot refreshed successfully
[[15:41:21]] [INFO] QUeGIASAxV=running
[[15:41:21]] [INFO] Executing action 491/591: Swipe from (50%, 50%) to (50%, 30%)
[[15:41:21]] [SUCCESS] Screenshot refreshed
[[15:41:21]] [INFO] Refreshing screenshot...
[[15:41:21]] [INFO] UpUSVInizv=pass
[[15:41:17]] [SUCCESS] Screenshot refreshed successfully
[[15:41:17]] [SUCCESS] Screenshot refreshed successfully
[[15:41:17]] [INFO] UpUSVInizv=running
[[15:41:17]] [INFO] Executing action 490/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[15:41:16]] [SUCCESS] Screenshot refreshed
[[15:41:16]] [INFO] Refreshing screenshot...
[[15:41:16]] [INFO] 0QtNHB5WEK=pass
[[15:41:13]] [SUCCESS] Screenshot refreshed successfully
[[15:41:13]] [SUCCESS] Screenshot refreshed successfully
[[15:41:12]] [INFO] 0QtNHB5WEK=running
[[15:41:12]] [INFO] Executing action 489/591: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[15:41:12]] [SUCCESS] Screenshot refreshed
[[15:41:12]] [INFO] Refreshing screenshot...
[[15:41:12]] [INFO] fTdGMJ3NH3=pass
[[15:41:09]] [SUCCESS] Screenshot refreshed successfully
[[15:41:09]] [SUCCESS] Screenshot refreshed successfully
[[15:41:08]] [INFO] fTdGMJ3NH3=running
[[15:41:08]] [INFO] Executing action 488/591: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[15:41:08]] [SUCCESS] Screenshot refreshed
[[15:41:08]] [INFO] Refreshing screenshot...
[[15:41:08]] [INFO] rYJcLPh8Aq=pass
[[15:41:04]] [INFO] rYJcLPh8Aq=running
[[15:41:04]] [INFO] Executing action 487/591: iOS Function: text - Text: "kmart au"
[[15:41:04]] [SUCCESS] Screenshot refreshed successfully
[[15:41:04]] [SUCCESS] Screenshot refreshed successfully
[[15:41:04]] [SUCCESS] Screenshot refreshed
[[15:41:04]] [INFO] Refreshing screenshot...
[[15:41:04]] [INFO] 0Q0fm6OTij=pass
[[15:41:01]] [SUCCESS] Screenshot refreshed successfully
[[15:41:01]] [SUCCESS] Screenshot refreshed successfully
[[15:41:01]] [INFO] 0Q0fm6OTij=running
[[15:41:01]] [INFO] Executing action 486/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[15:41:00]] [SUCCESS] Screenshot refreshed
[[15:41:00]] [INFO] Refreshing screenshot...
[[15:41:00]] [INFO] xVuuejtCFA=pass
[[15:40:57]] [SUCCESS] Screenshot refreshed successfully
[[15:40:57]] [SUCCESS] Screenshot refreshed successfully
[[15:40:57]] [INFO] xVuuejtCFA=running
[[15:40:57]] [INFO] Executing action 485/591: Restart app: com.apple.mobilesafari
[[15:40:56]] [SUCCESS] Screenshot refreshed
[[15:40:56]] [INFO] Refreshing screenshot...
[[15:40:56]] [INFO] LcYLwUffqj=pass
[[15:40:51]] [SUCCESS] Screenshot refreshed successfully
[[15:40:51]] [SUCCESS] Screenshot refreshed successfully
[[15:40:51]] [INFO] LcYLwUffqj=running
[[15:40:51]] [INFO] Executing action 484/591: Tap on Text: "out"
[[15:40:50]] [SUCCESS] Screenshot refreshed
[[15:40:50]] [INFO] Refreshing screenshot...
[[15:40:50]] [INFO] ZZPNqTJ65s=pass
[[15:40:46]] [SUCCESS] Screenshot refreshed successfully
[[15:40:46]] [SUCCESS] Screenshot refreshed successfully
[[15:40:46]] [INFO] ZZPNqTJ65s=running
[[15:40:46]] [INFO] Executing action 483/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:40:45]] [SUCCESS] Screenshot refreshed
[[15:40:45]] [INFO] Refreshing screenshot...
[[15:40:45]] [INFO] UpUSVInizv=pass
[[15:40:41]] [SUCCESS] Screenshot refreshed successfully
[[15:40:41]] [SUCCESS] Screenshot refreshed successfully
[[15:40:40]] [INFO] UpUSVInizv=running
[[15:40:40]] [INFO] Executing action 482/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[15:40:40]] [SUCCESS] Screenshot refreshed
[[15:40:40]] [INFO] Refreshing screenshot...
[[15:40:40]] [INFO] hCCEvRtj1A=pass
[[15:40:34]] [INFO] hCCEvRtj1A=running
[[15:40:34]] [INFO] Executing action 481/591: Restart app: env[appid]
[[15:40:34]] [SUCCESS] Screenshot refreshed successfully
[[15:40:34]] [SUCCESS] Screenshot refreshed successfully
[[15:40:34]] [SUCCESS] Screenshot refreshed
[[15:40:34]] [INFO] Refreshing screenshot...
[[15:40:34]] [INFO] V42eHtTRYW=pass
[[15:40:27]] [INFO] V42eHtTRYW=running
[[15:40:27]] [INFO] Executing action 480/591: Wait for 5 ms
[[15:40:27]] [SUCCESS] Screenshot refreshed successfully
[[15:40:27]] [SUCCESS] Screenshot refreshed successfully
[[15:40:26]] [SUCCESS] Screenshot refreshed
[[15:40:26]] [INFO] Refreshing screenshot...
[[15:40:26]] [INFO] GRwHMVK4sA=pass
[[15:40:24]] [INFO] GRwHMVK4sA=running
[[15:40:24]] [INFO] Executing action 479/591: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[15:40:24]] [SUCCESS] Screenshot refreshed successfully
[[15:40:24]] [SUCCESS] Screenshot refreshed successfully
[[15:40:24]] [SUCCESS] Screenshot refreshed
[[15:40:24]] [INFO] Refreshing screenshot...
[[15:40:24]] [INFO] V42eHtTRYW=pass
[[15:40:17]] [INFO] V42eHtTRYW=running
[[15:40:17]] [INFO] Executing action 478/591: Wait for 5 ms
[[15:40:17]] [SUCCESS] Screenshot refreshed successfully
[[15:40:17]] [SUCCESS] Screenshot refreshed successfully
[[15:40:16]] [SUCCESS] Screenshot refreshed
[[15:40:16]] [INFO] Refreshing screenshot...
[[15:40:16]] [INFO] LfyQctrEJn=pass
[[15:40:15]] [SUCCESS] Screenshot refreshed successfully
[[15:40:15]] [SUCCESS] Screenshot refreshed successfully
[[15:40:15]] [INFO] LfyQctrEJn=running
[[15:40:15]] [INFO] Executing action 477/591: Launch app: com.apple.Preferences
[[15:40:14]] [SUCCESS] Screenshot refreshed
[[15:40:14]] [INFO] Refreshing screenshot...
[[15:40:14]] [INFO] seQcUKjkSU=pass
[[15:40:12]] [SUCCESS] Screenshot refreshed successfully
[[15:40:12]] [SUCCESS] Screenshot refreshed successfully
[[15:40:12]] [INFO] seQcUKjkSU=running
[[15:40:12]] [INFO] Executing action 476/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:40:12]] [SUCCESS] Screenshot refreshed
[[15:40:12]] [INFO] Refreshing screenshot...
[[15:40:12]] [INFO] UpUSVInizv=pass
[[15:40:09]] [SUCCESS] Screenshot refreshed successfully
[[15:40:09]] [SUCCESS] Screenshot refreshed successfully
[[15:40:09]] [INFO] UpUSVInizv=running
[[15:40:09]] [INFO] Executing action 475/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[15:40:09]] [SUCCESS] Screenshot refreshed
[[15:40:09]] [INFO] Refreshing screenshot...
[[15:40:09]] [INFO] WoymrHdtrO=pass
[[15:40:07]] [SUCCESS] Screenshot refreshed successfully
[[15:40:07]] [SUCCESS] Screenshot refreshed successfully
[[15:40:07]] [INFO] WoymrHdtrO=running
[[15:40:07]] [INFO] Executing action 474/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:40:06]] [SUCCESS] Screenshot refreshed
[[15:40:06]] [INFO] Refreshing screenshot...
[[15:40:06]] [INFO] 6xgrAWyfZ4=pass
[[15:40:04]] [SUCCESS] Screenshot refreshed successfully
[[15:40:04]] [SUCCESS] Screenshot refreshed successfully
[[15:40:04]] [INFO] 6xgrAWyfZ4=running
[[15:40:04]] [INFO] Executing action 473/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[15:40:03]] [SUCCESS] Screenshot refreshed
[[15:40:03]] [INFO] Refreshing screenshot...
[[15:40:03]] [INFO] eSr9EFlJek=pass
[[15:40:02]] [SUCCESS] Screenshot refreshed successfully
[[15:40:02]] [SUCCESS] Screenshot refreshed successfully
[[15:40:01]] [INFO] eSr9EFlJek=running
[[15:40:01]] [INFO] Executing action 472/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:40:01]] [SUCCESS] Screenshot refreshed
[[15:40:01]] [INFO] Refreshing screenshot...
[[15:40:01]] [INFO] 3KNqlNy6Bj=pass
[[15:39:59]] [SUCCESS] Screenshot refreshed successfully
[[15:39:59]] [SUCCESS] Screenshot refreshed successfully
[[15:39:59]] [INFO] 3KNqlNy6Bj=running
[[15:39:59]] [INFO] Executing action 471/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[15:39:58]] [SUCCESS] Screenshot refreshed
[[15:39:58]] [INFO] Refreshing screenshot...
[[15:39:58]] [INFO] cokvFXhj4c=pass
[[15:39:56]] [SUCCESS] Screenshot refreshed successfully
[[15:39:56]] [SUCCESS] Screenshot refreshed successfully
[[15:39:56]] [INFO] cokvFXhj4c=running
[[15:39:56]] [INFO] Executing action 470/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:39:55]] [SUCCESS] Screenshot refreshed
[[15:39:55]] [INFO] Refreshing screenshot...
[[15:39:55]] [INFO] oSQ8sPdVOJ=pass
[[15:39:50]] [INFO] oSQ8sPdVOJ=running
[[15:39:50]] [INFO] Executing action 469/591: Restart app: env[appid]
[[15:39:50]] [SUCCESS] Screenshot refreshed successfully
[[15:39:50]] [SUCCESS] Screenshot refreshed successfully
[[15:39:50]] [SUCCESS] Screenshot refreshed
[[15:39:50]] [INFO] Refreshing screenshot...
[[15:39:50]] [INFO] V42eHtTRYW=pass
[[15:39:43]] [INFO] V42eHtTRYW=running
[[15:39:43]] [INFO] Executing action 468/591: Wait for 5 ms
[[15:39:43]] [SUCCESS] Screenshot refreshed successfully
[[15:39:43]] [SUCCESS] Screenshot refreshed successfully
[[15:39:43]] [SUCCESS] Screenshot refreshed
[[15:39:43]] [INFO] Refreshing screenshot...
[[15:39:43]] [INFO] jUCAk6GJc4=pass
[[15:39:40]] [INFO] jUCAk6GJc4=running
[[15:39:40]] [INFO] Executing action 467/591: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[15:39:40]] [SUCCESS] Screenshot refreshed successfully
[[15:39:40]] [SUCCESS] Screenshot refreshed successfully
[[15:39:40]] [SUCCESS] Screenshot refreshed
[[15:39:40]] [INFO] Refreshing screenshot...
[[15:39:40]] [INFO] V42eHtTRYW=pass
[[15:39:33]] [INFO] V42eHtTRYW=running
[[15:39:33]] [INFO] Executing action 466/591: Wait for 5 ms
[[15:39:33]] [SUCCESS] Screenshot refreshed successfully
[[15:39:33]] [SUCCESS] Screenshot refreshed successfully
[[15:39:33]] [SUCCESS] Screenshot refreshed
[[15:39:33]] [INFO] Refreshing screenshot...
[[15:39:33]] [INFO] w1RV76df9x=pass
[[15:39:28]] [INFO] w1RV76df9x=running
[[15:39:28]] [INFO] Executing action 465/591: Tap on Text: "Wi-Fi"
[[15:39:28]] [SUCCESS] Screenshot refreshed successfully
[[15:39:28]] [SUCCESS] Screenshot refreshed successfully
[[15:39:28]] [SUCCESS] Screenshot refreshed
[[15:39:28]] [INFO] Refreshing screenshot...
[[15:39:28]] [INFO] LfyQctrEJn=pass
[[15:39:26]] [SUCCESS] Screenshot refreshed successfully
[[15:39:26]] [SUCCESS] Screenshot refreshed successfully
[[15:39:25]] [INFO] LfyQctrEJn=running
[[15:39:25]] [INFO] Executing action 464/591: Launch app: com.apple.Preferences
[[15:39:25]] [SUCCESS] Screenshot refreshed
[[15:39:25]] [INFO] Refreshing screenshot...
[[15:39:25]] [INFO] mIKA85kXaW=pass
[[15:39:23]] [SUCCESS] Screenshot refreshed successfully
[[15:39:23]] [SUCCESS] Screenshot refreshed successfully
[[15:39:22]] [INFO] mIKA85kXaW=running
[[15:39:22]] [INFO] Executing action 463/591: Terminate app: com.apple.Preferences
[[15:39:22]] [SUCCESS] Screenshot refreshed
[[15:39:22]] [INFO] Refreshing screenshot...
[[15:39:22]] [SUCCESS] Screenshot refreshed
[[15:39:22]] [INFO] Refreshing screenshot...
[[15:39:17]] [SUCCESS] Screenshot refreshed successfully
[[15:39:17]] [SUCCESS] Screenshot refreshed successfully
[[15:39:17]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:39:16]] [SUCCESS] Screenshot refreshed
[[15:39:16]] [INFO] Refreshing screenshot...
[[15:39:12]] [SUCCESS] Screenshot refreshed successfully
[[15:39:12]] [SUCCESS] Screenshot refreshed successfully
[[15:39:12]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:39:11]] [SUCCESS] Screenshot refreshed
[[15:39:11]] [INFO] Refreshing screenshot...
[[15:39:06]] [SUCCESS] Screenshot refreshed successfully
[[15:39:06]] [SUCCESS] Screenshot refreshed successfully
[[15:39:06]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[15:39:06]] [SUCCESS] Screenshot refreshed
[[15:39:06]] [INFO] Refreshing screenshot...
[[15:39:00]] [SUCCESS] Screenshot refreshed successfully
[[15:39:00]] [SUCCESS] Screenshot refreshed successfully
[[15:39:00]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:38:59]] [SUCCESS] Screenshot refreshed
[[15:38:59]] [INFO] Refreshing screenshot...
[[15:38:54]] [SUCCESS] Screenshot refreshed successfully
[[15:38:54]] [SUCCESS] Screenshot refreshed successfully
[[15:38:54]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:38:53]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:38:53]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:38:53]] [INFO] x6vffndoRV=running
[[15:38:53]] [INFO] Executing action 462/591: Execute Test Case: Kmart-Signin (6 steps)
[[15:38:53]] [SUCCESS] Screenshot refreshed
[[15:38:53]] [INFO] Refreshing screenshot...
[[15:38:53]] [INFO] rJ86z4njuR=pass
[[15:38:50]] [SUCCESS] Screenshot refreshed successfully
[[15:38:50]] [SUCCESS] Screenshot refreshed successfully
[[15:38:50]] [INFO] rJ86z4njuR=running
[[15:38:50]] [INFO] Executing action 461/591: iOS Function: alert_accept
[[15:38:49]] [SUCCESS] Screenshot refreshed
[[15:38:49]] [INFO] Refreshing screenshot...
[[15:38:49]] [INFO] veukWo4573=pass
[[15:38:45]] [SUCCESS] Screenshot refreshed successfully
[[15:38:45]] [SUCCESS] Screenshot refreshed successfully
[[15:38:44]] [INFO] veukWo4573=running
[[15:38:44]] [INFO] Executing action 460/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[15:38:44]] [SUCCESS] Screenshot refreshed
[[15:38:44]] [INFO] Refreshing screenshot...
[[15:38:44]] [INFO] XEbZHdi0GT=pass
[[15:38:30]] [SUCCESS] Screenshot refreshed successfully
[[15:38:30]] [SUCCESS] Screenshot refreshed successfully
[[15:38:29]] [INFO] XEbZHdi0GT=running
[[15:38:29]] [INFO] Executing action 459/591: Restart app: env[appid]
[[15:38:29]] [SUCCESS] Screenshot refreshed
[[15:38:29]] [INFO] Refreshing screenshot...
[[15:38:29]] [SUCCESS] Screenshot refreshed
[[15:38:29]] [INFO] Refreshing screenshot...
[[15:38:26]] [SUCCESS] Screenshot refreshed successfully
[[15:38:26]] [SUCCESS] Screenshot refreshed successfully
[[15:38:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:38:25]] [SUCCESS] Screenshot refreshed
[[15:38:25]] [INFO] Refreshing screenshot...
[[15:38:12]] [SUCCESS] Screenshot refreshed successfully
[[15:38:12]] [SUCCESS] Screenshot refreshed successfully
[[15:38:12]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:38:12]] [SUCCESS] Screenshot refreshed
[[15:38:12]] [INFO] Refreshing screenshot...
[[15:38:08]] [SUCCESS] Screenshot refreshed successfully
[[15:38:08]] [SUCCESS] Screenshot refreshed successfully
[[15:38:08]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:38:07]] [SUCCESS] Screenshot refreshed
[[15:38:07]] [INFO] Refreshing screenshot...
[[15:38:02]] [SUCCESS] Screenshot refreshed successfully
[[15:38:02]] [SUCCESS] Screenshot refreshed successfully
[[15:38:01]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:38:01]] [SUCCESS] Screenshot refreshed
[[15:38:01]] [INFO] Refreshing screenshot...
[[15:37:54]] [SUCCESS] Screenshot refreshed successfully
[[15:37:54]] [SUCCESS] Screenshot refreshed successfully
[[15:37:54]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:37:53]] [SUCCESS] Screenshot refreshed
[[15:37:53]] [INFO] Refreshing screenshot...
[[15:37:46]] [SUCCESS] Screenshot refreshed successfully
[[15:37:46]] [SUCCESS] Screenshot refreshed successfully
[[15:37:46]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:37:46]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:37:46]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:37:46]] [INFO] ubySifeF65=running
[[15:37:46]] [INFO] Executing action 458/591: cleanupSteps action
[[15:37:45]] [SUCCESS] Screenshot refreshed
[[15:37:45]] [INFO] Refreshing screenshot...
[[15:37:45]] [INFO] xyHVihJMBi=pass
[[15:37:41]] [SUCCESS] Screenshot refreshed successfully
[[15:37:41]] [SUCCESS] Screenshot refreshed successfully
[[15:37:41]] [INFO] xyHVihJMBi=running
[[15:37:41]] [INFO] Executing action 457/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:37:40]] [SUCCESS] Screenshot refreshed
[[15:37:40]] [INFO] Refreshing screenshot...
[[15:37:40]] [INFO] mWeLQtXiL6=pass
[[15:37:31]] [SUCCESS] Screenshot refreshed successfully
[[15:37:31]] [SUCCESS] Screenshot refreshed successfully
[[15:37:31]] [INFO] mWeLQtXiL6=running
[[15:37:31]] [INFO] Executing action 456/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:37:31]] [SUCCESS] Screenshot refreshed
[[15:37:31]] [INFO] Refreshing screenshot...
[[15:37:31]] [INFO] F4NGh9HrLw=pass
[[15:37:26]] [SUCCESS] Screenshot refreshed successfully
[[15:37:26]] [SUCCESS] Screenshot refreshed successfully
[[15:37:26]] [INFO] F4NGh9HrLw=running
[[15:37:26]] [INFO] Executing action 455/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:37:25]] [SUCCESS] Screenshot refreshed
[[15:37:25]] [INFO] Refreshing screenshot...
[[15:37:25]] [INFO] 0f2FSZYjWq=pass
[[15:37:07]] [SUCCESS] Screenshot refreshed successfully
[[15:37:07]] [SUCCESS] Screenshot refreshed successfully
[[15:37:07]] [INFO] 0f2FSZYjWq=running
[[15:37:07]] [INFO] Executing action 454/591: Check if element with text="Melbourne" exists
[[15:37:06]] [SUCCESS] Screenshot refreshed
[[15:37:06]] [INFO] Refreshing screenshot...
[[15:37:06]] [INFO] Tebej51pT2=pass
[[15:37:00]] [INFO] Tebej51pT2=running
[[15:37:00]] [INFO] Executing action 453/591: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[15:37:00]] [SUCCESS] Screenshot refreshed successfully
[[15:37:00]] [SUCCESS] Screenshot refreshed successfully
[[15:37:00]] [SUCCESS] Screenshot refreshed
[[15:37:00]] [INFO] Refreshing screenshot...
[[15:37:00]] [INFO] I4gwigwXSj=pass
[[15:36:56]] [INFO] I4gwigwXSj=running
[[15:36:56]] [INFO] Executing action 452/591: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[15:36:56]] [SUCCESS] Screenshot refreshed successfully
[[15:36:56]] [SUCCESS] Screenshot refreshed successfully
[[15:36:56]] [SUCCESS] Screenshot refreshed
[[15:36:56]] [INFO] Refreshing screenshot...
[[15:36:56]] [INFO] eVytJrry9x=pass
[[15:36:52]] [SUCCESS] Screenshot refreshed successfully
[[15:36:52]] [SUCCESS] Screenshot refreshed successfully
[[15:36:51]] [INFO] eVytJrry9x=running
[[15:36:51]] [INFO] Executing action 451/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[15:36:51]] [SUCCESS] Screenshot refreshed
[[15:36:51]] [INFO] Refreshing screenshot...
[[15:36:51]] [INFO] s8h8VDUIOC=pass
[[15:36:46]] [SUCCESS] Screenshot refreshed successfully
[[15:36:46]] [SUCCESS] Screenshot refreshed successfully
[[15:36:46]] [INFO] s8h8VDUIOC=running
[[15:36:46]] [INFO] Executing action 450/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:36:46]] [SUCCESS] Screenshot refreshed
[[15:36:46]] [INFO] Refreshing screenshot...
[[15:36:46]] [INFO] bkU728TrRF=pass
[[15:36:39]] [SUCCESS] Screenshot refreshed successfully
[[15:36:39]] [SUCCESS] Screenshot refreshed successfully
[[15:36:39]] [INFO] bkU728TrRF=running
[[15:36:39]] [INFO] Executing action 449/591: Tap on element with accessibility_id: Done
[[15:36:38]] [SUCCESS] Screenshot refreshed
[[15:36:38]] [INFO] Refreshing screenshot...
[[15:36:38]] [INFO] ZWpYNcpbFA=pass
[[15:36:34]] [SUCCESS] Screenshot refreshed successfully
[[15:36:34]] [SUCCESS] Screenshot refreshed successfully
[[15:36:33]] [INFO] ZWpYNcpbFA=running
[[15:36:33]] [INFO] Executing action 448/591: Tap on Text: "VIC"
[[15:36:33]] [SUCCESS] Screenshot refreshed
[[15:36:33]] [INFO] Refreshing screenshot...
[[15:36:33]] [INFO] Wld5Urg70o=pass
[[15:36:26]] [INFO] Wld5Urg70o=running
[[15:36:26]] [INFO] Executing action 447/591: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[15:36:26]] [SUCCESS] Screenshot refreshed successfully
[[15:36:26]] [SUCCESS] Screenshot refreshed successfully
[[15:36:25]] [SUCCESS] Screenshot refreshed
[[15:36:25]] [INFO] Refreshing screenshot...
[[15:36:25]] [INFO] QpBLC6BStn=pass
[[15:36:18]] [SUCCESS] Screenshot refreshed successfully
[[15:36:18]] [SUCCESS] Screenshot refreshed successfully
[[15:36:18]] [INFO] QpBLC6BStn=running
[[15:36:18]] [INFO] Executing action 446/591: Tap on element with accessibility_id: delete
[[15:36:18]] [SUCCESS] Screenshot refreshed
[[15:36:18]] [INFO] Refreshing screenshot...
[[15:36:18]] [INFO] G4A3KBlXHq=pass
[[15:36:13]] [SUCCESS] Screenshot refreshed successfully
[[15:36:13]] [SUCCESS] Screenshot refreshed successfully
[[15:36:13]] [INFO] G4A3KBlXHq=running
[[15:36:13]] [INFO] Executing action 445/591: Tap on Text: "Nearby"
[[15:36:12]] [SUCCESS] Screenshot refreshed
[[15:36:12]] [INFO] Refreshing screenshot...
[[15:36:12]] [INFO] uArzgeZYf7=pass
[[15:36:08]] [SUCCESS] Screenshot refreshed successfully
[[15:36:08]] [SUCCESS] Screenshot refreshed successfully
[[15:36:08]] [INFO] uArzgeZYf7=running
[[15:36:08]] [INFO] Executing action 444/591: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[15:36:08]] [SUCCESS] Screenshot refreshed
[[15:36:08]] [INFO] Refreshing screenshot...
[[15:36:08]] [INFO] 3gJsiap2Ds=pass
[[15:36:03]] [SUCCESS] Screenshot refreshed successfully
[[15:36:03]] [SUCCESS] Screenshot refreshed successfully
[[15:36:02]] [INFO] 3gJsiap2Ds=running
[[15:36:02]] [INFO] Executing action 443/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[15:36:01]] [SUCCESS] Screenshot refreshed
[[15:36:01]] [INFO] Refreshing screenshot...
[[15:36:01]] [INFO] dF3hpprg71=pass
[[15:35:57]] [INFO] dF3hpprg71=running
[[15:35:57]] [INFO] Executing action 442/591: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[15:35:57]] [SUCCESS] Screenshot refreshed successfully
[[15:35:57]] [SUCCESS] Screenshot refreshed successfully
[[15:35:57]] [SUCCESS] Screenshot refreshed
[[15:35:57]] [INFO] Refreshing screenshot...
[[15:35:57]] [INFO] EReijW5iNX=pass
[[15:35:52]] [SUCCESS] Screenshot refreshed successfully
[[15:35:52]] [SUCCESS] Screenshot refreshed successfully
[[15:35:52]] [INFO] EReijW5iNX=running
[[15:35:52]] [INFO] Executing action 441/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:35:51]] [SUCCESS] Screenshot refreshed
[[15:35:51]] [INFO] Refreshing screenshot...
[[15:35:51]] [INFO] 94ikwhIEE2=pass
[[15:35:46]] [SUCCESS] Screenshot refreshed successfully
[[15:35:46]] [SUCCESS] Screenshot refreshed successfully
[[15:35:46]] [INFO] 94ikwhIEE2=running
[[15:35:46]] [INFO] Executing action 440/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:35:46]] [SUCCESS] Screenshot refreshed
[[15:35:46]] [INFO] Refreshing screenshot...
[[15:35:46]] [INFO] q8oldD8uZt=pass
[[15:35:42]] [SUCCESS] Screenshot refreshed successfully
[[15:35:42]] [SUCCESS] Screenshot refreshed successfully
[[15:35:42]] [INFO] q8oldD8uZt=running
[[15:35:42]] [INFO] Executing action 439/591: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[15:35:41]] [SUCCESS] Screenshot refreshed
[[15:35:41]] [INFO] Refreshing screenshot...
[[15:35:41]] [INFO] Jf2wJyOphY=pass
[[15:35:21]] [SUCCESS] Screenshot refreshed successfully
[[15:35:21]] [SUCCESS] Screenshot refreshed successfully
[[15:35:21]] [INFO] Jf2wJyOphY=running
[[15:35:21]] [INFO] Executing action 438/591: Tap on element with accessibility_id: Add to bag
[[15:35:20]] [SUCCESS] Screenshot refreshed
[[15:35:20]] [INFO] Refreshing screenshot...
[[15:35:20]] [INFO] eRCmRhc3re=pass
[[15:35:05]] [SUCCESS] Screenshot refreshed successfully
[[15:35:05]] [SUCCESS] Screenshot refreshed successfully
[[15:35:04]] [INFO] eRCmRhc3re=running
[[15:35:04]] [INFO] Executing action 437/591: Check if element with text="Broadway" exists
[[15:35:04]] [SUCCESS] Screenshot refreshed
[[15:35:04]] [INFO] Refreshing screenshot...
[[15:35:04]] [INFO] ORI6ZFMBK1=pass
[[15:34:58]] [SUCCESS] Screenshot refreshed successfully
[[15:34:58]] [SUCCESS] Screenshot refreshed successfully
[[15:34:58]] [INFO] ORI6ZFMBK1=running
[[15:34:58]] [INFO] Executing action 436/591: Tap on Text: "Save"
[[15:34:58]] [SUCCESS] Screenshot refreshed
[[15:34:58]] [INFO] Refreshing screenshot...
[[15:34:58]] [INFO] hr0IVckpYI=pass
[[15:34:52]] [SUCCESS] Screenshot refreshed successfully
[[15:34:52]] [SUCCESS] Screenshot refreshed successfully
[[15:34:52]] [INFO] hr0IVckpYI=running
[[15:34:52]] [INFO] Executing action 435/591: Wait till accessibility_id=btnSaveOrContinue
[[15:34:52]] [SUCCESS] Screenshot refreshed
[[15:34:52]] [INFO] Refreshing screenshot...
[[15:34:52]] [INFO] H0ODFz7sWJ=pass
[[15:34:47]] [SUCCESS] Screenshot refreshed successfully
[[15:34:47]] [SUCCESS] Screenshot refreshed successfully
[[15:34:47]] [INFO] H0ODFz7sWJ=running
[[15:34:47]] [INFO] Executing action 434/591: Tap on Text: "2000"
[[15:34:46]] [SUCCESS] Screenshot refreshed
[[15:34:46]] [INFO] Refreshing screenshot...
[[15:34:46]] [INFO] uZHvvAzVfx=pass
[[15:34:41]] [SUCCESS] Screenshot refreshed successfully
[[15:34:41]] [SUCCESS] Screenshot refreshed successfully
[[15:34:41]] [INFO] uZHvvAzVfx=running
[[15:34:41]] [INFO] Executing action 433/591: textClear action
[[15:34:41]] [SUCCESS] Screenshot refreshed
[[15:34:41]] [INFO] Refreshing screenshot...
[[15:34:41]] [INFO] WmNWcsWVHv=pass
[[15:34:35]] [SUCCESS] Screenshot refreshed successfully
[[15:34:35]] [SUCCESS] Screenshot refreshed successfully
[[15:34:35]] [INFO] WmNWcsWVHv=running
[[15:34:35]] [INFO] Executing action 432/591: Tap on element with accessibility_id: Search suburb or postcode
[[15:34:34]] [SUCCESS] Screenshot refreshed
[[15:34:34]] [INFO] Refreshing screenshot...
[[15:34:34]] [INFO] lnjoz8hHUU=pass
[[15:34:28]] [SUCCESS] Screenshot refreshed successfully
[[15:34:28]] [SUCCESS] Screenshot refreshed successfully
[[15:34:28]] [INFO] lnjoz8hHUU=running
[[15:34:28]] [INFO] Executing action 431/591: Tap on Text: "Edit"
[[15:34:28]] [SUCCESS] Screenshot refreshed
[[15:34:28]] [INFO] Refreshing screenshot...
[[15:34:28]] [INFO] letbbewlnA=pass
[[15:34:23]] [SUCCESS] Screenshot refreshed successfully
[[15:34:23]] [SUCCESS] Screenshot refreshed successfully
[[15:34:23]] [INFO] letbbewlnA=running
[[15:34:23]] [INFO] Executing action 430/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:34:22]] [SUCCESS] Screenshot refreshed
[[15:34:22]] [INFO] Refreshing screenshot...
[[15:34:22]] [INFO] trBISwJ8eZ=pass
[[15:34:18]] [SUCCESS] Screenshot refreshed successfully
[[15:34:18]] [SUCCESS] Screenshot refreshed successfully
[[15:34:18]] [INFO] trBISwJ8eZ=running
[[15:34:18]] [INFO] Executing action 429/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:34:17]] [SUCCESS] Screenshot refreshed
[[15:34:17]] [INFO] Refreshing screenshot...
[[15:34:17]] [INFO] foVGMl9wvu=pass
[[15:34:13]] [SUCCESS] Screenshot refreshed successfully
[[15:34:13]] [SUCCESS] Screenshot refreshed successfully
[[15:34:13]] [INFO] foVGMl9wvu=running
[[15:34:13]] [INFO] Executing action 428/591: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:34:12]] [SUCCESS] Screenshot refreshed
[[15:34:12]] [INFO] Refreshing screenshot...
[[15:34:12]] [INFO] 73NABkfWyY=pass
[[15:33:56]] [SUCCESS] Screenshot refreshed successfully
[[15:33:56]] [SUCCESS] Screenshot refreshed successfully
[[15:33:55]] [INFO] 73NABkfWyY=running
[[15:33:55]] [INFO] Executing action 427/591: Check if element with text="Tarneit" exists
[[15:33:54]] [SUCCESS] Screenshot refreshed
[[15:33:54]] [INFO] Refreshing screenshot...
[[15:33:54]] [INFO] pKjXoj4mNg=pass
[[15:33:49]] [SUCCESS] Screenshot refreshed successfully
[[15:33:49]] [SUCCESS] Screenshot refreshed successfully
[[15:33:49]] [INFO] pKjXoj4mNg=running
[[15:33:49]] [INFO] Executing action 426/591: Tap on Text: "Save"
[[15:33:49]] [SUCCESS] Screenshot refreshed
[[15:33:49]] [INFO] Refreshing screenshot...
[[15:33:49]] [INFO] M3dXqigqRv=pass
[[15:33:44]] [SUCCESS] Screenshot refreshed successfully
[[15:33:44]] [SUCCESS] Screenshot refreshed successfully
[[15:33:43]] [INFO] M3dXqigqRv=running
[[15:33:43]] [INFO] Executing action 425/591: Wait till accessibility_id=btnSaveOrContinue
[[15:33:43]] [SUCCESS] Screenshot refreshed
[[15:33:43]] [INFO] Refreshing screenshot...
[[15:33:43]] [INFO] GYRHQr7TWx=pass
[[15:33:38]] [SUCCESS] Screenshot refreshed successfully
[[15:33:38]] [SUCCESS] Screenshot refreshed successfully
[[15:33:38]] [INFO] GYRHQr7TWx=running
[[15:33:38]] [INFO] Executing action 424/591: Tap on Text: "current"
[[15:33:37]] [SUCCESS] Screenshot refreshed
[[15:33:37]] [INFO] Refreshing screenshot...
[[15:33:37]] [INFO] kiM0WyWE9I=pass
[[15:33:31]] [SUCCESS] Screenshot refreshed successfully
[[15:33:31]] [SUCCESS] Screenshot refreshed successfully
[[15:33:31]] [INFO] kiM0WyWE9I=running
[[15:33:31]] [INFO] Executing action 423/591: Wait till accessibility_id=btnCurrentLocationButton
[[15:33:30]] [SUCCESS] Screenshot refreshed
[[15:33:30]] [INFO] Refreshing screenshot...
[[15:33:30]] [INFO] VkUKQbf1Qt=pass
[[15:33:25]] [SUCCESS] Screenshot refreshed successfully
[[15:33:25]] [SUCCESS] Screenshot refreshed successfully
[[15:33:25]] [INFO] VkUKQbf1Qt=running
[[15:33:25]] [INFO] Executing action 422/591: Tap on Text: "Edit"
[[15:33:24]] [SUCCESS] Screenshot refreshed
[[15:33:24]] [INFO] Refreshing screenshot...
[[15:33:24]] [INFO] C6JHhLdWTv=pass
[[15:33:21]] [SUCCESS] Screenshot refreshed successfully
[[15:33:21]] [SUCCESS] Screenshot refreshed successfully
[[15:33:20]] [INFO] C6JHhLdWTv=running
[[15:33:20]] [INFO] Executing action 421/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:33:20]] [SUCCESS] Screenshot refreshed
[[15:33:20]] [INFO] Refreshing screenshot...
[[15:33:20]] [INFO] IupxLP2Jsr=pass
[[15:33:15]] [SUCCESS] Screenshot refreshed successfully
[[15:33:15]] [SUCCESS] Screenshot refreshed successfully
[[15:33:15]] [INFO] IupxLP2Jsr=running
[[15:33:15]] [INFO] Executing action 420/591: iOS Function: text - Text: "Uno card"
[[15:33:15]] [SUCCESS] Screenshot refreshed
[[15:33:15]] [INFO] Refreshing screenshot...
[[15:33:15]] [INFO] 70iOOakiG7=pass
[[15:33:09]] [SUCCESS] Screenshot refreshed successfully
[[15:33:09]] [SUCCESS] Screenshot refreshed successfully
[[15:33:09]] [INFO] 70iOOakiG7=running
[[15:33:09]] [INFO] Executing action 419/591: Tap on Text: "Find"
[[15:33:08]] [SUCCESS] Screenshot refreshed
[[15:33:08]] [INFO] Refreshing screenshot...
[[15:33:08]] [INFO] Xqj9EIVEfg=pass
[[15:32:59]] [SUCCESS] Screenshot refreshed successfully
[[15:32:59]] [SUCCESS] Screenshot refreshed successfully
[[15:32:58]] [INFO] Xqj9EIVEfg=running
[[15:32:58]] [INFO] Executing action 418/591: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[15:32:58]] [SUCCESS] Screenshot refreshed
[[15:32:58]] [INFO] Refreshing screenshot...
[[15:32:58]] [INFO] E2jpN7BioW=pass
[[15:32:53]] [SUCCESS] Screenshot refreshed successfully
[[15:32:53]] [SUCCESS] Screenshot refreshed successfully
[[15:32:53]] [INFO] E2jpN7BioW=running
[[15:32:53]] [INFO] Executing action 417/591: Tap on Text: "Save"
[[15:32:52]] [SUCCESS] Screenshot refreshed
[[15:32:52]] [INFO] Refreshing screenshot...
[[15:32:52]] [INFO] Sl6eiqZkRm=pass
[[15:32:47]] [SUCCESS] Screenshot refreshed successfully
[[15:32:47]] [SUCCESS] Screenshot refreshed successfully
[[15:32:47]] [INFO] Sl6eiqZkRm=running
[[15:32:47]] [INFO] Executing action 416/591: Wait till accessibility_id=btnSaveOrContinue
[[15:32:47]] [SUCCESS] Screenshot refreshed
[[15:32:47]] [INFO] Refreshing screenshot...
[[15:32:47]] [INFO] mw9GQ4mzRE=pass
[[15:32:42]] [SUCCESS] Screenshot refreshed successfully
[[15:32:42]] [SUCCESS] Screenshot refreshed successfully
[[15:32:42]] [INFO] mw9GQ4mzRE=running
[[15:32:42]] [INFO] Executing action 415/591: Tap on Text: "2000"
[[15:32:41]] [SUCCESS] Screenshot refreshed
[[15:32:41]] [INFO] Refreshing screenshot...
[[15:32:41]] [INFO] kbdEPCPYod=pass
[[15:32:36]] [SUCCESS] Screenshot refreshed successfully
[[15:32:36]] [SUCCESS] Screenshot refreshed successfully
[[15:32:36]] [INFO] kbdEPCPYod=running
[[15:32:36]] [INFO] Executing action 414/591: textClear action
[[15:32:36]] [SUCCESS] Screenshot refreshed
[[15:32:36]] [INFO] Refreshing screenshot...
[[15:32:36]] [INFO] 8WCusTZ8q9=pass
[[15:32:29]] [SUCCESS] Screenshot refreshed successfully
[[15:32:29]] [SUCCESS] Screenshot refreshed successfully
[[15:32:29]] [INFO] 8WCusTZ8q9=running
[[15:32:29]] [INFO] Executing action 413/591: Tap on element with accessibility_id: Search suburb or postcode
[[15:32:28]] [SUCCESS] Screenshot refreshed
[[15:32:28]] [INFO] Refreshing screenshot...
[[15:32:28]] [INFO] QMXBlswP6H=pass
[[15:32:25]] [SUCCESS] Screenshot refreshed successfully
[[15:32:25]] [SUCCESS] Screenshot refreshed successfully
[[15:32:24]] [INFO] QMXBlswP6H=running
[[15:32:24]] [INFO] Executing action 412/591: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[15:32:24]] [SUCCESS] Screenshot refreshed
[[15:32:24]] [INFO] Refreshing screenshot...
[[15:32:24]] [INFO] m0956RsrdM=pass
[[15:32:22]] [SUCCESS] Screenshot refreshed successfully
[[15:32:22]] [SUCCESS] Screenshot refreshed successfully
[[15:32:19]] [INFO] m0956RsrdM=running
[[15:32:19]] [INFO] Executing action 411/591: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[15:32:19]] [SUCCESS] Screenshot refreshed
[[15:32:19]] [INFO] Refreshing screenshot...
[[15:32:19]] [SUCCESS] Screenshot refreshed
[[15:32:19]] [INFO] Refreshing screenshot...
[[15:32:14]] [SUCCESS] Screenshot refreshed successfully
[[15:32:14]] [SUCCESS] Screenshot refreshed successfully
[[15:32:14]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:32:13]] [SUCCESS] Screenshot refreshed
[[15:32:13]] [INFO] Refreshing screenshot...
[[15:32:09]] [SUCCESS] Screenshot refreshed successfully
[[15:32:09]] [SUCCESS] Screenshot refreshed successfully
[[15:32:09]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:32:08]] [SUCCESS] Screenshot refreshed
[[15:32:08]] [INFO] Refreshing screenshot...
[[15:32:03]] [SUCCESS] Screenshot refreshed successfully
[[15:32:03]] [SUCCESS] Screenshot refreshed successfully
[[15:32:02]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[15:32:02]] [SUCCESS] Screenshot refreshed
[[15:32:02]] [INFO] Refreshing screenshot...
[[15:31:58]] [SUCCESS] Screenshot refreshed successfully
[[15:31:58]] [SUCCESS] Screenshot refreshed successfully
[[15:31:58]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:31:57]] [SUCCESS] Screenshot refreshed
[[15:31:57]] [INFO] Refreshing screenshot...
[[15:31:51]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:31:51]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:31:51]] [SUCCESS] Screenshot refreshed successfully
[[15:31:51]] [SUCCESS] Screenshot refreshed successfully
[[15:31:51]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:31:51]] [INFO] C3UHsKxa5P=running
[[15:31:51]] [INFO] Executing action 410/591: Execute Test Case: Kmart-Signin (6 steps)
[[15:31:50]] [SUCCESS] Screenshot refreshed
[[15:31:50]] [INFO] Refreshing screenshot...
[[15:31:50]] [INFO] Azb1flbIJJ=pass
[[15:31:47]] [SUCCESS] Screenshot refreshed successfully
[[15:31:47]] [SUCCESS] Screenshot refreshed successfully
[[15:31:47]] [INFO] Azb1flbIJJ=running
[[15:31:47]] [INFO] Executing action 409/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:31:46]] [SUCCESS] Screenshot refreshed
[[15:31:46]] [INFO] Refreshing screenshot...
[[15:31:46]] [INFO] 2xC5fLfLe8=pass
[[15:31:44]] [SUCCESS] Screenshot refreshed successfully
[[15:31:44]] [SUCCESS] Screenshot refreshed successfully
[[15:31:43]] [INFO] 2xC5fLfLe8=running
[[15:31:43]] [INFO] Executing action 408/591: iOS Function: alert_accept
[[15:31:43]] [SUCCESS] Screenshot refreshed
[[15:31:43]] [INFO] Refreshing screenshot...
[[15:31:43]] [INFO] Y8vz7AJD1i=pass
[[15:31:36]] [SUCCESS] Screenshot refreshed successfully
[[15:31:36]] [SUCCESS] Screenshot refreshed successfully
[[15:31:35]] [INFO] Y8vz7AJD1i=running
[[15:31:35]] [INFO] Executing action 407/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:31:35]] [SUCCESS] Screenshot refreshed
[[15:31:35]] [INFO] Refreshing screenshot...
[[15:31:35]] [INFO] H9fy9qcFbZ=pass
[[15:31:22]] [SUCCESS] Screenshot refreshed successfully
[[15:31:22]] [SUCCESS] Screenshot refreshed successfully
[[15:31:20]] [INFO] H9fy9qcFbZ=running
[[15:31:20]] [INFO] Executing action 406/591: Restart app: env[appid]
[[15:31:20]] [SUCCESS] Screenshot refreshed
[[15:31:20]] [INFO] Refreshing screenshot...
[[15:31:20]] [SUCCESS] Screenshot refreshed
[[15:31:20]] [INFO] Refreshing screenshot...
[[15:31:17]] [SUCCESS] Screenshot refreshed successfully
[[15:31:17]] [SUCCESS] Screenshot refreshed successfully
[[15:31:17]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:31:16]] [SUCCESS] Screenshot refreshed
[[15:31:16]] [INFO] Refreshing screenshot...
[[15:31:05]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:31:05]] [SUCCESS] Screenshot refreshed successfully
[[15:31:05]] [SUCCESS] Screenshot refreshed successfully
[[15:31:04]] [SUCCESS] Screenshot refreshed
[[15:31:04]] [INFO] Refreshing screenshot...
[[15:31:00]] [SUCCESS] Screenshot refreshed successfully
[[15:31:00]] [SUCCESS] Screenshot refreshed successfully
[[15:31:00]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:30:59]] [SUCCESS] Screenshot refreshed
[[15:30:59]] [INFO] Refreshing screenshot...
[[15:30:56]] [SUCCESS] Screenshot refreshed successfully
[[15:30:56]] [SUCCESS] Screenshot refreshed successfully
[[15:30:55]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:30:55]] [SUCCESS] Screenshot refreshed
[[15:30:55]] [INFO] Refreshing screenshot...
[[15:30:48]] [SUCCESS] Screenshot refreshed successfully
[[15:30:48]] [SUCCESS] Screenshot refreshed successfully
[[15:30:48]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:30:47]] [SUCCESS] Screenshot refreshed
[[15:30:47]] [INFO] Refreshing screenshot...
[[15:30:41]] [SUCCESS] Screenshot refreshed successfully
[[15:30:41]] [SUCCESS] Screenshot refreshed successfully
[[15:30:41]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:30:41]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:30:41]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:30:41]] [INFO] OMgc2gHHyq=running
[[15:30:41]] [INFO] Executing action 405/591: cleanupSteps action
[[15:30:40]] [SUCCESS] Screenshot refreshed
[[15:30:40]] [INFO] Refreshing screenshot...
[[15:30:40]] [INFO] x4yLCZHaCR=pass
[[15:30:37]] [SUCCESS] Screenshot refreshed successfully
[[15:30:37]] [SUCCESS] Screenshot refreshed successfully
[[15:30:37]] [INFO] x4yLCZHaCR=running
[[15:30:37]] [INFO] Executing action 404/591: Terminate app: env[appid]
[[15:30:36]] [SUCCESS] Screenshot refreshed
[[15:30:36]] [INFO] Refreshing screenshot...
[[15:30:36]] [INFO] 2p13JoJbbA=pass
[[15:30:30]] [SUCCESS] Screenshot refreshed successfully
[[15:30:30]] [SUCCESS] Screenshot refreshed successfully
[[15:30:30]] [INFO] 2p13JoJbbA=running
[[15:30:30]] [INFO] Executing action 403/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:30:30]] [SUCCESS] Screenshot refreshed
[[15:30:30]] [INFO] Refreshing screenshot...
[[15:30:30]] [INFO] qHdMgerbTE=pass
[[15:30:25]] [SUCCESS] Screenshot refreshed successfully
[[15:30:25]] [SUCCESS] Screenshot refreshed successfully
[[15:30:25]] [INFO] qHdMgerbTE=running
[[15:30:25]] [INFO] Executing action 402/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:30:25]] [SUCCESS] Screenshot refreshed
[[15:30:25]] [INFO] Refreshing screenshot...
[[15:30:25]] [INFO] F4NGh9HrLw=pass
[[15:30:23]] [SUCCESS] Screenshot refreshed successfully
[[15:30:23]] [SUCCESS] Screenshot refreshed successfully
[[15:30:20]] [INFO] F4NGh9HrLw=running
[[15:30:20]] [INFO] Executing action 401/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:30:20]] [SUCCESS] Screenshot refreshed
[[15:30:20]] [INFO] Refreshing screenshot...
[[15:30:19]] [SUCCESS] Screenshot refreshed
[[15:30:19]] [INFO] Refreshing screenshot...
[[15:30:14]] [INFO] Executing Multi Step action step 42/42: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[15:30:14]] [SUCCESS] Screenshot refreshed successfully
[[15:30:14]] [SUCCESS] Screenshot refreshed successfully
[[15:30:14]] [SUCCESS] Screenshot refreshed
[[15:30:14]] [INFO] Refreshing screenshot...
[[15:30:10]] [SUCCESS] Screenshot refreshed successfully
[[15:30:10]] [SUCCESS] Screenshot refreshed successfully
[[15:30:09]] [INFO] Executing Multi Step action step 41/42: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[15:30:09]] [SUCCESS] Screenshot refreshed
[[15:30:09]] [INFO] Refreshing screenshot...
[[15:30:01]] [SUCCESS] Screenshot refreshed successfully
[[15:30:01]] [SUCCESS] Screenshot refreshed successfully
[[15:30:01]] [INFO] Executing Multi Step action step 40/42: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[15:30:01]] [SUCCESS] Screenshot refreshed
[[15:30:01]] [INFO] Refreshing screenshot...
[[15:29:57]] [INFO] Executing Multi Step action step 39/42: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[15:29:57]] [SUCCESS] Screenshot refreshed successfully
[[15:29:57]] [SUCCESS] Screenshot refreshed successfully
[[15:29:56]] [SUCCESS] Screenshot refreshed
[[15:29:56]] [INFO] Refreshing screenshot...
[[15:29:51]] [SUCCESS] Screenshot refreshed successfully
[[15:29:51]] [SUCCESS] Screenshot refreshed successfully
[[15:29:51]] [INFO] Executing Multi Step action step 38/42: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:29:51]] [SUCCESS] Screenshot refreshed
[[15:29:51]] [INFO] Refreshing screenshot...
[[15:29:47]] [SUCCESS] Screenshot refreshed successfully
[[15:29:47]] [SUCCESS] Screenshot refreshed successfully
[[15:29:47]] [INFO] Executing Multi Step action step 37/42: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:29:46]] [SUCCESS] Screenshot refreshed
[[15:29:46]] [INFO] Refreshing screenshot...
[[15:29:43]] [SUCCESS] Screenshot refreshed successfully
[[15:29:43]] [SUCCESS] Screenshot refreshed successfully
[[15:29:43]] [INFO] Executing Multi Step action step 36/42: Tap on image: banner-close-updated.png
[[15:29:42]] [SUCCESS] Screenshot refreshed
[[15:29:42]] [INFO] Refreshing screenshot...
[[15:29:39]] [INFO] Executing Multi Step action step 35/42: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[15:29:39]] [SUCCESS] Screenshot refreshed successfully
[[15:29:39]] [SUCCESS] Screenshot refreshed successfully
[[15:29:38]] [SUCCESS] Screenshot refreshed
[[15:29:38]] [INFO] Refreshing screenshot...
[[15:29:35]] [SUCCESS] Screenshot refreshed successfully
[[15:29:35]] [SUCCESS] Screenshot refreshed successfully
[[15:29:34]] [INFO] Executing Multi Step action step 34/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[15:29:34]] [SUCCESS] Screenshot refreshed
[[15:29:34]] [INFO] Refreshing screenshot...
[[15:29:29]] [SUCCESS] Screenshot refreshed successfully
[[15:29:29]] [SUCCESS] Screenshot refreshed successfully
[[15:29:29]] [INFO] Executing Multi Step action step 33/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[15:29:29]] [SUCCESS] Screenshot refreshed
[[15:29:29]] [INFO] Refreshing screenshot...
[[15:29:25]] [SUCCESS] Screenshot refreshed successfully
[[15:29:25]] [SUCCESS] Screenshot refreshed successfully
[[15:29:25]] [INFO] Executing Multi Step action step 32/42: Tap on image: banner-close-updated.png
[[15:29:24]] [SUCCESS] Screenshot refreshed
[[15:29:24]] [INFO] Refreshing screenshot...
[[15:29:21]] [INFO] Executing Multi Step action step 31/42: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[15:29:21]] [SUCCESS] Screenshot refreshed successfully
[[15:29:21]] [SUCCESS] Screenshot refreshed successfully
[[15:29:21]] [SUCCESS] Screenshot refreshed
[[15:29:21]] [INFO] Refreshing screenshot...
[[15:29:17]] [SUCCESS] Screenshot refreshed successfully
[[15:29:17]] [SUCCESS] Screenshot refreshed successfully
[[15:29:17]] [INFO] Executing Multi Step action step 30/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[15:29:16]] [SUCCESS] Screenshot refreshed
[[15:29:16]] [INFO] Refreshing screenshot...
[[15:29:12]] [SUCCESS] Screenshot refreshed successfully
[[15:29:12]] [SUCCESS] Screenshot refreshed successfully
[[15:29:12]] [INFO] Executing Multi Step action step 29/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[15:29:12]] [SUCCESS] Screenshot refreshed
[[15:29:12]] [INFO] Refreshing screenshot...
[[15:29:07]] [SUCCESS] Screenshot refreshed successfully
[[15:29:07]] [SUCCESS] Screenshot refreshed successfully
[[15:29:07]] [INFO] Executing Multi Step action step 28/42: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[15:29:07]] [SUCCESS] Screenshot refreshed
[[15:29:07]] [INFO] Refreshing screenshot...
[[15:29:04]] [SUCCESS] Screenshot refreshed successfully
[[15:29:04]] [SUCCESS] Screenshot refreshed successfully
[[15:29:02]] [INFO] Executing Multi Step action step 27/42: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[15:29:02]] [SUCCESS] Screenshot refreshed
[[15:29:02]] [INFO] Refreshing screenshot...
[[15:28:58]] [SUCCESS] Screenshot refreshed successfully
[[15:28:58]] [SUCCESS] Screenshot refreshed successfully
[[15:28:57]] [INFO] Executing Multi Step action step 26/42: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[15:28:57]] [SUCCESS] Screenshot refreshed
[[15:28:57]] [INFO] Refreshing screenshot...
[[15:28:53]] [SUCCESS] Screenshot refreshed successfully
[[15:28:53]] [SUCCESS] Screenshot refreshed successfully
[[15:28:53]] [INFO] Executing Multi Step action step 25/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[15:28:52]] [SUCCESS] Screenshot refreshed
[[15:28:52]] [INFO] Refreshing screenshot...
[[15:28:48]] [SUCCESS] Screenshot refreshed successfully
[[15:28:48]] [SUCCESS] Screenshot refreshed successfully
[[15:28:48]] [INFO] Executing Multi Step action step 24/42: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[15:28:48]] [SUCCESS] Screenshot refreshed
[[15:28:48]] [INFO] Refreshing screenshot...
[[15:28:44]] [SUCCESS] Screenshot refreshed successfully
[[15:28:44]] [SUCCESS] Screenshot refreshed successfully
[[15:28:44]] [INFO] Executing Multi Step action step 23/42: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[15:28:44]] [SUCCESS] Screenshot refreshed
[[15:28:44]] [INFO] Refreshing screenshot...
[[15:28:40]] [SUCCESS] Screenshot refreshed successfully
[[15:28:40]] [SUCCESS] Screenshot refreshed successfully
[[15:28:39]] [INFO] Executing Multi Step action step 22/42: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[15:28:39]] [SUCCESS] Screenshot refreshed
[[15:28:39]] [INFO] Refreshing screenshot...
[[15:28:31]] [SUCCESS] Screenshot refreshed successfully
[[15:28:31]] [SUCCESS] Screenshot refreshed successfully
[[15:28:30]] [INFO] Executing Multi Step action step 21/42: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[15:28:30]] [SUCCESS] Screenshot refreshed
[[15:28:30]] [INFO] Refreshing screenshot...
[[15:28:26]] [SUCCESS] Screenshot refreshed successfully
[[15:28:26]] [SUCCESS] Screenshot refreshed successfully
[[15:28:26]] [INFO] Executing Multi Step action step 20/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[15:28:25]] [SUCCESS] Screenshot refreshed
[[15:28:25]] [INFO] Refreshing screenshot...
[[15:28:21]] [SUCCESS] Screenshot refreshed successfully
[[15:28:21]] [SUCCESS] Screenshot refreshed successfully
[[15:28:21]] [INFO] Executing Multi Step action step 19/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[15:28:21]] [SUCCESS] Screenshot refreshed
[[15:28:21]] [INFO] Refreshing screenshot...
[[15:28:13]] [SUCCESS] Screenshot refreshed successfully
[[15:28:13]] [SUCCESS] Screenshot refreshed successfully
[[15:28:12]] [INFO] Executing Multi Step action step 18/42: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[15:28:12]] [SUCCESS] Screenshot refreshed
[[15:28:12]] [INFO] Refreshing screenshot...
[[15:28:08]] [SUCCESS] Screenshot refreshed successfully
[[15:28:08]] [SUCCESS] Screenshot refreshed successfully
[[15:28:08]] [INFO] Executing Multi Step action step 17/42: Tap on image: env[delivery-address-img]
[[15:28:08]] [SUCCESS] Screenshot refreshed
[[15:28:08]] [INFO] Refreshing screenshot...
[[15:28:05]] [SUCCESS] Screenshot refreshed successfully
[[15:28:05]] [SUCCESS] Screenshot refreshed successfully
[[15:28:03]] [INFO] Executing Multi Step action step 16/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[15:28:03]] [SUCCESS] Screenshot refreshed
[[15:28:03]] [INFO] Refreshing screenshot...
[[15:27:56]] [SUCCESS] Screenshot refreshed successfully
[[15:27:56]] [SUCCESS] Screenshot refreshed successfully
[[15:27:56]] [INFO] Executing Multi Step action step 15/42: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[15:27:55]] [SUCCESS] Screenshot refreshed
[[15:27:55]] [INFO] Refreshing screenshot...
[[15:27:50]] [SUCCESS] Screenshot refreshed successfully
[[15:27:50]] [SUCCESS] Screenshot refreshed successfully
[[15:27:50]] [INFO] Executing Multi Step action step 14/42: Tap on Text: "address"
[[15:27:49]] [SUCCESS] Screenshot refreshed
[[15:27:49]] [INFO] Refreshing screenshot...
[[15:27:45]] [SUCCESS] Screenshot refreshed successfully
[[15:27:45]] [SUCCESS] Screenshot refreshed successfully
[[15:27:45]] [INFO] Executing Multi Step action step 13/42: iOS Function: text - Text: " "
[[15:27:44]] [SUCCESS] Screenshot refreshed
[[15:27:44]] [INFO] Refreshing screenshot...
[[15:27:38]] [SUCCESS] Screenshot refreshed successfully
[[15:27:38]] [SUCCESS] Screenshot refreshed successfully
[[15:27:38]] [INFO] Executing Multi Step action step 12/42: textClear action
[[15:27:37]] [SUCCESS] Screenshot refreshed
[[15:27:37]] [INFO] Refreshing screenshot...
[[15:27:31]] [SUCCESS] Screenshot refreshed successfully
[[15:27:31]] [SUCCESS] Screenshot refreshed successfully
[[15:27:31]] [INFO] Executing Multi Step action step 11/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[15:27:31]] [SUCCESS] Screenshot refreshed
[[15:27:31]] [INFO] Refreshing screenshot...
[[15:27:24]] [SUCCESS] Screenshot refreshed successfully
[[15:27:24]] [SUCCESS] Screenshot refreshed successfully
[[15:27:24]] [INFO] Executing Multi Step action step 10/42: textClear action
[[15:27:23]] [SUCCESS] Screenshot refreshed
[[15:27:23]] [INFO] Refreshing screenshot...
[[15:27:19]] [SUCCESS] Screenshot refreshed successfully
[[15:27:19]] [SUCCESS] Screenshot refreshed successfully
[[15:27:19]] [INFO] Executing Multi Step action step 9/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:27:19]] [SUCCESS] Screenshot refreshed
[[15:27:19]] [INFO] Refreshing screenshot...
[[15:27:12]] [SUCCESS] Screenshot refreshed successfully
[[15:27:12]] [SUCCESS] Screenshot refreshed successfully
[[15:27:12]] [INFO] Executing Multi Step action step 8/42: textClear action
[[15:27:11]] [SUCCESS] Screenshot refreshed
[[15:27:11]] [INFO] Refreshing screenshot...
[[15:27:07]] [SUCCESS] Screenshot refreshed successfully
[[15:27:07]] [SUCCESS] Screenshot refreshed successfully
[[15:27:07]] [INFO] Executing Multi Step action step 7/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[15:27:07]] [SUCCESS] Screenshot refreshed
[[15:27:07]] [INFO] Refreshing screenshot...
[[15:26:59]] [SUCCESS] Screenshot refreshed successfully
[[15:26:59]] [SUCCESS] Screenshot refreshed successfully
[[15:26:59]] [INFO] Executing Multi Step action step 6/42: textClear action
[[15:26:58]] [SUCCESS] Screenshot refreshed
[[15:26:58]] [INFO] Refreshing screenshot...
[[15:26:54]] [SUCCESS] Screenshot refreshed successfully
[[15:26:54]] [SUCCESS] Screenshot refreshed successfully
[[15:26:54]] [INFO] Executing Multi Step action step 5/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[15:26:54]] [SUCCESS] Screenshot refreshed
[[15:26:54]] [INFO] Refreshing screenshot...
[[15:26:50]] [SUCCESS] Screenshot refreshed successfully
[[15:26:50]] [SUCCESS] Screenshot refreshed successfully
[[15:26:50]] [INFO] Executing Multi Step action step 4/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[15:26:49]] [SUCCESS] Screenshot refreshed
[[15:26:49]] [INFO] Refreshing screenshot...
[[15:26:38]] [SUCCESS] Screenshot refreshed successfully
[[15:26:38]] [SUCCESS] Screenshot refreshed successfully
[[15:26:38]] [INFO] Executing Multi Step action step 3/42: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[15:26:37]] [SUCCESS] Screenshot refreshed
[[15:26:37]] [INFO] Refreshing screenshot...
[[15:26:32]] [SUCCESS] Screenshot refreshed successfully
[[15:26:32]] [SUCCESS] Screenshot refreshed successfully
[[15:26:32]] [INFO] Executing Multi Step action step 2/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[15:26:31]] [SUCCESS] Screenshot refreshed
[[15:26:31]] [INFO] Refreshing screenshot...
[[15:26:25]] [INFO] Executing Multi Step action step 1/42: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[15:26:25]] [INFO] Loaded 42 steps from test case: Delivery Buy Steps
[[15:26:25]] [SUCCESS] Screenshot refreshed successfully
[[15:26:25]] [SUCCESS] Screenshot refreshed successfully
[[15:26:25]] [INFO] Loading steps for multiStep action: Delivery Buy Steps
[[15:26:25]] [INFO] ZxObWodIp8=running
[[15:26:25]] [INFO] Executing action 400/591: Execute Test Case: Delivery Buy Steps (41 steps)
[[15:26:25]] [SUCCESS] Screenshot refreshed
[[15:26:25]] [INFO] Refreshing screenshot...
[[15:26:25]] [INFO] ZlrZ0BjA1R=pass
[[15:26:20]] [SUCCESS] Screenshot refreshed successfully
[[15:26:20]] [SUCCESS] Screenshot refreshed successfully
[[15:26:20]] [INFO] ZlrZ0BjA1R=running
[[15:26:20]] [INFO] Executing action 399/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:26:19]] [SUCCESS] Screenshot refreshed
[[15:26:19]] [INFO] Refreshing screenshot...
[[15:26:19]] [INFO] F4NGh9HrLw=pass
[[15:26:15]] [SUCCESS] Screenshot refreshed successfully
[[15:26:15]] [SUCCESS] Screenshot refreshed successfully
[[15:26:14]] [INFO] F4NGh9HrLw=running
[[15:26:14]] [INFO] Executing action 398/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:26:14]] [SUCCESS] Screenshot refreshed
[[15:26:14]] [INFO] Refreshing screenshot...
[[15:26:14]] [INFO] 4eEEGs1x8i=pass
[[15:26:01]] [SUCCESS] Screenshot refreshed successfully
[[15:26:01]] [SUCCESS] Screenshot refreshed successfully
[[15:26:01]] [INFO] 4eEEGs1x8i=running
[[15:26:01]] [INFO] Executing action 397/591: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[15:26:00]] [SUCCESS] Screenshot refreshed
[[15:26:00]] [INFO] Refreshing screenshot...
[[15:26:00]] [INFO] XOaZPEqzKU=pass
[[15:25:51]] [SUCCESS] Screenshot refreshed successfully
[[15:25:51]] [SUCCESS] Screenshot refreshed successfully
[[15:25:51]] [INFO] XOaZPEqzKU=running
[[15:25:51]] [INFO] Executing action 396/591: Tap if locator exists: accessibility_id="Add to bag"
[[15:25:50]] [SUCCESS] Screenshot refreshed
[[15:25:50]] [INFO] Refreshing screenshot...
[[15:25:50]] [INFO] hlB6ARmBVC=pass
[[15:25:49]] [SUCCESS] Screenshot refreshed successfully
[[15:25:49]] [SUCCESS] Screenshot refreshed successfully
[[15:25:48]] [INFO] hlB6ARmBVC=running
[[15:25:48]] [INFO] Executing action 395/591: Tap on image if exists: add-to-bag-ip14.png
[[15:25:48]] [SUCCESS] Screenshot refreshed
[[15:25:48]] [INFO] Refreshing screenshot...
[[15:25:48]] [INFO] ALWzI9hXIc=pass
[[15:25:42]] [SUCCESS] Screenshot refreshed successfully
[[15:25:42]] [SUCCESS] Screenshot refreshed successfully
[[15:25:42]] [INFO] ALWzI9hXIc=running
[[15:25:42]] [INFO] Executing action 394/591: Swipe from (50%, 50%) to (50%, 30%)
[[15:25:41]] [SUCCESS] Screenshot refreshed
[[15:25:41]] [INFO] Refreshing screenshot...
[[15:25:41]] [INFO] CcFsA41sKp=pass
[[15:25:37]] [SUCCESS] Screenshot refreshed successfully
[[15:25:37]] [SUCCESS] Screenshot refreshed successfully
[[15:25:37]] [INFO] CcFsA41sKp=running
[[15:25:37]] [INFO] Executing action 393/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:25:37]] [SUCCESS] Screenshot refreshed
[[15:25:37]] [INFO] Refreshing screenshot...
[[15:25:37]] [INFO] 8XWyF2kgwW=pass
[[15:25:34]] [SUCCESS] Screenshot refreshed successfully
[[15:25:34]] [SUCCESS] Screenshot refreshed successfully
[[15:25:32]] [INFO] 8XWyF2kgwW=running
[[15:25:32]] [INFO] Executing action 392/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:25:32]] [SUCCESS] Screenshot refreshed
[[15:25:32]] [INFO] Refreshing screenshot...
[[15:25:32]] [INFO] qG4RkNac30=pass
[[15:25:27]] [SUCCESS] Screenshot refreshed successfully
[[15:25:27]] [SUCCESS] Screenshot refreshed successfully
[[15:25:27]] [INFO] qG4RkNac30=running
[[15:25:27]] [INFO] Executing action 391/591: iOS Function: text - Text: "P_42691341"
[[15:25:27]] [SUCCESS] Screenshot refreshed
[[15:25:27]] [INFO] Refreshing screenshot...
[[15:25:27]] [INFO] Jtn2FK4THX=pass
[[15:25:21]] [SUCCESS] Screenshot refreshed successfully
[[15:25:21]] [SUCCESS] Screenshot refreshed successfully
[[15:25:21]] [INFO] Jtn2FK4THX=running
[[15:25:21]] [INFO] Executing action 390/591: Tap on Text: "Find"
[[15:25:20]] [SUCCESS] Screenshot refreshed
[[15:25:20]] [INFO] Refreshing screenshot...
[[15:25:20]] [INFO] tWq2Qzn22D=pass
[[15:25:16]] [SUCCESS] Screenshot refreshed successfully
[[15:25:16]] [SUCCESS] Screenshot refreshed successfully
[[15:25:16]] [INFO] tWq2Qzn22D=running
[[15:25:16]] [INFO] Executing action 389/591: Tap on image: env[device-back-img]
[[15:25:15]] [SUCCESS] Screenshot refreshed
[[15:25:15]] [INFO] Refreshing screenshot...
[[15:25:15]] [INFO] 5hClb2pKKx=pass
[[15:24:53]] [SUCCESS] Screenshot refreshed successfully
[[15:24:53]] [SUCCESS] Screenshot refreshed successfully
[[15:24:53]] [INFO] 5hClb2pKKx=running
[[15:24:53]] [INFO] Executing action 388/591: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[15:24:52]] [SUCCESS] Screenshot refreshed
[[15:24:52]] [INFO] Refreshing screenshot...
[[15:24:52]] [INFO] jmKjclMUWT=pass
[[15:24:47]] [SUCCESS] Screenshot refreshed successfully
[[15:24:47]] [SUCCESS] Screenshot refreshed successfully
[[15:24:47]] [INFO] jmKjclMUWT=running
[[15:24:47]] [INFO] Executing action 387/591: Tap on Text: "current"
[[15:24:47]] [SUCCESS] Screenshot refreshed
[[15:24:47]] [INFO] Refreshing screenshot...
[[15:24:47]] [INFO] UoH0wdtcLk=pass
[[15:24:42]] [SUCCESS] Screenshot refreshed successfully
[[15:24:42]] [SUCCESS] Screenshot refreshed successfully
[[15:24:41]] [INFO] UoH0wdtcLk=running
[[15:24:41]] [INFO] Executing action 386/591: Tap on Text: "Edit"
[[15:24:40]] [SUCCESS] Screenshot refreshed
[[15:24:40]] [INFO] Refreshing screenshot...
[[15:24:40]] [INFO] U48qCNydwd=pass
[[15:24:35]] [SUCCESS] Screenshot refreshed successfully
[[15:24:35]] [SUCCESS] Screenshot refreshed successfully
[[15:24:35]] [INFO] U48qCNydwd=running
[[15:24:35]] [INFO] Executing action 385/591: Restart app: env[appid]
[[15:24:35]] [SUCCESS] Screenshot refreshed
[[15:24:35]] [INFO] Refreshing screenshot...
[[15:24:35]] [INFO] XjclKOaCTh=pass
[[15:24:29]] [SUCCESS] Screenshot refreshed successfully
[[15:24:29]] [SUCCESS] Screenshot refreshed successfully
[[15:24:29]] [INFO] XjclKOaCTh=running
[[15:24:29]] [INFO] Executing action 384/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[15:24:28]] [SUCCESS] Screenshot refreshed
[[15:24:28]] [INFO] Refreshing screenshot...
[[15:24:28]] [INFO] q6cKxgMAIn=pass
[[15:24:25]] [SUCCESS] Screenshot refreshed successfully
[[15:24:25]] [SUCCESS] Screenshot refreshed successfully
[[15:24:25]] [INFO] q6cKxgMAIn=running
[[15:24:25]] [INFO] Executing action 383/591: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[15:24:24]] [SUCCESS] Screenshot refreshed
[[15:24:24]] [INFO] Refreshing screenshot...
[[15:24:24]] [INFO] zdh8hKYC1a=pass
[[15:24:20]] [SUCCESS] Screenshot refreshed successfully
[[15:24:20]] [SUCCESS] Screenshot refreshed successfully
[[15:24:20]] [INFO] zdh8hKYC1a=running
[[15:24:20]] [INFO] Executing action 382/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[15:24:19]] [SUCCESS] Screenshot refreshed
[[15:24:19]] [INFO] Refreshing screenshot...
[[15:24:19]] [INFO] P4b2BITpCf=pass
[[15:24:16]] [SUCCESS] Screenshot refreshed successfully
[[15:24:16]] [SUCCESS] Screenshot refreshed successfully
[[15:24:16]] [INFO] P4b2BITpCf=running
[[15:24:16]] [INFO] Executing action 381/591: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[15:24:15]] [SUCCESS] Screenshot refreshed
[[15:24:15]] [INFO] Refreshing screenshot...
[[15:24:15]] [INFO] inrxgdWzXr=pass
[[15:24:10]] [SUCCESS] Screenshot refreshed successfully
[[15:24:10]] [SUCCESS] Screenshot refreshed successfully
[[15:24:10]] [INFO] inrxgdWzXr=running
[[15:24:10]] [INFO] Executing action 380/591: Tap on Text: "Store"
[[15:24:10]] [SUCCESS] Screenshot refreshed
[[15:24:10]] [INFO] Refreshing screenshot...
[[15:24:10]] [INFO] inrxgdWzXr=pass
[[15:24:05]] [SUCCESS] Screenshot refreshed successfully
[[15:24:05]] [SUCCESS] Screenshot refreshed successfully
[[15:24:05]] [INFO] inrxgdWzXr=running
[[15:24:05]] [INFO] Executing action 379/591: Tap on Text: "receipts"
[[15:24:05]] [SUCCESS] Screenshot refreshed
[[15:24:05]] [INFO] Refreshing screenshot...
[[15:24:05]] [INFO] GEMv6goQtW=pass
[[15:24:01]] [SUCCESS] Screenshot refreshed successfully
[[15:24:01]] [SUCCESS] Screenshot refreshed successfully
[[15:24:01]] [INFO] GEMv6goQtW=running
[[15:24:01]] [INFO] Executing action 378/591: Tap on image: env[device-back-img]
[[15:24:00]] [SUCCESS] Screenshot refreshed
[[15:24:00]] [INFO] Refreshing screenshot...
[[15:24:00]] [INFO] DhWa2PCBXE=pass
[[15:23:57]] [SUCCESS] Screenshot refreshed successfully
[[15:23:57]] [SUCCESS] Screenshot refreshed successfully
[[15:23:57]] [INFO] DhWa2PCBXE=running
[[15:23:57]] [INFO] Executing action 377/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[15:23:56]] [SUCCESS] Screenshot refreshed
[[15:23:56]] [INFO] Refreshing screenshot...
[[15:23:56]] [INFO] pk2DLZFBmx=pass
[[15:23:52]] [SUCCESS] Screenshot refreshed successfully
[[15:23:52]] [SUCCESS] Screenshot refreshed successfully
[[15:23:52]] [INFO] pk2DLZFBmx=running
[[15:23:52]] [INFO] Executing action 376/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[15:23:52]] [SUCCESS] Screenshot refreshed
[[15:23:52]] [INFO] Refreshing screenshot...
[[15:23:52]] [INFO] ShJSdXvmVL=pass
[[15:23:48]] [SUCCESS] Screenshot refreshed successfully
[[15:23:48]] [SUCCESS] Screenshot refreshed successfully
[[15:23:48]] [INFO] ShJSdXvmVL=running
[[15:23:48]] [INFO] Executing action 375/591: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[15:23:48]] [SUCCESS] Screenshot refreshed
[[15:23:48]] [INFO] Refreshing screenshot...
[[15:23:48]] [INFO] kWPRvuo7kk=pass
[[15:23:43]] [SUCCESS] Screenshot refreshed successfully
[[15:23:43]] [SUCCESS] Screenshot refreshed successfully
[[15:23:43]] [INFO] kWPRvuo7kk=running
[[15:23:43]] [INFO] Executing action 374/591: iOS Function: text - Text: "env[pwd-op]"
[[15:23:43]] [SUCCESS] Screenshot refreshed
[[15:23:43]] [INFO] Refreshing screenshot...
[[15:23:43]] [INFO] d6vTfR4Y0D=pass
[[15:23:38]] [SUCCESS] Screenshot refreshed successfully
[[15:23:38]] [SUCCESS] Screenshot refreshed successfully
[[15:23:38]] [INFO] d6vTfR4Y0D=running
[[15:23:38]] [INFO] Executing action 373/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:23:38]] [SUCCESS] Screenshot refreshed
[[15:23:38]] [INFO] Refreshing screenshot...
[[15:23:38]] [INFO] pe9W6tZdXT=pass
[[15:23:31]] [SUCCESS] Screenshot refreshed successfully
[[15:23:31]] [SUCCESS] Screenshot refreshed successfully
[[15:23:31]] [INFO] pe9W6tZdXT=running
[[15:23:31]] [INFO] Executing action 372/591: iOS Function: text - Text: "env[uname-op]"
[[15:23:30]] [SUCCESS] Screenshot refreshed
[[15:23:30]] [INFO] Refreshing screenshot...
[[15:23:30]] [INFO] u928vFzSni=pass
[[15:23:26]] [SUCCESS] Screenshot refreshed successfully
[[15:23:26]] [SUCCESS] Screenshot refreshed successfully
[[15:23:26]] [INFO] u928vFzSni=running
[[15:23:26]] [INFO] Executing action 371/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:23:25]] [SUCCESS] Screenshot refreshed
[[15:23:25]] [INFO] Refreshing screenshot...
[[15:23:25]] [INFO] s0WyiD1w0B=pass
[[15:23:23]] [SUCCESS] Screenshot refreshed successfully
[[15:23:23]] [SUCCESS] Screenshot refreshed successfully
[[15:23:23]] [INFO] s0WyiD1w0B=running
[[15:23:23]] [INFO] Executing action 370/591: iOS Function: alert_accept
[[15:23:22]] [SUCCESS] Screenshot refreshed
[[15:23:22]] [INFO] Refreshing screenshot...
[[15:23:22]] [INFO] gekNSY5O2E=pass
[[15:23:18]] [SUCCESS] Screenshot refreshed successfully
[[15:23:18]] [SUCCESS] Screenshot refreshed successfully
[[15:23:18]] [INFO] gekNSY5O2E=running
[[15:23:18]] [INFO] Executing action 369/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[15:23:17]] [SUCCESS] Screenshot refreshed
[[15:23:17]] [INFO] Refreshing screenshot...
[[15:23:17]] [INFO] VJJ3EXXotU=pass
[[15:23:13]] [SUCCESS] Screenshot refreshed successfully
[[15:23:13]] [SUCCESS] Screenshot refreshed successfully
[[15:23:13]] [INFO] VJJ3EXXotU=running
[[15:23:13]] [INFO] Executing action 368/591: Tap on image: env[device-back-img]
[[15:23:13]] [SUCCESS] Screenshot refreshed
[[15:23:13]] [INFO] Refreshing screenshot...
[[15:23:13]] [INFO] 83tV9A4NOn=pass
[[15:23:10]] [SUCCESS] Screenshot refreshed successfully
[[15:23:10]] [SUCCESS] Screenshot refreshed successfully
[[15:23:10]] [INFO] 83tV9A4NOn=running
[[15:23:10]] [INFO] Executing action 367/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[15:23:09]] [SUCCESS] Screenshot refreshed
[[15:23:09]] [INFO] Refreshing screenshot...
[[15:23:09]] [INFO] aNN0yYFLEd=pass
[[15:23:05]] [SUCCESS] Screenshot refreshed successfully
[[15:23:05]] [SUCCESS] Screenshot refreshed successfully
[[15:23:05]] [INFO] aNN0yYFLEd=running
[[15:23:05]] [INFO] Executing action 366/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[15:23:04]] [SUCCESS] Screenshot refreshed
[[15:23:04]] [INFO] Refreshing screenshot...
[[15:23:04]] [INFO] XJv08Gkucs=pass
[[15:23:00]] [SUCCESS] Screenshot refreshed successfully
[[15:23:00]] [SUCCESS] Screenshot refreshed successfully
[[15:23:00]] [INFO] XJv08Gkucs=running
[[15:23:00]] [INFO] Executing action 365/591: Input text: "env[uname-op]"
[[15:23:00]] [SUCCESS] Screenshot refreshed
[[15:23:00]] [INFO] Refreshing screenshot...
[[15:23:00]] [INFO] kAQ1yIIw3h=pass
[[15:22:56]] [SUCCESS] Screenshot refreshed successfully
[[15:22:56]] [SUCCESS] Screenshot refreshed successfully
[[15:22:56]] [INFO] kAQ1yIIw3h=running
[[15:22:56]] [INFO] Executing action 364/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[15:22:55]] [SUCCESS] Screenshot refreshed
[[15:22:55]] [INFO] Refreshing screenshot...
[[15:22:55]] [INFO] 7YbjwQH1Jc=pass
[[15:22:52]] [SUCCESS] Screenshot refreshed successfully
[[15:22:52]] [SUCCESS] Screenshot refreshed successfully
[[15:22:52]] [INFO] 7YbjwQH1Jc=running
[[15:22:52]] [INFO] Executing action 363/591: Input text: "env[searchorder]"
[[15:22:51]] [SUCCESS] Screenshot refreshed
[[15:22:51]] [INFO] Refreshing screenshot...
[[15:22:51]] [INFO] OmKfD9iBjD=pass
[[15:22:47]] [SUCCESS] Screenshot refreshed successfully
[[15:22:47]] [SUCCESS] Screenshot refreshed successfully
[[15:22:47]] [INFO] OmKfD9iBjD=running
[[15:22:47]] [INFO] Executing action 362/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[15:22:47]] [SUCCESS] Screenshot refreshed
[[15:22:47]] [INFO] Refreshing screenshot...
[[15:22:47]] [INFO] eHLWiRoqqS=pass
[[15:22:43]] [SUCCESS] Screenshot refreshed successfully
[[15:22:43]] [SUCCESS] Screenshot refreshed successfully
[[15:22:43]] [INFO] eHLWiRoqqS=running
[[15:22:43]] [INFO] Executing action 361/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[15:22:42]] [SUCCESS] Screenshot refreshed
[[15:22:42]] [INFO] Refreshing screenshot...
[[15:22:42]] [INFO] F4NGh9HrLw=pass
[[15:22:39]] [SUCCESS] Screenshot refreshed successfully
[[15:22:39]] [SUCCESS] Screenshot refreshed successfully
[[15:22:38]] [INFO] F4NGh9HrLw=running
[[15:22:38]] [INFO] Executing action 360/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:22:38]] [SUCCESS] Screenshot refreshed
[[15:22:38]] [INFO] Refreshing screenshot...
[[15:22:38]] [INFO] 74XW7x54ad=pass
[[15:22:33]] [SUCCESS] Screenshot refreshed successfully
[[15:22:33]] [SUCCESS] Screenshot refreshed successfully
[[15:22:32]] [INFO] 74XW7x54ad=running
[[15:22:32]] [INFO] Executing action 359/591: Tap on image: env[device-back-img]
[[15:22:31]] [SUCCESS] Screenshot refreshed
[[15:22:31]] [INFO] Refreshing screenshot...
[[15:22:31]] [INFO] xUbWFa8Ok2=pass
[[15:22:28]] [SUCCESS] Screenshot refreshed successfully
[[15:22:28]] [SUCCESS] Screenshot refreshed successfully
[[15:22:27]] [INFO] xUbWFa8Ok2=running
[[15:22:27]] [INFO] Executing action 358/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[15:22:26]] [SUCCESS] Screenshot refreshed
[[15:22:26]] [INFO] Refreshing screenshot...
[[15:22:26]] [INFO] RbNtEW6N9T=pass
[[15:22:23]] [SUCCESS] Screenshot refreshed successfully
[[15:22:23]] [SUCCESS] Screenshot refreshed successfully
[[15:22:22]] [INFO] RbNtEW6N9T=running
[[15:22:22]] [INFO] Executing action 357/591: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[15:22:22]] [SUCCESS] Screenshot refreshed
[[15:22:22]] [INFO] Refreshing screenshot...
[[15:22:22]] [INFO] F4NGh9HrLw=pass
[[15:22:19]] [SUCCESS] Screenshot refreshed successfully
[[15:22:19]] [SUCCESS] Screenshot refreshed successfully
[[15:22:17]] [INFO] F4NGh9HrLw=running
[[15:22:17]] [INFO] Executing action 356/591: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[15:22:17]] [SUCCESS] Screenshot refreshed
[[15:22:17]] [INFO] Refreshing screenshot...
[[15:22:17]] [INFO] RlDZFks4Lc=pass
[[15:22:15]] [SUCCESS] Screenshot refreshed successfully
[[15:22:15]] [SUCCESS] Screenshot refreshed successfully
[[15:22:13]] [INFO] RlDZFks4Lc=running
[[15:22:13]] [INFO] Executing action 355/591: iOS Function: alert_accept
[[15:22:13]] [SUCCESS] Screenshot refreshed
[[15:22:13]] [INFO] Refreshing screenshot...
[[15:22:13]] [INFO] Dzn2Q7JTe0=pass
[[15:22:08]] [SUCCESS] Screenshot refreshed successfully
[[15:22:08]] [SUCCESS] Screenshot refreshed successfully
[[15:22:07]] [INFO] Dzn2Q7JTe0=running
[[15:22:07]] [INFO] Executing action 354/591: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[15:22:07]] [SUCCESS] Screenshot refreshed
[[15:22:07]] [INFO] Refreshing screenshot...
[[15:22:07]] [INFO] H9fy9qcFbZ=pass
[[15:21:54]] [SUCCESS] Screenshot refreshed successfully
[[15:21:54]] [SUCCESS] Screenshot refreshed successfully
[[15:21:52]] [INFO] H9fy9qcFbZ=running
[[15:21:52]] [INFO] Executing action 353/591: Restart app: env[appid]
[[15:21:52]] [SUCCESS] Screenshot refreshed
[[15:21:52]] [INFO] Refreshing screenshot...
[[15:21:52]] [SUCCESS] Screenshot refreshed
[[15:21:52]] [INFO] Refreshing screenshot...
[[15:21:49]] [SUCCESS] Screenshot refreshed successfully
[[15:21:49]] [SUCCESS] Screenshot refreshed successfully
[[15:21:48]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:21:48]] [SUCCESS] Screenshot refreshed
[[15:21:48]] [INFO] Refreshing screenshot...
[[15:21:35]] [SUCCESS] Screenshot refreshed successfully
[[15:21:35]] [SUCCESS] Screenshot refreshed successfully
[[15:21:35]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:21:35]] [SUCCESS] Screenshot refreshed
[[15:21:35]] [INFO] Refreshing screenshot...
[[15:21:30]] [SUCCESS] Screenshot refreshed successfully
[[15:21:30]] [SUCCESS] Screenshot refreshed successfully
[[15:21:30]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:21:30]] [SUCCESS] Screenshot refreshed
[[15:21:30]] [INFO] Refreshing screenshot...
[[15:21:26]] [SUCCESS] Screenshot refreshed successfully
[[15:21:26]] [SUCCESS] Screenshot refreshed successfully
[[15:21:26]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:21:25]] [SUCCESS] Screenshot refreshed
[[15:21:25]] [INFO] Refreshing screenshot...
[[15:21:18]] [SUCCESS] Screenshot refreshed successfully
[[15:21:18]] [SUCCESS] Screenshot refreshed successfully
[[15:21:18]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:21:17]] [SUCCESS] Screenshot refreshed
[[15:21:17]] [INFO] Refreshing screenshot...
[[15:21:11]] [SUCCESS] Screenshot refreshed successfully
[[15:21:11]] [SUCCESS] Screenshot refreshed successfully
[[15:21:10]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:21:10]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:21:10]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:21:10]] [INFO] AeQaElnzUN=running
[[15:21:10]] [INFO] Executing action 352/591: cleanupSteps action
[[15:21:09]] [SUCCESS] Screenshot refreshed
[[15:21:09]] [INFO] Refreshing screenshot...
[[15:21:09]] [INFO] BracBsfa3Y=pass
[[15:21:04]] [SUCCESS] Screenshot refreshed successfully
[[15:21:04]] [SUCCESS] Screenshot refreshed successfully
[[15:21:04]] [INFO] BracBsfa3Y=running
[[15:21:04]] [INFO] Executing action 351/591: Tap on Text: "out"
[[15:21:04]] [SUCCESS] Screenshot refreshed
[[15:21:04]] [INFO] Refreshing screenshot...
[[15:21:04]] [INFO] s6tWdQ5URW=pass
[[15:20:56]] [SUCCESS] Screenshot refreshed successfully
[[15:20:56]] [SUCCESS] Screenshot refreshed successfully
[[15:20:56]] [INFO] s6tWdQ5URW=running
[[15:20:56]] [INFO] Executing action 350/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:20:55]] [SUCCESS] Screenshot refreshed
[[15:20:55]] [INFO] Refreshing screenshot...
[[15:20:55]] [INFO] wNGRrfUjpK=pass
[[15:20:51]] [SUCCESS] Screenshot refreshed successfully
[[15:20:51]] [SUCCESS] Screenshot refreshed successfully
[[15:20:51]] [INFO] wNGRrfUjpK=running
[[15:20:51]] [INFO] Executing action 349/591: Tap on image: env[device-back-img]
[[15:20:51]] [SUCCESS] Screenshot refreshed
[[15:20:51]] [INFO] Refreshing screenshot...
[[15:20:51]] [INFO] BracBsfa3Y=pass
[[15:20:46]] [SUCCESS] Screenshot refreshed successfully
[[15:20:46]] [SUCCESS] Screenshot refreshed successfully
[[15:20:46]] [INFO] BracBsfa3Y=running
[[15:20:46]] [INFO] Executing action 348/591: Tap on Text: "Customer"
[[15:20:46]] [SUCCESS] Screenshot refreshed
[[15:20:46]] [INFO] Refreshing screenshot...
[[15:20:46]] [INFO] H4WfwVU8YP=pass
[[15:20:42]] [SUCCESS] Screenshot refreshed successfully
[[15:20:42]] [SUCCESS] Screenshot refreshed successfully
[[15:20:41]] [INFO] H4WfwVU8YP=running
[[15:20:41]] [INFO] Executing action 347/591: Tap on image: banner-close-updated.png
[[15:20:41]] [SUCCESS] Screenshot refreshed
[[15:20:41]] [INFO] Refreshing screenshot...
[[15:20:41]] [INFO] ePyaYpttQA=pass
[[15:20:37]] [SUCCESS] Screenshot refreshed successfully
[[15:20:37]] [SUCCESS] Screenshot refreshed successfully
[[15:20:37]] [INFO] ePyaYpttQA=running
[[15:20:37]] [INFO] Executing action 346/591: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[15:20:37]] [SUCCESS] Screenshot refreshed
[[15:20:37]] [INFO] Refreshing screenshot...
[[15:20:37]] [INFO] BracBsfa3Y=pass
[[15:20:31]] [SUCCESS] Screenshot refreshed successfully
[[15:20:31]] [SUCCESS] Screenshot refreshed successfully
[[15:20:31]] [INFO] BracBsfa3Y=running
[[15:20:31]] [INFO] Executing action 345/591: Tap on Text: "Invite"
[[15:20:30]] [SUCCESS] Screenshot refreshed
[[15:20:30]] [INFO] Refreshing screenshot...
[[15:20:30]] [INFO] xVbCNStsOP=pass
[[15:20:27]] [SUCCESS] Screenshot refreshed successfully
[[15:20:27]] [SUCCESS] Screenshot refreshed successfully
[[15:20:26]] [INFO] xVbCNStsOP=running
[[15:20:26]] [INFO] Executing action 344/591: Tap on image: env[device-back-img]
[[15:20:26]] [SUCCESS] Screenshot refreshed
[[15:20:26]] [INFO] Refreshing screenshot...
[[15:20:26]] [INFO] 8kQkC2FGyZ=pass
[[15:20:23]] [INFO] 8kQkC2FGyZ=running
[[15:20:23]] [INFO] Executing action 343/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[15:20:23]] [SUCCESS] Screenshot refreshed successfully
[[15:20:23]] [SUCCESS] Screenshot refreshed successfully
[[15:20:22]] [SUCCESS] Screenshot refreshed
[[15:20:22]] [INFO] Refreshing screenshot...
[[15:20:22]] [INFO] PgjJCrKFYo=pass
[[15:20:17]] [SUCCESS] Screenshot refreshed successfully
[[15:20:17]] [SUCCESS] Screenshot refreshed successfully
[[15:20:17]] [INFO] PgjJCrKFYo=running
[[15:20:17]] [INFO] Executing action 342/591: Tap on Text: "VIC"
[[15:20:16]] [SUCCESS] Screenshot refreshed
[[15:20:16]] [INFO] Refreshing screenshot...
[[15:20:16]] [INFO] 3Si0csRNaw=pass
[[15:20:09]] [SUCCESS] Screenshot refreshed successfully
[[15:20:09]] [SUCCESS] Screenshot refreshed successfully
[[15:20:09]] [INFO] 3Si0csRNaw=running
[[15:20:09]] [INFO] Executing action 341/591: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[15:20:09]] [SUCCESS] Screenshot refreshed
[[15:20:09]] [INFO] Refreshing screenshot...
[[15:20:09]] [INFO] BracBsfa3Y=pass
[[15:20:04]] [SUCCESS] Screenshot refreshed successfully
[[15:20:04]] [SUCCESS] Screenshot refreshed successfully
[[15:20:03]] [INFO] BracBsfa3Y=running
[[15:20:03]] [INFO] Executing action 340/591: Tap on Text: "Nearby"
[[15:20:03]] [SUCCESS] Screenshot refreshed
[[15:20:03]] [INFO] Refreshing screenshot...
[[15:20:03]] [INFO] BracBsfa3Y=pass
[[15:19:58]] [SUCCESS] Screenshot refreshed successfully
[[15:19:58]] [SUCCESS] Screenshot refreshed successfully
[[15:19:58]] [INFO] BracBsfa3Y=running
[[15:19:58]] [INFO] Executing action 339/591: Tap on Text: "locator"
[[15:19:58]] [SUCCESS] Screenshot refreshed
[[15:19:58]] [INFO] Refreshing screenshot...
[[15:19:58]] [INFO] s6tWdQ5URW=pass
[[15:19:51]] [SUCCESS] Screenshot refreshed successfully
[[15:19:51]] [SUCCESS] Screenshot refreshed successfully
[[15:19:51]] [INFO] s6tWdQ5URW=running
[[15:19:51]] [INFO] Executing action 338/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:19:50]] [SUCCESS] Screenshot refreshed
[[15:19:50]] [INFO] Refreshing screenshot...
[[15:19:50]] [INFO] 2M0KHOVecv=pass
[[15:19:47]] [SUCCESS] Screenshot refreshed successfully
[[15:19:47]] [SUCCESS] Screenshot refreshed successfully
[[15:19:47]] [INFO] 2M0KHOVecv=running
[[15:19:47]] [INFO] Executing action 337/591: Check if element with accessibility_id="txtMy Flybuys card" exists
[[15:19:46]] [SUCCESS] Screenshot refreshed
[[15:19:46]] [INFO] Refreshing screenshot...
[[15:19:46]] [INFO] LBgsj3oLcu=pass
[[15:19:42]] [SUCCESS] Screenshot refreshed successfully
[[15:19:42]] [SUCCESS] Screenshot refreshed successfully
[[15:19:42]] [INFO] LBgsj3oLcu=running
[[15:19:42]] [INFO] Executing action 336/591: Tap on image: env[device-back-img]
[[15:19:42]] [SUCCESS] Screenshot refreshed
[[15:19:42]] [INFO] Refreshing screenshot...
[[15:19:42]] [INFO] biRyWs3nSs=pass
[[15:19:35]] [SUCCESS] Screenshot refreshed successfully
[[15:19:35]] [SUCCESS] Screenshot refreshed successfully
[[15:19:35]] [INFO] biRyWs3nSs=running
[[15:19:35]] [INFO] Executing action 335/591: Tap on element with accessibility_id: btnSaveFlybuysCard
[[15:19:35]] [SUCCESS] Screenshot refreshed
[[15:19:35]] [INFO] Refreshing screenshot...
[[15:19:35]] [INFO] 8cFGh3GD68=pass
[[15:19:28]] [SUCCESS] Screenshot refreshed successfully
[[15:19:28]] [SUCCESS] Screenshot refreshed successfully
[[15:19:28]] [INFO] 8cFGh3GD68=running
[[15:19:28]] [INFO] Executing action 334/591: Tap on element with accessibility_id: Done
[[15:19:28]] [SUCCESS] Screenshot refreshed
[[15:19:28]] [INFO] Refreshing screenshot...
[[15:19:28]] [INFO] sLe0Wurhgm=pass
[[15:19:25]] [SUCCESS] Screenshot refreshed successfully
[[15:19:25]] [SUCCESS] Screenshot refreshed successfully
[[15:19:25]] [INFO] sLe0Wurhgm=running
[[15:19:25]] [INFO] Executing action 333/591: Input text: "2791234567890"
[[15:19:24]] [SUCCESS] Screenshot refreshed
[[15:19:24]] [INFO] Refreshing screenshot...
[[15:19:24]] [INFO] Ey86YRVRzU=pass
[[15:19:18]] [SUCCESS] Screenshot refreshed successfully
[[15:19:18]] [SUCCESS] Screenshot refreshed successfully
[[15:19:18]] [INFO] Ey86YRVRzU=running
[[15:19:18]] [INFO] Executing action 332/591: Tap on element with accessibility_id: Flybuys barcode number
[[15:19:18]] [SUCCESS] Screenshot refreshed
[[15:19:18]] [INFO] Refreshing screenshot...
[[15:19:18]] [INFO] Gxhf3XGc6e=pass
[[15:19:12]] [SUCCESS] Screenshot refreshed successfully
[[15:19:12]] [SUCCESS] Screenshot refreshed successfully
[[15:19:12]] [INFO] Gxhf3XGc6e=running
[[15:19:12]] [INFO] Executing action 331/591: Tap on element with accessibility_id: btnLinkFlyBuys
[[15:19:11]] [SUCCESS] Screenshot refreshed
[[15:19:11]] [INFO] Refreshing screenshot...
[[15:19:11]] [INFO] BracBsfa3Y=pass
[[15:19:06]] [SUCCESS] Screenshot refreshed successfully
[[15:19:06]] [SUCCESS] Screenshot refreshed successfully
[[15:19:06]] [INFO] BracBsfa3Y=running
[[15:19:06]] [INFO] Executing action 330/591: Tap on Text: "Flybuys"
[[15:19:06]] [SUCCESS] Screenshot refreshed
[[15:19:06]] [INFO] Refreshing screenshot...
[[15:19:06]] [INFO] Ds5GfNVb3x=pass
[[15:18:59]] [SUCCESS] Screenshot refreshed successfully
[[15:18:59]] [SUCCESS] Screenshot refreshed successfully
[[15:18:59]] [INFO] Ds5GfNVb3x=running
[[15:18:59]] [INFO] Executing action 329/591: Tap on element with accessibility_id: btnRemove
[[15:18:59]] [SUCCESS] Screenshot refreshed
[[15:18:59]] [INFO] Refreshing screenshot...
[[15:18:59]] [INFO] 3ZFgwFaiXp=pass
[[15:18:53]] [SUCCESS] Screenshot refreshed successfully
[[15:18:53]] [SUCCESS] Screenshot refreshed successfully
[[15:18:53]] [INFO] 3ZFgwFaiXp=running
[[15:18:53]] [INFO] Executing action 328/591: Tap on element with accessibility_id: Remove card
[[15:18:52]] [SUCCESS] Screenshot refreshed
[[15:18:52]] [INFO] Refreshing screenshot...
[[15:18:52]] [INFO] 40hnWPsQ9P=pass
[[15:18:46]] [SUCCESS] Screenshot refreshed successfully
[[15:18:46]] [SUCCESS] Screenshot refreshed successfully
[[15:18:46]] [INFO] 40hnWPsQ9P=running
[[15:18:46]] [INFO] Executing action 327/591: Tap on element with accessibility_id: btneditFlybuysCard
[[15:18:46]] [SUCCESS] Screenshot refreshed
[[15:18:46]] [INFO] Refreshing screenshot...
[[15:18:46]] [INFO] 40hnWPsQ9P=pass
[[15:18:41]] [SUCCESS] Screenshot refreshed successfully
[[15:18:41]] [SUCCESS] Screenshot refreshed successfully
[[15:18:41]] [INFO] 40hnWPsQ9P=running
[[15:18:41]] [INFO] Executing action 326/591: Wait till accessibility_id=btneditFlybuysCard
[[15:18:40]] [SUCCESS] Screenshot refreshed
[[15:18:40]] [INFO] Refreshing screenshot...
[[15:18:40]] [INFO] BracBsfa3Y=pass
[[15:18:35]] [SUCCESS] Screenshot refreshed successfully
[[15:18:35]] [SUCCESS] Screenshot refreshed successfully
[[15:18:35]] [INFO] BracBsfa3Y=running
[[15:18:35]] [INFO] Executing action 325/591: Tap on Text: "Flybuys"
[[15:18:35]] [SUCCESS] Screenshot refreshed
[[15:18:35]] [INFO] Refreshing screenshot...
[[15:18:35]] [INFO] MkTFxfzubv=pass
[[15:18:30]] [SUCCESS] Screenshot refreshed successfully
[[15:18:30]] [SUCCESS] Screenshot refreshed successfully
[[15:18:30]] [INFO] MkTFxfzubv=running
[[15:18:30]] [INFO] Executing action 324/591: Tap on image: env[device-back-img]
[[15:18:30]] [SUCCESS] Screenshot refreshed
[[15:18:30]] [INFO] Refreshing screenshot...
[[15:18:30]] [INFO] EO3cMmdUyM=pass
[[15:18:26]] [SUCCESS] Screenshot refreshed successfully
[[15:18:26]] [SUCCESS] Screenshot refreshed successfully
[[15:18:26]] [INFO] EO3cMmdUyM=running
[[15:18:26]] [INFO] Executing action 323/591: Tap on image: env[device-back-img]
[[15:18:25]] [SUCCESS] Screenshot refreshed
[[15:18:25]] [INFO] Refreshing screenshot...
[[15:18:25]] [INFO] napKDohf3Z=pass
[[15:18:21]] [SUCCESS] Screenshot refreshed successfully
[[15:18:21]] [SUCCESS] Screenshot refreshed successfully
[[15:18:21]] [INFO] napKDohf3Z=running
[[15:18:21]] [INFO] Executing action 322/591: Tap on Text: "payment"
[[15:18:20]] [SUCCESS] Screenshot refreshed
[[15:18:20]] [INFO] Refreshing screenshot...
[[15:18:20]] [INFO] ekqt95ZRol=pass
[[15:18:16]] [SUCCESS] Screenshot refreshed successfully
[[15:18:16]] [SUCCESS] Screenshot refreshed successfully
[[15:18:16]] [INFO] ekqt95ZRol=running
[[15:18:16]] [INFO] Executing action 321/591: Tap on image: env[device-back-img]
[[15:18:16]] [SUCCESS] Screenshot refreshed
[[15:18:16]] [INFO] Refreshing screenshot...
[[15:18:16]] [INFO] 20qUCJgpE9=pass
[[15:18:11]] [SUCCESS] Screenshot refreshed successfully
[[15:18:11]] [SUCCESS] Screenshot refreshed successfully
[[15:18:11]] [INFO] 20qUCJgpE9=running
[[15:18:11]] [INFO] Executing action 320/591: Tap on Text: "address"
[[15:18:11]] [SUCCESS] Screenshot refreshed
[[15:18:11]] [INFO] Refreshing screenshot...
[[15:18:11]] [INFO] 6HR2weiXoT=pass
[[15:18:07]] [SUCCESS] Screenshot refreshed successfully
[[15:18:07]] [SUCCESS] Screenshot refreshed successfully
[[15:18:07]] [INFO] 6HR2weiXoT=running
[[15:18:07]] [INFO] Executing action 319/591: Tap on image: env[device-back-img]
[[15:18:06]] [SUCCESS] Screenshot refreshed
[[15:18:06]] [INFO] Refreshing screenshot...
[[15:18:06]] [INFO] 3hOTINBVMf=pass
[[15:18:01]] [SUCCESS] Screenshot refreshed successfully
[[15:18:01]] [SUCCESS] Screenshot refreshed successfully
[[15:18:01]] [INFO] 3hOTINBVMf=running
[[15:18:01]] [INFO] Executing action 318/591: Tap on Text: "details"
[[15:18:00]] [SUCCESS] Screenshot refreshed
[[15:18:00]] [INFO] Refreshing screenshot...
[[15:18:00]] [INFO] yJi0WxnERj=pass
[[15:17:56]] [SUCCESS] Screenshot refreshed successfully
[[15:17:56]] [SUCCESS] Screenshot refreshed successfully
[[15:17:56]] [INFO] yJi0WxnERj=running
[[15:17:56]] [INFO] Executing action 317/591: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[15:17:56]] [SUCCESS] Screenshot refreshed
[[15:17:56]] [INFO] Refreshing screenshot...
[[15:17:56]] [INFO] PbfHAtFQPP=pass
[[15:17:52]] [SUCCESS] Screenshot refreshed successfully
[[15:17:52]] [SUCCESS] Screenshot refreshed successfully
[[15:17:51]] [INFO] PbfHAtFQPP=running
[[15:17:51]] [INFO] Executing action 316/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:17:51]] [SUCCESS] Screenshot refreshed
[[15:17:51]] [INFO] Refreshing screenshot...
[[15:17:51]] [INFO] 6qZnk86hGg=pass
[[15:17:46]] [SUCCESS] Screenshot refreshed successfully
[[15:17:46]] [SUCCESS] Screenshot refreshed successfully
[[15:17:46]] [INFO] 6qZnk86hGg=running
[[15:17:46]] [INFO] Executing action 315/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[15:17:46]] [SUCCESS] Screenshot refreshed
[[15:17:46]] [INFO] Refreshing screenshot...
[[15:17:46]] [INFO] FAvQgIuHc1=pass
[[15:17:41]] [SUCCESS] Screenshot refreshed successfully
[[15:17:41]] [SUCCESS] Screenshot refreshed successfully
[[15:17:41]] [INFO] FAvQgIuHc1=running
[[15:17:41]] [INFO] Executing action 314/591: Tap on Text: "Return"
[[15:17:40]] [SUCCESS] Screenshot refreshed
[[15:17:40]] [INFO] Refreshing screenshot...
[[15:17:40]] [INFO] vmc01sHkbr=pass
[[15:17:34]] [SUCCESS] Screenshot refreshed successfully
[[15:17:34]] [SUCCESS] Screenshot refreshed successfully
[[15:17:33]] [INFO] vmc01sHkbr=running
[[15:17:33]] [INFO] Executing action 313/591: Wait for 5 ms
[[15:17:33]] [SUCCESS] Screenshot refreshed
[[15:17:33]] [INFO] Refreshing screenshot...
[[15:17:33]] [INFO] zeu0wd1vcF=pass
[[15:17:20]] [SUCCESS] Screenshot refreshed successfully
[[15:17:20]] [SUCCESS] Screenshot refreshed successfully
[[15:17:20]] [INFO] zeu0wd1vcF=running
[[15:17:20]] [INFO] Executing action 312/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:17:19]] [SUCCESS] Screenshot refreshed
[[15:17:19]] [INFO] Refreshing screenshot...
[[15:17:19]] [INFO] OwWeZes4aT=pass
[[15:17:16]] [SUCCESS] Screenshot refreshed successfully
[[15:17:16]] [SUCCESS] Screenshot refreshed successfully
[[15:17:15]] [INFO] OwWeZes4aT=running
[[15:17:15]] [INFO] Executing action 311/591: Tap on image: env[device-back-img]
[[15:17:15]] [SUCCESS] Screenshot refreshed
[[15:17:15]] [INFO] Refreshing screenshot...
[[15:17:15]] [INFO] aAaTtUE92h=pass
[[15:17:12]] [SUCCESS] Screenshot refreshed successfully
[[15:17:12]] [SUCCESS] Screenshot refreshed successfully
[[15:17:12]] [INFO] aAaTtUE92h=running
[[15:17:12]] [INFO] Executing action 310/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[15:17:11]] [SUCCESS] Screenshot refreshed
[[15:17:11]] [INFO] Refreshing screenshot...
[[15:17:11]] [INFO] 9iOZGMqAZK=pass
[[15:17:07]] [SUCCESS] Screenshot refreshed successfully
[[15:17:07]] [SUCCESS] Screenshot refreshed successfully
[[15:17:07]] [INFO] 9iOZGMqAZK=running
[[15:17:07]] [INFO] Executing action 309/591: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[15:17:07]] [SUCCESS] Screenshot refreshed
[[15:17:07]] [INFO] Refreshing screenshot...
[[15:17:07]] [INFO] mRTYzOFRRw=pass
[[15:17:04]] [SUCCESS] Screenshot refreshed successfully
[[15:17:04]] [SUCCESS] Screenshot refreshed successfully
[[15:17:03]] [INFO] mRTYzOFRRw=running
[[15:17:03]] [INFO] Executing action 308/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[15:17:03]] [SUCCESS] Screenshot refreshed
[[15:17:03]] [INFO] Refreshing screenshot...
[[15:17:03]] [INFO] 7g6MFJSGIO=pass
[[15:16:58]] [SUCCESS] Screenshot refreshed successfully
[[15:16:58]] [SUCCESS] Screenshot refreshed successfully
[[15:16:58]] [INFO] 7g6MFJSGIO=running
[[15:16:58]] [INFO] Executing action 307/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[15:16:58]] [SUCCESS] Screenshot refreshed
[[15:16:58]] [INFO] Refreshing screenshot...
[[15:16:58]] [INFO] zNwyPagPE1=pass
[[15:16:51]] [SUCCESS] Screenshot refreshed successfully
[[15:16:51]] [SUCCESS] Screenshot refreshed successfully
[[15:16:51]] [INFO] zNwyPagPE1=running
[[15:16:51]] [INFO] Executing action 306/591: Wait for 5 ms
[[15:16:51]] [SUCCESS] Screenshot refreshed
[[15:16:51]] [INFO] Refreshing screenshot...
[[15:16:51]] [INFO] qXsL3wzg6J=pass
[[15:16:47]] [SUCCESS] Screenshot refreshed successfully
[[15:16:47]] [SUCCESS] Screenshot refreshed successfully
[[15:16:46]] [INFO] qXsL3wzg6J=running
[[15:16:46]] [INFO] Executing action 305/591: Tap on image: env[device-back-img]
[[15:16:46]] [SUCCESS] Screenshot refreshed
[[15:16:46]] [INFO] Refreshing screenshot...
[[15:16:46]] [INFO] YuuQe2KupX=pass
[[15:16:39]] [SUCCESS] Screenshot refreshed successfully
[[15:16:39]] [SUCCESS] Screenshot refreshed successfully
[[15:16:39]] [INFO] YuuQe2KupX=running
[[15:16:39]] [INFO] Executing action 304/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[15:16:39]] [SUCCESS] Screenshot refreshed
[[15:16:39]] [INFO] Refreshing screenshot...
[[15:16:39]] [INFO] g0PE7Mofye=pass
[[15:16:31]] [SUCCESS] Screenshot refreshed successfully
[[15:16:31]] [SUCCESS] Screenshot refreshed successfully
[[15:16:31]] [INFO] g0PE7Mofye=running
[[15:16:31]] [INFO] Executing action 303/591: Tap on element with accessibility_id: Print order details
[[15:16:30]] [SUCCESS] Screenshot refreshed
[[15:16:30]] [INFO] Refreshing screenshot...
[[15:16:30]] [INFO] GgQaBLWYkb=pass
[[15:16:26]] [SUCCESS] Screenshot refreshed successfully
[[15:16:26]] [SUCCESS] Screenshot refreshed successfully
[[15:16:26]] [INFO] GgQaBLWYkb=running
[[15:16:26]] [INFO] Executing action 302/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[15:16:26]] [SUCCESS] Screenshot refreshed
[[15:16:26]] [INFO] Refreshing screenshot...
[[15:16:26]] [INFO] f3OrHHzTFN=pass
[[15:16:09]] [SUCCESS] Screenshot refreshed successfully
[[15:16:09]] [SUCCESS] Screenshot refreshed successfully
[[15:16:09]] [INFO] f3OrHHzTFN=running
[[15:16:09]] [INFO] Executing action 301/591: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[15:16:09]] [SUCCESS] Screenshot refreshed
[[15:16:09]] [INFO] Refreshing screenshot...
[[15:16:09]] [INFO] 7g6MFJSGIO=pass
[[15:16:05]] [SUCCESS] Screenshot refreshed successfully
[[15:16:05]] [SUCCESS] Screenshot refreshed successfully
[[15:16:05]] [INFO] 7g6MFJSGIO=running
[[15:16:05]] [INFO] Executing action 300/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[15:16:04]] [SUCCESS] Screenshot refreshed
[[15:16:04]] [INFO] Refreshing screenshot...
[[15:16:04]] [INFO] Z6g3sGuHTp=pass
[[15:15:57]] [INFO] Z6g3sGuHTp=running
[[15:15:57]] [INFO] Executing action 299/591: Wait for 5 ms
[[15:15:57]] [SUCCESS] Screenshot refreshed successfully
[[15:15:57]] [SUCCESS] Screenshot refreshed successfully
[[15:15:57]] [SUCCESS] Screenshot refreshed
[[15:15:57]] [INFO] Refreshing screenshot...
[[15:15:57]] [INFO] pFlYwTS53v=pass
[[15:15:52]] [SUCCESS] Screenshot refreshed successfully
[[15:15:52]] [SUCCESS] Screenshot refreshed successfully
[[15:15:52]] [INFO] pFlYwTS53v=running
[[15:15:52]] [INFO] Executing action 298/591: Tap on Text: "receipts"
[[15:15:52]] [SUCCESS] Screenshot refreshed
[[15:15:52]] [INFO] Refreshing screenshot...
[[15:15:52]] [INFO] V59u3l1wkM=pass
[[15:15:48]] [SUCCESS] Screenshot refreshed successfully
[[15:15:48]] [SUCCESS] Screenshot refreshed successfully
[[15:15:48]] [INFO] V59u3l1wkM=running
[[15:15:48]] [INFO] Executing action 297/591: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[15:15:48]] [SUCCESS] Screenshot refreshed
[[15:15:48]] [INFO] Refreshing screenshot...
[[15:15:48]] [INFO] sl3Wk1gK8X=pass
[[15:15:46]] [SUCCESS] Screenshot refreshed successfully
[[15:15:46]] [SUCCESS] Screenshot refreshed successfully
[[15:15:43]] [INFO] sl3Wk1gK8X=running
[[15:15:43]] [INFO] Executing action 296/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:15:43]] [SUCCESS] Screenshot refreshed
[[15:15:43]] [INFO] Refreshing screenshot...
[[15:15:43]] [SUCCESS] Screenshot refreshed
[[15:15:43]] [INFO] Refreshing screenshot...
[[15:15:38]] [SUCCESS] Screenshot refreshed successfully
[[15:15:38]] [SUCCESS] Screenshot refreshed successfully
[[15:15:37]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:15:37]] [SUCCESS] Screenshot refreshed
[[15:15:37]] [INFO] Refreshing screenshot...
[[15:15:31]] [SUCCESS] Screenshot refreshed successfully
[[15:15:31]] [SUCCESS] Screenshot refreshed successfully
[[15:15:31]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:15:30]] [SUCCESS] Screenshot refreshed
[[15:15:30]] [INFO] Refreshing screenshot...
[[15:15:26]] [SUCCESS] Screenshot refreshed successfully
[[15:15:26]] [SUCCESS] Screenshot refreshed successfully
[[15:15:26]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[15:15:25]] [SUCCESS] Screenshot refreshed
[[15:15:25]] [INFO] Refreshing screenshot...
[[15:15:21]] [SUCCESS] Screenshot refreshed successfully
[[15:15:21]] [SUCCESS] Screenshot refreshed successfully
[[15:15:21]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:15:20]] [SUCCESS] Screenshot refreshed
[[15:15:20]] [INFO] Refreshing screenshot...
[[15:15:14]] [SUCCESS] Screenshot refreshed successfully
[[15:15:14]] [SUCCESS] Screenshot refreshed successfully
[[15:15:14]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:15:14]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:15:14]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:15:14]] [INFO] vjK6GqOF3r=running
[[15:15:14]] [INFO] Executing action 295/591: Execute Test Case: Kmart-Signin (8 steps)
[[15:15:14]] [SUCCESS] Screenshot refreshed
[[15:15:14]] [INFO] Refreshing screenshot...
[[15:15:14]] [INFO] ly2oT3zqmf=pass
[[15:15:11]] [SUCCESS] Screenshot refreshed successfully
[[15:15:11]] [SUCCESS] Screenshot refreshed successfully
[[15:15:11]] [INFO] ly2oT3zqmf=running
[[15:15:11]] [INFO] Executing action 294/591: iOS Function: alert_accept
[[15:15:10]] [SUCCESS] Screenshot refreshed
[[15:15:10]] [INFO] Refreshing screenshot...
[[15:15:10]] [INFO] xAPeBnVHrT=pass
[[15:15:04]] [SUCCESS] Screenshot refreshed successfully
[[15:15:04]] [SUCCESS] Screenshot refreshed successfully
[[15:15:04]] [INFO] xAPeBnVHrT=running
[[15:15:04]] [INFO] Executing action 293/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:15:03]] [SUCCESS] Screenshot refreshed
[[15:15:03]] [INFO] Refreshing screenshot...
[[15:15:03]] [INFO] u6bRYZZFAv=pass
[[15:14:56]] [SUCCESS] Screenshot refreshed successfully
[[15:14:56]] [SUCCESS] Screenshot refreshed successfully
[[15:14:55]] [INFO] u6bRYZZFAv=running
[[15:14:55]] [INFO] Executing action 292/591: Wait for 5 ms
[[15:14:55]] [SUCCESS] Screenshot refreshed
[[15:14:55]] [INFO] Refreshing screenshot...
[[15:14:55]] [INFO] pjFNt3w5Fr=pass
[[15:14:42]] [SUCCESS] Screenshot refreshed successfully
[[15:14:42]] [SUCCESS] Screenshot refreshed successfully
[[15:14:40]] [INFO] pjFNt3w5Fr=running
[[15:14:40]] [INFO] Executing action 291/591: Restart app: env[appid]
[[15:14:40]] [SUCCESS] Screenshot refreshed
[[15:14:40]] [INFO] Refreshing screenshot...
[[15:14:40]] [SUCCESS] Screenshot refreshed
[[15:14:40]] [INFO] Refreshing screenshot...
[[15:14:37]] [SUCCESS] Screenshot refreshed successfully
[[15:14:37]] [SUCCESS] Screenshot refreshed successfully
[[15:14:37]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:14:36]] [SUCCESS] Screenshot refreshed
[[15:14:36]] [INFO] Refreshing screenshot...
[[15:14:24]] [SUCCESS] Screenshot refreshed successfully
[[15:14:24]] [SUCCESS] Screenshot refreshed successfully
[[15:14:24]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:14:24]] [SUCCESS] Screenshot refreshed
[[15:14:24]] [INFO] Refreshing screenshot...
[[15:14:20]] [SUCCESS] Screenshot refreshed successfully
[[15:14:20]] [SUCCESS] Screenshot refreshed successfully
[[15:14:20]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:14:19]] [SUCCESS] Screenshot refreshed
[[15:14:19]] [INFO] Refreshing screenshot...
[[15:14:16]] [SUCCESS] Screenshot refreshed successfully
[[15:14:16]] [SUCCESS] Screenshot refreshed successfully
[[15:14:15]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:14:15]] [SUCCESS] Screenshot refreshed
[[15:14:15]] [INFO] Refreshing screenshot...
[[15:14:08]] [SUCCESS] Screenshot refreshed successfully
[[15:14:08]] [SUCCESS] Screenshot refreshed successfully
[[15:14:08]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:14:07]] [SUCCESS] Screenshot refreshed
[[15:14:07]] [INFO] Refreshing screenshot...
[[15:14:01]] [SUCCESS] Screenshot refreshed successfully
[[15:14:01]] [SUCCESS] Screenshot refreshed successfully
[[15:14:01]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:14:01]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:14:01]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:14:00]] [INFO] PGvsG6rpU4=running
[[15:14:00]] [INFO] Executing action 290/591: cleanupSteps action
[[15:14:00]] [SUCCESS] Screenshot refreshed
[[15:14:00]] [INFO] Refreshing screenshot...
[[15:14:00]] [INFO] LzGkAcsQyE=pass
[[15:13:57]] [SUCCESS] Screenshot refreshed successfully
[[15:13:57]] [SUCCESS] Screenshot refreshed successfully
[[15:13:57]] [INFO] LzGkAcsQyE=running
[[15:13:57]] [INFO] Executing action 289/591: Terminate app: env[appid]
[[15:13:56]] [SUCCESS] Screenshot refreshed
[[15:13:56]] [INFO] Refreshing screenshot...
[[15:13:56]] [INFO] Bdhe5AoUlM=pass
[[15:13:51]] [SUCCESS] Screenshot refreshed successfully
[[15:13:51]] [SUCCESS] Screenshot refreshed successfully
[[15:13:51]] [INFO] Bdhe5AoUlM=running
[[15:13:51]] [INFO] Executing action 288/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:13:51]] [SUCCESS] Screenshot refreshed
[[15:13:51]] [INFO] Refreshing screenshot...
[[15:13:51]] [INFO] FciJcOsMsB=pass
[[15:13:44]] [SUCCESS] Screenshot refreshed successfully
[[15:13:44]] [SUCCESS] Screenshot refreshed successfully
[[15:13:44]] [INFO] FciJcOsMsB=running
[[15:13:44]] [INFO] Executing action 287/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:13:43]] [SUCCESS] Screenshot refreshed
[[15:13:43]] [INFO] Refreshing screenshot...
[[15:13:43]] [INFO] FARWZvOj0x=pass
[[15:13:40]] [SUCCESS] Screenshot refreshed successfully
[[15:13:40]] [SUCCESS] Screenshot refreshed successfully
[[15:13:39]] [INFO] FARWZvOj0x=running
[[15:13:39]] [INFO] Executing action 286/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:13:39]] [SUCCESS] Screenshot refreshed
[[15:13:39]] [INFO] Refreshing screenshot...
[[15:13:39]] [INFO] bZCkx4U9Gk=pass
[[15:13:34]] [SUCCESS] Screenshot refreshed successfully
[[15:13:34]] [SUCCESS] Screenshot refreshed successfully
[[15:13:33]] [INFO] bZCkx4U9Gk=running
[[15:13:33]] [INFO] Executing action 285/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:13:33]] [SUCCESS] Screenshot refreshed
[[15:13:33]] [INFO] Refreshing screenshot...
[[15:13:33]] [INFO] vwFwkK6ydQ=pass
[[15:13:28]] [SUCCESS] Screenshot refreshed successfully
[[15:13:28]] [SUCCESS] Screenshot refreshed successfully
[[15:13:28]] [INFO] vwFwkK6ydQ=running
[[15:13:28]] [INFO] Executing action 284/591: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[15:13:28]] [SUCCESS] Screenshot refreshed
[[15:13:28]] [INFO] Refreshing screenshot...
[[15:13:28]] [INFO] xLGm9FefWE=pass
[[15:13:23]] [SUCCESS] Screenshot refreshed successfully
[[15:13:23]] [SUCCESS] Screenshot refreshed successfully
[[15:13:23]] [INFO] xLGm9FefWE=running
[[15:13:23]] [INFO] Executing action 283/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[15:13:23]] [SUCCESS] Screenshot refreshed
[[15:13:23]] [INFO] Refreshing screenshot...
[[15:13:23]] [INFO] UtVRXwa86e=pass
[[15:13:16]] [SUCCESS] Screenshot refreshed successfully
[[15:13:16]] [SUCCESS] Screenshot refreshed successfully
[[15:13:16]] [INFO] UtVRXwa86e=running
[[15:13:16]] [INFO] Executing action 282/591: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[15:13:15]] [SUCCESS] Screenshot refreshed
[[15:13:15]] [INFO] Refreshing screenshot...
[[15:13:15]] [INFO] SDtskxyVpg=pass
[[15:13:12]] [SUCCESS] Screenshot refreshed successfully
[[15:13:12]] [SUCCESS] Screenshot refreshed successfully
[[15:13:12]] [INFO] SDtskxyVpg=running
[[15:13:12]] [INFO] Executing action 281/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:13:11]] [SUCCESS] Screenshot refreshed
[[15:13:11]] [INFO] Refreshing screenshot...
[[15:13:11]] [INFO] 6HhScBaqQp=pass
[[15:13:09]] [SUCCESS] Screenshot refreshed successfully
[[15:13:09]] [SUCCESS] Screenshot refreshed successfully
[[15:13:08]] [INFO] 6HhScBaqQp=running
[[15:13:08]] [INFO] Executing action 280/591: iOS Function: alert_accept
[[15:13:08]] [SUCCESS] Screenshot refreshed
[[15:13:08]] [INFO] Refreshing screenshot...
[[15:13:08]] [INFO] quzlwPw42x=pass
[[15:13:00]] [SUCCESS] Screenshot refreshed successfully
[[15:13:00]] [SUCCESS] Screenshot refreshed successfully
[[15:13:00]] [INFO] quzlwPw42x=running
[[15:13:00]] [INFO] Executing action 279/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:12:59]] [SUCCESS] Screenshot refreshed
[[15:12:59]] [INFO] Refreshing screenshot...
[[15:12:59]] [INFO] jQYHQIvQ8l=pass
[[15:12:55]] [SUCCESS] Screenshot refreshed successfully
[[15:12:55]] [SUCCESS] Screenshot refreshed successfully
[[15:12:55]] [INFO] jQYHQIvQ8l=running
[[15:12:55]] [INFO] Executing action 278/591: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[15:12:54]] [SUCCESS] Screenshot refreshed
[[15:12:54]] [INFO] Refreshing screenshot...
[[15:12:54]] [INFO] ts3qyFxyMf=pass
[[15:12:50]] [SUCCESS] Screenshot refreshed successfully
[[15:12:50]] [SUCCESS] Screenshot refreshed successfully
[[15:12:49]] [INFO] ts3qyFxyMf=running
[[15:12:49]] [INFO] Executing action 277/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:12:49]] [SUCCESS] Screenshot refreshed
[[15:12:49]] [INFO] Refreshing screenshot...
[[15:12:49]] [INFO] FciJcOsMsB=pass
[[15:12:42]] [SUCCESS] Screenshot refreshed successfully
[[15:12:42]] [SUCCESS] Screenshot refreshed successfully
[[15:12:42]] [INFO] FciJcOsMsB=running
[[15:12:42]] [INFO] Executing action 276/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:12:41]] [SUCCESS] Screenshot refreshed
[[15:12:41]] [INFO] Refreshing screenshot...
[[15:12:41]] [INFO] CWkqGp5ndO=pass
[[15:12:38]] [SUCCESS] Screenshot refreshed successfully
[[15:12:38]] [SUCCESS] Screenshot refreshed successfully
[[15:12:37]] [INFO] CWkqGp5ndO=running
[[15:12:37]] [INFO] Executing action 275/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:12:37]] [SUCCESS] Screenshot refreshed
[[15:12:37]] [INFO] Refreshing screenshot...
[[15:12:37]] [INFO] KfMHchi8cx=pass
[[15:12:28]] [INFO] KfMHchi8cx=running
[[15:12:28]] [INFO] Executing action 274/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:12:28]] [SUCCESS] Screenshot refreshed successfully
[[15:12:28]] [SUCCESS] Screenshot refreshed successfully
[[15:12:28]] [SUCCESS] Screenshot refreshed
[[15:12:28]] [INFO] Refreshing screenshot...
[[15:12:28]] [INFO] zsVeGHiIgX=pass
[[15:12:25]] [INFO] zsVeGHiIgX=running
[[15:12:25]] [INFO] Executing action 273/591: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[15:12:25]] [SUCCESS] Screenshot refreshed successfully
[[15:12:25]] [SUCCESS] Screenshot refreshed successfully
[[15:12:24]] [SUCCESS] Screenshot refreshed
[[15:12:24]] [INFO] Refreshing screenshot...
[[15:12:24]] [INFO] 5nsUXQ5L7u=pass
[[15:12:21]] [INFO] 5nsUXQ5L7u=running
[[15:12:21]] [INFO] Executing action 272/591: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[15:12:21]] [SUCCESS] Screenshot refreshed successfully
[[15:12:21]] [SUCCESS] Screenshot refreshed successfully
[[15:12:21]] [SUCCESS] Screenshot refreshed
[[15:12:21]] [INFO] Refreshing screenshot...
[[15:12:21]] [INFO] iSckENpXrN=pass
[[15:12:18]] [INFO] iSckENpXrN=running
[[15:12:18]] [INFO] Executing action 271/591: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[15:12:17]] [SUCCESS] Screenshot refreshed successfully
[[15:12:17]] [SUCCESS] Screenshot refreshed successfully
[[15:12:17]] [SUCCESS] Screenshot refreshed
[[15:12:17]] [INFO] Refreshing screenshot...
[[15:12:17]] [INFO] J7BPGVnRJI=pass
[[15:12:14]] [INFO] J7BPGVnRJI=running
[[15:12:14]] [INFO] Executing action 270/591: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[15:12:14]] [SUCCESS] Screenshot refreshed successfully
[[15:12:14]] [SUCCESS] Screenshot refreshed successfully
[[15:12:13]] [SUCCESS] Screenshot refreshed
[[15:12:13]] [INFO] Refreshing screenshot...
[[15:12:13]] [INFO] 0pwZCYAtOv=pass
[[15:12:10]] [INFO] 0pwZCYAtOv=running
[[15:12:10]] [INFO] Executing action 269/591: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[15:12:10]] [SUCCESS] Screenshot refreshed successfully
[[15:12:10]] [SUCCESS] Screenshot refreshed successfully
[[15:12:10]] [SUCCESS] Screenshot refreshed
[[15:12:10]] [INFO] Refreshing screenshot...
[[15:12:10]] [INFO] soKM0KayFJ=pass
[[15:12:07]] [INFO] soKM0KayFJ=running
[[15:12:07]] [INFO] Executing action 268/591: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[15:12:07]] [SUCCESS] Screenshot refreshed successfully
[[15:12:07]] [SUCCESS] Screenshot refreshed successfully
[[15:12:06]] [SUCCESS] Screenshot refreshed
[[15:12:06]] [INFO] Refreshing screenshot...
[[15:12:06]] [INFO] hnH3ayslCh=pass
[[15:12:03]] [SUCCESS] Screenshot refreshed successfully
[[15:12:03]] [SUCCESS] Screenshot refreshed successfully
[[15:12:03]] [INFO] hnH3ayslCh=running
[[15:12:03]] [INFO] Executing action 267/591: Tap on Text: "Passcode"
[[15:12:02]] [SUCCESS] Screenshot refreshed
[[15:12:02]] [INFO] Refreshing screenshot...
[[15:12:02]] [INFO] CzVeOTdAX9=pass
[[15:11:51]] [SUCCESS] Screenshot refreshed successfully
[[15:11:51]] [SUCCESS] Screenshot refreshed successfully
[[15:11:51]] [INFO] CzVeOTdAX9=running
[[15:11:51]] [INFO] Executing action 266/591: Wait for 10 ms
[[15:11:50]] [SUCCESS] Screenshot refreshed
[[15:11:50]] [INFO] Refreshing screenshot...
[[15:11:50]] [INFO] NL2gtj6qIu=pass
[[15:11:45]] [SUCCESS] Screenshot refreshed successfully
[[15:11:45]] [SUCCESS] Screenshot refreshed successfully
[[15:11:45]] [INFO] NL2gtj6qIu=running
[[15:11:45]] [INFO] Executing action 265/591: Tap on Text: "Apple"
[[15:11:45]] [SUCCESS] Screenshot refreshed
[[15:11:45]] [INFO] Refreshing screenshot...
[[15:11:45]] [INFO] VsSlyhXuVD=pass
[[15:11:40]] [SUCCESS] Screenshot refreshed successfully
[[15:11:40]] [SUCCESS] Screenshot refreshed successfully
[[15:11:40]] [INFO] VsSlyhXuVD=running
[[15:11:40]] [INFO] Executing action 264/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:11:39]] [SUCCESS] Screenshot refreshed
[[15:11:39]] [INFO] Refreshing screenshot...
[[15:11:39]] [INFO] CJ88OgjKXp=pass
[[15:11:36]] [SUCCESS] Screenshot refreshed successfully
[[15:11:36]] [SUCCESS] Screenshot refreshed successfully
[[15:11:35]] [INFO] CJ88OgjKXp=running
[[15:11:35]] [INFO] Executing action 263/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:11:35]] [SUCCESS] Screenshot refreshed
[[15:11:35]] [INFO] Refreshing screenshot...
[[15:11:35]] [INFO] AYiwFSLTBD=pass
[[15:11:32]] [SUCCESS] Screenshot refreshed successfully
[[15:11:32]] [SUCCESS] Screenshot refreshed successfully
[[15:11:31]] [INFO] AYiwFSLTBD=running
[[15:11:31]] [INFO] Executing action 262/591: iOS Function: alert_accept
[[15:11:31]] [SUCCESS] Screenshot refreshed
[[15:11:31]] [INFO] Refreshing screenshot...
[[15:11:31]] [INFO] HJzOYZNnGr=pass
[[15:11:25]] [SUCCESS] Screenshot refreshed successfully
[[15:11:25]] [SUCCESS] Screenshot refreshed successfully
[[15:11:24]] [INFO] HJzOYZNnGr=running
[[15:11:24]] [INFO] Executing action 261/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:11:24]] [SUCCESS] Screenshot refreshed
[[15:11:24]] [INFO] Refreshing screenshot...
[[15:11:24]] [INFO] taf19mtrUT=pass
[[15:11:20]] [SUCCESS] Screenshot refreshed successfully
[[15:11:20]] [SUCCESS] Screenshot refreshed successfully
[[15:11:19]] [INFO] taf19mtrUT=running
[[15:11:19]] [INFO] Executing action 260/591: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[15:11:19]] [SUCCESS] Screenshot refreshed
[[15:11:19]] [INFO] Refreshing screenshot...
[[15:11:19]] [INFO] oiPcknTonJ=pass
[[15:11:14]] [SUCCESS] Screenshot refreshed successfully
[[15:11:14]] [SUCCESS] Screenshot refreshed successfully
[[15:11:14]] [INFO] oiPcknTonJ=running
[[15:11:14]] [INFO] Executing action 259/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:11:14]] [SUCCESS] Screenshot refreshed
[[15:11:14]] [INFO] Refreshing screenshot...
[[15:11:14]] [INFO] FciJcOsMsB=pass
[[15:11:08]] [SUCCESS] Screenshot refreshed successfully
[[15:11:08]] [SUCCESS] Screenshot refreshed successfully
[[15:11:08]] [INFO] FciJcOsMsB=running
[[15:11:08]] [INFO] Executing action 258/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:11:07]] [SUCCESS] Screenshot refreshed
[[15:11:07]] [INFO] Refreshing screenshot...
[[15:11:07]] [INFO] 2qOXZcEmK8=pass
[[15:11:05]] [SUCCESS] Screenshot refreshed successfully
[[15:11:05]] [SUCCESS] Screenshot refreshed successfully
[[15:11:02]] [INFO] 2qOXZcEmK8=running
[[15:11:02]] [INFO] Executing action 257/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:11:02]] [SUCCESS] Screenshot refreshed
[[15:11:02]] [INFO] Refreshing screenshot...
[[15:11:02]] [INFO] M6HdLxu76S=pass
[[15:10:57]] [SUCCESS] Screenshot refreshed successfully
[[15:10:57]] [SUCCESS] Screenshot refreshed successfully
[[15:10:57]] [INFO] M6HdLxu76S=running
[[15:10:57]] [INFO] Executing action 256/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:10:56]] [SUCCESS] Screenshot refreshed
[[15:10:56]] [INFO] Refreshing screenshot...
[[15:10:56]] [INFO] pCPTAtSZbf=pass
[[15:10:51]] [SUCCESS] Screenshot refreshed successfully
[[15:10:51]] [SUCCESS] Screenshot refreshed successfully
[[15:10:51]] [INFO] pCPTAtSZbf=running
[[15:10:51]] [INFO] Executing action 255/591: iOS Function: text - Text: "Wonderbaby@5"
[[15:10:51]] [SUCCESS] Screenshot refreshed
[[15:10:51]] [INFO] Refreshing screenshot...
[[15:10:51]] [INFO] DaVBARRwft=pass
[[15:10:46]] [SUCCESS] Screenshot refreshed successfully
[[15:10:46]] [SUCCESS] Screenshot refreshed successfully
[[15:10:46]] [INFO] DaVBARRwft=running
[[15:10:46]] [INFO] Executing action 254/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[15:10:46]] [SUCCESS] Screenshot refreshed
[[15:10:46]] [INFO] Refreshing screenshot...
[[15:10:46]] [INFO] e1RoZWCZJb=pass
[[15:10:41]] [SUCCESS] Screenshot refreshed successfully
[[15:10:41]] [SUCCESS] Screenshot refreshed successfully
[[15:10:40]] [INFO] e1RoZWCZJb=running
[[15:10:40]] [INFO] Executing action 253/591: iOS Function: text - Text: "<EMAIL>"
[[15:10:40]] [SUCCESS] Screenshot refreshed
[[15:10:40]] [INFO] Refreshing screenshot...
[[15:10:40]] [INFO] y8ZMTkG38M=pass
[[15:10:36]] [SUCCESS] Screenshot refreshed successfully
[[15:10:36]] [SUCCESS] Screenshot refreshed successfully
[[15:10:35]] [INFO] y8ZMTkG38M=running
[[15:10:35]] [INFO] Executing action 252/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[15:10:35]] [SUCCESS] Screenshot refreshed
[[15:10:35]] [INFO] Refreshing screenshot...
[[15:10:35]] [INFO] UUhQjmzfO2=pass
[[15:10:30]] [SUCCESS] Screenshot refreshed successfully
[[15:10:30]] [SUCCESS] Screenshot refreshed successfully
[[15:10:30]] [INFO] UUhQjmzfO2=running
[[15:10:30]] [INFO] Executing action 251/591: Tap on Text: "OnePass"
[[15:10:29]] [SUCCESS] Screenshot refreshed
[[15:10:29]] [INFO] Refreshing screenshot...
[[15:10:29]] [INFO] FciJcOsMsB=pass
[[15:10:25]] [SUCCESS] Screenshot refreshed successfully
[[15:10:25]] [SUCCESS] Screenshot refreshed successfully
[[15:10:25]] [INFO] FciJcOsMsB=running
[[15:10:25]] [INFO] Executing action 250/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:10:24]] [SUCCESS] Screenshot refreshed
[[15:10:24]] [INFO] Refreshing screenshot...
[[15:10:24]] [INFO] NCyuT8W5Xz=pass
[[15:10:20]] [SUCCESS] Screenshot refreshed successfully
[[15:10:20]] [SUCCESS] Screenshot refreshed successfully
[[15:10:20]] [INFO] NCyuT8W5Xz=running
[[15:10:20]] [INFO] Executing action 249/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:10:20]] [SUCCESS] Screenshot refreshed
[[15:10:20]] [INFO] Refreshing screenshot...
[[15:10:20]] [INFO] 2kwu2VBmuZ=pass
[[15:10:17]] [SUCCESS] Screenshot refreshed successfully
[[15:10:17]] [SUCCESS] Screenshot refreshed successfully
[[15:10:17]] [INFO] 2kwu2VBmuZ=running
[[15:10:17]] [INFO] Executing action 248/591: iOS Function: alert_accept
[[15:10:16]] [SUCCESS] Screenshot refreshed
[[15:10:16]] [INFO] Refreshing screenshot...
[[15:10:16]] [INFO] cJDpd7aK3d=pass
[[15:10:10]] [SUCCESS] Screenshot refreshed successfully
[[15:10:10]] [SUCCESS] Screenshot refreshed successfully
[[15:10:10]] [INFO] cJDpd7aK3d=running
[[15:10:10]] [INFO] Executing action 247/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:10:09]] [SUCCESS] Screenshot refreshed
[[15:10:09]] [INFO] Refreshing screenshot...
[[15:10:09]] [INFO] FlEukNkjlS=pass
[[15:10:05]] [SUCCESS] Screenshot refreshed successfully
[[15:10:05]] [SUCCESS] Screenshot refreshed successfully
[[15:10:05]] [INFO] FlEukNkjlS=running
[[15:10:05]] [INFO] Executing action 246/591: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[15:10:04]] [SUCCESS] Screenshot refreshed
[[15:10:04]] [INFO] Refreshing screenshot...
[[15:10:04]] [INFO] LlRfimKPrn=pass
[[15:09:59]] [SUCCESS] Screenshot refreshed successfully
[[15:09:59]] [SUCCESS] Screenshot refreshed successfully
[[15:09:59]] [INFO] LlRfimKPrn=running
[[15:09:59]] [INFO] Executing action 245/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:09:58]] [SUCCESS] Screenshot refreshed
[[15:09:58]] [INFO] Refreshing screenshot...
[[15:09:58]] [INFO] FciJcOsMsB=pass
[[15:09:51]] [SUCCESS] Screenshot refreshed successfully
[[15:09:51]] [SUCCESS] Screenshot refreshed successfully
[[15:09:51]] [INFO] FciJcOsMsB=running
[[15:09:51]] [INFO] Executing action 244/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:09:51]] [SUCCESS] Screenshot refreshed
[[15:09:51]] [INFO] Refreshing screenshot...
[[15:09:51]] [INFO] 08NzsvhQXK=pass
[[15:09:47]] [SUCCESS] Screenshot refreshed successfully
[[15:09:47]] [SUCCESS] Screenshot refreshed successfully
[[15:09:47]] [INFO] 08NzsvhQXK=running
[[15:09:47]] [INFO] Executing action 243/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:09:46]] [SUCCESS] Screenshot refreshed
[[15:09:46]] [INFO] Refreshing screenshot...
[[15:09:46]] [INFO] IsGWxLFpIn=pass
[[15:09:43]] [SUCCESS] Screenshot refreshed successfully
[[15:09:43]] [SUCCESS] Screenshot refreshed successfully
[[15:09:42]] [INFO] IsGWxLFpIn=running
[[15:09:42]] [INFO] Executing action 242/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:09:42]] [SUCCESS] Screenshot refreshed
[[15:09:42]] [INFO] Refreshing screenshot...
[[15:09:42]] [INFO] dyECdbRifp=pass
[[15:09:37]] [SUCCESS] Screenshot refreshed successfully
[[15:09:37]] [SUCCESS] Screenshot refreshed successfully
[[15:09:37]] [INFO] dyECdbRifp=running
[[15:09:37]] [INFO] Executing action 241/591: iOS Function: text - Text: "Wonderbaby@5"
[[15:09:36]] [SUCCESS] Screenshot refreshed
[[15:09:36]] [INFO] Refreshing screenshot...
[[15:09:36]] [INFO] I5bRbYY1hD=pass
[[15:09:30]] [SUCCESS] Screenshot refreshed successfully
[[15:09:30]] [SUCCESS] Screenshot refreshed successfully
[[15:09:30]] [INFO] I5bRbYY1hD=running
[[15:09:30]] [INFO] Executing action 240/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:09:30]] [SUCCESS] Screenshot refreshed
[[15:09:30]] [INFO] Refreshing screenshot...
[[15:09:30]] [INFO] WMl5g82CCq=pass
[[15:09:25]] [SUCCESS] Screenshot refreshed successfully
[[15:09:25]] [SUCCESS] Screenshot refreshed successfully
[[15:09:25]] [INFO] WMl5g82CCq=running
[[15:09:25]] [INFO] Executing action 239/591: iOS Function: text - Text: "<EMAIL>"
[[15:09:24]] [SUCCESS] Screenshot refreshed
[[15:09:24]] [INFO] Refreshing screenshot...
[[15:09:24]] [INFO] 8OsQmoVYqW=pass
[[15:09:20]] [SUCCESS] Screenshot refreshed successfully
[[15:09:20]] [SUCCESS] Screenshot refreshed successfully
[[15:09:20]] [INFO] 8OsQmoVYqW=running
[[15:09:20]] [INFO] Executing action 238/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:09:19]] [SUCCESS] Screenshot refreshed
[[15:09:19]] [INFO] Refreshing screenshot...
[[15:09:19]] [INFO] ImienLpJEN=pass
[[15:09:15]] [SUCCESS] Screenshot refreshed successfully
[[15:09:15]] [SUCCESS] Screenshot refreshed successfully
[[15:09:15]] [INFO] ImienLpJEN=running
[[15:09:15]] [INFO] Executing action 237/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:09:15]] [SUCCESS] Screenshot refreshed
[[15:09:15]] [INFO] Refreshing screenshot...
[[15:09:15]] [INFO] q4hPXCBtx4=pass
[[15:09:13]] [SUCCESS] Screenshot refreshed successfully
[[15:09:13]] [SUCCESS] Screenshot refreshed successfully
[[15:09:12]] [INFO] q4hPXCBtx4=running
[[15:09:12]] [INFO] Executing action 236/591: iOS Function: alert_accept
[[15:09:11]] [SUCCESS] Screenshot refreshed
[[15:09:11]] [INFO] Refreshing screenshot...
[[15:09:11]] [INFO] 2cTZvK1psn=pass
[[15:09:05]] [SUCCESS] Screenshot refreshed successfully
[[15:09:05]] [SUCCESS] Screenshot refreshed successfully
[[15:09:04]] [INFO] 2cTZvK1psn=running
[[15:09:04]] [INFO] Executing action 235/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:09:04]] [SUCCESS] Screenshot refreshed
[[15:09:04]] [INFO] Refreshing screenshot...
[[15:09:04]] [INFO] Vxt7QOYeDD=pass
[[15:08:50]] [SUCCESS] Screenshot refreshed successfully
[[15:08:50]] [SUCCESS] Screenshot refreshed successfully
[[15:08:49]] [INFO] Vxt7QOYeDD=running
[[15:08:49]] [INFO] Executing action 234/591: Restart app: env[appid]
[[15:08:49]] [SUCCESS] Screenshot refreshed
[[15:08:49]] [INFO] Refreshing screenshot...
[[15:08:49]] [SUCCESS] Screenshot refreshed
[[15:08:49]] [INFO] Refreshing screenshot...
[[15:08:46]] [SUCCESS] Screenshot refreshed successfully
[[15:08:46]] [SUCCESS] Screenshot refreshed successfully
[[15:08:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:08:45]] [SUCCESS] Screenshot refreshed
[[15:08:45]] [INFO] Refreshing screenshot...
[[15:08:35]] [SUCCESS] Screenshot refreshed successfully
[[15:08:35]] [SUCCESS] Screenshot refreshed successfully
[[15:08:33]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:08:33]] [SUCCESS] Screenshot refreshed
[[15:08:33]] [INFO] Refreshing screenshot...
[[15:08:28]] [SUCCESS] Screenshot refreshed successfully
[[15:08:28]] [SUCCESS] Screenshot refreshed successfully
[[15:08:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:08:27]] [SUCCESS] Screenshot refreshed
[[15:08:27]] [INFO] Refreshing screenshot...
[[15:08:24]] [SUCCESS] Screenshot refreshed successfully
[[15:08:24]] [SUCCESS] Screenshot refreshed successfully
[[15:08:23]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:08:23]] [SUCCESS] Screenshot refreshed
[[15:08:23]] [INFO] Refreshing screenshot...
[[15:08:16]] [SUCCESS] Screenshot refreshed successfully
[[15:08:16]] [SUCCESS] Screenshot refreshed successfully
[[15:08:16]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:08:15]] [SUCCESS] Screenshot refreshed
[[15:08:15]] [INFO] Refreshing screenshot...
[[15:08:08]] [SUCCESS] Screenshot refreshed successfully
[[15:08:08]] [SUCCESS] Screenshot refreshed successfully
[[15:08:08]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:08:08]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:08:08]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:08:08]] [INFO] DYWpUY7xB6=running
[[15:08:08]] [INFO] Executing action 233/591: cleanupSteps action
[[15:08:07]] [SUCCESS] Screenshot refreshed
[[15:08:07]] [INFO] Refreshing screenshot...
[[15:08:07]] [INFO] OyUowAaBzD=pass
[[15:08:01]] [SUCCESS] Screenshot refreshed successfully
[[15:08:01]] [SUCCESS] Screenshot refreshed successfully
[[15:08:01]] [INFO] OyUowAaBzD=running
[[15:08:01]] [INFO] Executing action 232/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:08:01]] [SUCCESS] Screenshot refreshed
[[15:08:01]] [INFO] Refreshing screenshot...
[[15:08:01]] [INFO] Ob26qqcA0p=pass
[[15:07:54]] [SUCCESS] Screenshot refreshed successfully
[[15:07:54]] [SUCCESS] Screenshot refreshed successfully
[[15:07:54]] [INFO] Ob26qqcA0p=running
[[15:07:54]] [INFO] Executing action 231/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:07:53]] [SUCCESS] Screenshot refreshed
[[15:07:53]] [INFO] Refreshing screenshot...
[[15:07:53]] [INFO] k3mu9Mt7Ec=pass
[[15:07:49]] [SUCCESS] Screenshot refreshed successfully
[[15:07:49]] [SUCCESS] Screenshot refreshed successfully
[[15:07:49]] [INFO] k3mu9Mt7Ec=running
[[15:07:49]] [INFO] Executing action 230/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:07:49]] [SUCCESS] Screenshot refreshed
[[15:07:49]] [INFO] Refreshing screenshot...
[[15:07:49]] [INFO] yhmzeynQyu=pass
[[15:07:44]] [SUCCESS] Screenshot refreshed successfully
[[15:07:44]] [SUCCESS] Screenshot refreshed successfully
[[15:07:44]] [INFO] yhmzeynQyu=running
[[15:07:44]] [INFO] Executing action 229/591: Tap on Text: "Remove"
[[15:07:44]] [SUCCESS] Screenshot refreshed
[[15:07:44]] [INFO] Refreshing screenshot...
[[15:07:44]] [INFO] Q0fomJIDoQ=pass
[[15:07:38]] [SUCCESS] Screenshot refreshed successfully
[[15:07:38]] [SUCCESS] Screenshot refreshed successfully
[[15:07:38]] [INFO] Q0fomJIDoQ=running
[[15:07:38]] [INFO] Executing action 228/591: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[15:07:38]] [SUCCESS] Screenshot refreshed
[[15:07:38]] [INFO] Refreshing screenshot...
[[15:07:38]] [INFO] yhmzeynQyu=pass
[[15:07:33]] [SUCCESS] Screenshot refreshed successfully
[[15:07:33]] [SUCCESS] Screenshot refreshed successfully
[[15:07:32]] [INFO] yhmzeynQyu=running
[[15:07:32]] [INFO] Executing action 227/591: Tap on Text: "Remove"
[[15:07:32]] [SUCCESS] Screenshot refreshed
[[15:07:32]] [INFO] Refreshing screenshot...
[[15:07:32]] [INFO] Q0fomJIDoQ=pass
[[15:07:26]] [SUCCESS] Screenshot refreshed successfully
[[15:07:26]] [SUCCESS] Screenshot refreshed successfully
[[15:07:26]] [INFO] Q0fomJIDoQ=running
[[15:07:26]] [INFO] Executing action 226/591: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[15:07:26]] [SUCCESS] Screenshot refreshed
[[15:07:26]] [INFO] Refreshing screenshot...
[[15:07:26]] [INFO] F1olhgKhUt=pass
[[15:07:22]] [INFO] F1olhgKhUt=running
[[15:07:22]] [INFO] Executing action 225/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[15:07:22]] [SUCCESS] Screenshot refreshed successfully
[[15:07:22]] [SUCCESS] Screenshot refreshed successfully
[[15:07:21]] [SUCCESS] Screenshot refreshed
[[15:07:21]] [INFO] Refreshing screenshot...
[[15:07:21]] [INFO] 8umPSX0vrr=pass
[[15:07:17]] [INFO] 8umPSX0vrr=running
[[15:07:17]] [INFO] Executing action 224/591: Tap on image: banner-close-updated.png
[[15:07:17]] [SUCCESS] Screenshot refreshed successfully
[[15:07:17]] [SUCCESS] Screenshot refreshed successfully
[[15:07:17]] [SUCCESS] Screenshot refreshed
[[15:07:17]] [INFO] Refreshing screenshot...
[[15:07:17]] [INFO] pr9o8Zsm5p=pass
[[15:07:13]] [SUCCESS] Screenshot refreshed successfully
[[15:07:13]] [SUCCESS] Screenshot refreshed successfully
[[15:07:13]] [INFO] pr9o8Zsm5p=running
[[15:07:13]] [INFO] Executing action 223/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[15:07:12]] [SUCCESS] Screenshot refreshed
[[15:07:12]] [INFO] Refreshing screenshot...
[[15:07:12]] [INFO] Qbg9bipTGs=pass
[[15:07:09]] [SUCCESS] Screenshot refreshed successfully
[[15:07:09]] [SUCCESS] Screenshot refreshed successfully
[[15:07:09]] [INFO] Qbg9bipTGs=running
[[15:07:09]] [INFO] Executing action 222/591: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[15:07:08]] [SUCCESS] Screenshot refreshed
[[15:07:08]] [INFO] Refreshing screenshot...
[[15:07:08]] [INFO] Ob26qqcA0p=pass
[[15:07:04]] [SUCCESS] Screenshot refreshed successfully
[[15:07:04]] [SUCCESS] Screenshot refreshed successfully
[[15:07:02]] [INFO] Ob26qqcA0p=running
[[15:07:02]] [INFO] Executing action 221/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:07:02]] [SUCCESS] Screenshot refreshed
[[15:07:02]] [INFO] Refreshing screenshot...
[[15:07:02]] [INFO] ByviEQxEgr=pass
[[15:06:57]] [SUCCESS] Screenshot refreshed successfully
[[15:06:57]] [SUCCESS] Screenshot refreshed successfully
[[15:06:57]] [INFO] ByviEQxEgr=running
[[15:06:57]] [INFO] Executing action 220/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:06:56]] [SUCCESS] Screenshot refreshed
[[15:06:56]] [INFO] Refreshing screenshot...
[[15:06:56]] [INFO] lWIRxRm6HE=pass
[[15:06:52]] [SUCCESS] Screenshot refreshed successfully
[[15:06:52]] [SUCCESS] Screenshot refreshed successfully
[[15:06:51]] [INFO] lWIRxRm6HE=running
[[15:06:51]] [INFO] Executing action 219/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:06:51]] [SUCCESS] Screenshot refreshed
[[15:06:51]] [INFO] Refreshing screenshot...
[[15:06:51]] [INFO] uOt2cFGhGr=pass
[[15:06:46]] [SUCCESS] Screenshot refreshed successfully
[[15:06:46]] [SUCCESS] Screenshot refreshed successfully
[[15:06:46]] [INFO] uOt2cFGhGr=running
[[15:06:46]] [INFO] Executing action 218/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:06:45]] [SUCCESS] Screenshot refreshed
[[15:06:45]] [INFO] Refreshing screenshot...
[[15:06:45]] [INFO] Q0fomJIDoQ=pass
[[15:06:42]] [SUCCESS] Screenshot refreshed successfully
[[15:06:42]] [SUCCESS] Screenshot refreshed successfully
[[15:06:41]] [INFO] Q0fomJIDoQ=running
[[15:06:41]] [INFO] Executing action 217/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[15:06:41]] [SUCCESS] Screenshot refreshed
[[15:06:41]] [INFO] Refreshing screenshot...
[[15:06:41]] [INFO] yhmzeynQyu=pass
[[15:06:37]] [SUCCESS] Screenshot refreshed successfully
[[15:06:37]] [SUCCESS] Screenshot refreshed successfully
[[15:06:36]] [INFO] yhmzeynQyu=running
[[15:06:36]] [INFO] Executing action 216/591: Tap on Text: "Remove"
[[15:06:36]] [SUCCESS] Screenshot refreshed
[[15:06:36]] [INFO] Refreshing screenshot...
[[15:06:36]] [INFO] Q0fomJIDoQ=pass
[[15:06:30]] [SUCCESS] Screenshot refreshed successfully
[[15:06:30]] [SUCCESS] Screenshot refreshed successfully
[[15:06:30]] [INFO] Q0fomJIDoQ=running
[[15:06:30]] [INFO] Executing action 215/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[15:06:29]] [SUCCESS] Screenshot refreshed
[[15:06:29]] [INFO] Refreshing screenshot...
[[15:06:29]] [INFO] y4i304JeJj=pass
[[15:06:25]] [SUCCESS] Screenshot refreshed successfully
[[15:06:25]] [SUCCESS] Screenshot refreshed successfully
[[15:06:24]] [INFO] y4i304JeJj=running
[[15:06:24]] [INFO] Executing action 214/591: Tap on Text: "Move"
[[15:06:24]] [SUCCESS] Screenshot refreshed
[[15:06:24]] [INFO] Refreshing screenshot...
[[15:06:24]] [INFO] Q0fomJIDoQ=pass
[[15:06:20]] [SUCCESS] Screenshot refreshed successfully
[[15:06:20]] [SUCCESS] Screenshot refreshed successfully
[[15:06:20]] [INFO] Q0fomJIDoQ=running
[[15:06:20]] [INFO] Executing action 213/591: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[15:06:19]] [SUCCESS] Screenshot refreshed
[[15:06:19]] [INFO] Refreshing screenshot...
[[15:06:19]] [INFO] Q0fomJIDoQ=pass
[[15:06:16]] [SUCCESS] Screenshot refreshed successfully
[[15:06:16]] [SUCCESS] Screenshot refreshed successfully
[[15:06:16]] [INFO] Q0fomJIDoQ=running
[[15:06:16]] [INFO] Executing action 212/591: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[15:06:15]] [SUCCESS] Screenshot refreshed
[[15:06:15]] [INFO] Refreshing screenshot...
[[15:06:15]] [INFO] F1olhgKhUt=pass
[[15:06:11]] [SUCCESS] Screenshot refreshed successfully
[[15:06:11]] [SUCCESS] Screenshot refreshed successfully
[[15:06:11]] [INFO] F1olhgKhUt=running
[[15:06:11]] [INFO] Executing action 211/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[15:06:10]] [SUCCESS] Screenshot refreshed
[[15:06:10]] [INFO] Refreshing screenshot...
[[15:06:10]] [INFO] WbxRVpWtjw=pass
[[15:06:06]] [SUCCESS] Screenshot refreshed successfully
[[15:06:06]] [SUCCESS] Screenshot refreshed successfully
[[15:06:05]] [INFO] WbxRVpWtjw=running
[[15:06:05]] [INFO] Executing action 210/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[15:06:05]] [SUCCESS] Screenshot refreshed
[[15:06:05]] [INFO] Refreshing screenshot...
[[15:06:05]] [INFO] H3IAmq3r3i=pass
[[15:05:57]] [SUCCESS] Screenshot refreshed successfully
[[15:05:57]] [SUCCESS] Screenshot refreshed successfully
[[15:05:57]] [INFO] H3IAmq3r3i=running
[[15:05:57]] [INFO] Executing action 209/591: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[15:05:56]] [SUCCESS] Screenshot refreshed
[[15:05:56]] [INFO] Refreshing screenshot...
[[15:05:56]] [INFO] uOt2cFGhGr=pass
[[15:05:52]] [SUCCESS] Screenshot refreshed successfully
[[15:05:52]] [SUCCESS] Screenshot refreshed successfully
[[15:05:52]] [INFO] uOt2cFGhGr=running
[[15:05:52]] [INFO] Executing action 208/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:05:51]] [SUCCESS] Screenshot refreshed
[[15:05:51]] [INFO] Refreshing screenshot...
[[15:05:51]] [INFO] eLxHVWKeDQ=pass
[[15:05:47]] [SUCCESS] Screenshot refreshed successfully
[[15:05:47]] [SUCCESS] Screenshot refreshed successfully
[[15:05:47]] [INFO] eLxHVWKeDQ=running
[[15:05:47]] [INFO] Executing action 207/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:05:47]] [SUCCESS] Screenshot refreshed
[[15:05:47]] [INFO] Refreshing screenshot...
[[15:05:47]] [INFO] ghzdMuwrHj=pass
[[15:05:42]] [SUCCESS] Screenshot refreshed successfully
[[15:05:42]] [SUCCESS] Screenshot refreshed successfully
[[15:05:42]] [INFO] ghzdMuwrHj=running
[[15:05:42]] [INFO] Executing action 206/591: iOS Function: text - Text: "P_43386093"
[[15:05:42]] [SUCCESS] Screenshot refreshed
[[15:05:42]] [INFO] Refreshing screenshot...
[[15:05:42]] [INFO] fMzoZJg9I7=pass
[[15:05:36]] [SUCCESS] Screenshot refreshed successfully
[[15:05:36]] [SUCCESS] Screenshot refreshed successfully
[[15:05:36]] [INFO] fMzoZJg9I7=running
[[15:05:36]] [INFO] Executing action 205/591: Tap on Text: "Find"
[[15:05:35]] [SUCCESS] Screenshot refreshed
[[15:05:35]] [INFO] Refreshing screenshot...
[[15:05:35]] [INFO] j1JjmfPRaE=pass
[[15:05:30]] [SUCCESS] Screenshot refreshed successfully
[[15:05:30]] [SUCCESS] Screenshot refreshed successfully
[[15:05:30]] [INFO] j1JjmfPRaE=running
[[15:05:30]] [INFO] Executing action 204/591: Restart app: env[appid]
[[15:05:29]] [SUCCESS] Screenshot refreshed
[[15:05:29]] [INFO] Refreshing screenshot...
[[15:05:29]] [INFO] WbxRVpWtjw=pass
[[15:05:24]] [SUCCESS] Screenshot refreshed successfully
[[15:05:24]] [SUCCESS] Screenshot refreshed successfully
[[15:05:24]] [INFO] WbxRVpWtjw=running
[[15:05:24]] [INFO] Executing action 203/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[15:05:23]] [SUCCESS] Screenshot refreshed
[[15:05:23]] [INFO] Refreshing screenshot...
[[15:05:23]] [INFO] H3IAmq3r3i=pass
[[15:05:16]] [SUCCESS] Screenshot refreshed successfully
[[15:05:16]] [SUCCESS] Screenshot refreshed successfully
[[15:05:15]] [INFO] H3IAmq3r3i=running
[[15:05:15]] [INFO] Executing action 202/591: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[15:05:15]] [SUCCESS] Screenshot refreshed
[[15:05:15]] [INFO] Refreshing screenshot...
[[15:05:15]] [INFO] ITHvSyXXmu=pass
[[15:05:11]] [SUCCESS] Screenshot refreshed successfully
[[15:05:11]] [SUCCESS] Screenshot refreshed successfully
[[15:05:10]] [INFO] ITHvSyXXmu=running
[[15:05:10]] [INFO] Executing action 201/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:05:10]] [SUCCESS] Screenshot refreshed
[[15:05:10]] [INFO] Refreshing screenshot...
[[15:05:10]] [INFO] eLxHVWKeDQ=pass
[[15:04:56]] [SUCCESS] Screenshot refreshed successfully
[[15:04:56]] [SUCCESS] Screenshot refreshed successfully
[[15:04:55]] [INFO] eLxHVWKeDQ=running
[[15:04:55]] [INFO] Executing action 200/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[15:04:55]] [SUCCESS] Screenshot refreshed
[[15:04:55]] [INFO] Refreshing screenshot...
[[15:04:55]] [INFO] WbxRVpWtjw=pass
[[15:04:50]] [SUCCESS] Screenshot refreshed successfully
[[15:04:50]] [SUCCESS] Screenshot refreshed successfully
[[15:04:50]] [INFO] WbxRVpWtjw=running
[[15:04:50]] [INFO] Executing action 199/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[15:04:49]] [SUCCESS] Screenshot refreshed
[[15:04:49]] [INFO] Refreshing screenshot...
[[15:04:49]] [INFO] H3IAmq3r3i=pass
[[15:04:42]] [SUCCESS] Screenshot refreshed successfully
[[15:04:42]] [SUCCESS] Screenshot refreshed successfully
[[15:04:42]] [INFO] H3IAmq3r3i=running
[[15:04:42]] [INFO] Executing action 198/591: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[15:04:41]] [SUCCESS] Screenshot refreshed
[[15:04:41]] [INFO] Refreshing screenshot...
[[15:04:41]] [INFO] ITHvSyXXmu=pass
[[15:04:37]] [SUCCESS] Screenshot refreshed successfully
[[15:04:37]] [SUCCESS] Screenshot refreshed successfully
[[15:04:37]] [INFO] ITHvSyXXmu=running
[[15:04:37]] [INFO] Executing action 197/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[15:04:36]] [SUCCESS] Screenshot refreshed
[[15:04:36]] [INFO] Refreshing screenshot...
[[15:04:36]] [INFO] eLxHVWKeDQ=pass
[[15:04:30]] [SUCCESS] Screenshot refreshed successfully
[[15:04:30]] [SUCCESS] Screenshot refreshed successfully
[[15:04:30]] [INFO] eLxHVWKeDQ=running
[[15:04:30]] [INFO] Executing action 196/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:04:29]] [SUCCESS] Screenshot refreshed
[[15:04:29]] [INFO] Refreshing screenshot...
[[15:04:29]] [INFO] nAB6Q8LAdv=pass
[[15:04:26]] [SUCCESS] Screenshot refreshed successfully
[[15:04:26]] [SUCCESS] Screenshot refreshed successfully
[[15:04:25]] [INFO] nAB6Q8LAdv=running
[[15:04:25]] [INFO] Executing action 195/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:04:25]] [SUCCESS] Screenshot refreshed
[[15:04:25]] [INFO] Refreshing screenshot...
[[15:04:25]] [INFO] sc2KH9bG6H=pass
[[15:04:20]] [SUCCESS] Screenshot refreshed successfully
[[15:04:20]] [SUCCESS] Screenshot refreshed successfully
[[15:04:20]] [INFO] sc2KH9bG6H=running
[[15:04:20]] [INFO] Executing action 194/591: iOS Function: text - Text: "Uno card"
[[15:04:19]] [SUCCESS] Screenshot refreshed
[[15:04:19]] [INFO] Refreshing screenshot...
[[15:04:19]] [INFO] rqLJpAP0mA=pass
[[15:04:14]] [SUCCESS] Screenshot refreshed successfully
[[15:04:14]] [SUCCESS] Screenshot refreshed successfully
[[15:04:13]] [INFO] rqLJpAP0mA=running
[[15:04:13]] [INFO] Executing action 193/591: Tap on Text: "Find"
[[15:04:13]] [SUCCESS] Screenshot refreshed
[[15:04:13]] [INFO] Refreshing screenshot...
[[15:04:13]] [INFO] yiKyF5FJwN=pass
[[15:04:10]] [SUCCESS] Screenshot refreshed successfully
[[15:04:10]] [SUCCESS] Screenshot refreshed successfully
[[15:04:09]] [INFO] yiKyF5FJwN=running
[[15:04:09]] [INFO] Executing action 192/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[15:04:08]] [SUCCESS] Screenshot refreshed
[[15:04:08]] [INFO] Refreshing screenshot...
[[15:04:08]] [INFO] YqMEb5Jr6o=pass
[[15:04:03]] [SUCCESS] Screenshot refreshed successfully
[[15:04:03]] [SUCCESS] Screenshot refreshed successfully
[[15:04:02]] [INFO] YqMEb5Jr6o=running
[[15:04:02]] [INFO] Executing action 191/591: iOS Function: text - Text: "Wonderbaby@6"
[[15:04:02]] [SUCCESS] Screenshot refreshed
[[15:04:02]] [INFO] Refreshing screenshot...
[[15:04:02]] [INFO] T3MmUw30SF=pass
[[15:03:57]] [SUCCESS] Screenshot refreshed successfully
[[15:03:57]] [SUCCESS] Screenshot refreshed successfully
[[15:03:57]] [INFO] T3MmUw30SF=running
[[15:03:57]] [INFO] Executing action 190/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:03:57]] [SUCCESS] Screenshot refreshed
[[15:03:57]] [INFO] Refreshing screenshot...
[[15:03:57]] [INFO] 3FBGGKUMbh=pass
[[15:03:52]] [SUCCESS] Screenshot refreshed successfully
[[15:03:52]] [SUCCESS] Screenshot refreshed successfully
[[15:03:52]] [INFO] 3FBGGKUMbh=running
[[15:03:52]] [INFO] Executing action 189/591: iOS Function: text - Text: "env[uname-op]"
[[15:03:51]] [SUCCESS] Screenshot refreshed
[[15:03:51]] [INFO] Refreshing screenshot...
[[15:03:51]] [INFO] LDkFLWks00=pass
[[15:03:47]] [SUCCESS] Screenshot refreshed successfully
[[15:03:47]] [SUCCESS] Screenshot refreshed successfully
[[15:03:47]] [INFO] LDkFLWks00=running
[[15:03:47]] [INFO] Executing action 188/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:03:46]] [SUCCESS] Screenshot refreshed
[[15:03:46]] [INFO] Refreshing screenshot...
[[15:03:46]] [INFO] 3caMBvQX7k=pass
[[15:03:43]] [SUCCESS] Screenshot refreshed successfully
[[15:03:43]] [SUCCESS] Screenshot refreshed successfully
[[15:03:42]] [INFO] 3caMBvQX7k=running
[[15:03:42]] [INFO] Executing action 187/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:03:42]] [SUCCESS] Screenshot refreshed
[[15:03:42]] [INFO] Refreshing screenshot...
[[15:03:42]] [INFO] yUJyVO5Wev=pass
[[15:03:40]] [SUCCESS] Screenshot refreshed successfully
[[15:03:40]] [SUCCESS] Screenshot refreshed successfully
[[15:03:39]] [INFO] yUJyVO5Wev=running
[[15:03:39]] [INFO] Executing action 186/591: iOS Function: alert_accept
[[15:03:39]] [SUCCESS] Screenshot refreshed
[[15:03:39]] [INFO] Refreshing screenshot...
[[15:03:39]] [INFO] rkL0oz4kiL=pass
[[15:03:30]] [SUCCESS] Screenshot refreshed successfully
[[15:03:30]] [SUCCESS] Screenshot refreshed successfully
[[15:03:29]] [INFO] rkL0oz4kiL=running
[[15:03:29]] [INFO] Executing action 185/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:03:29]] [SUCCESS] Screenshot refreshed
[[15:03:29]] [INFO] Refreshing screenshot...
[[15:03:29]] [INFO] HotUJOd6oB=pass
[[15:03:16]] [SUCCESS] Screenshot refreshed successfully
[[15:03:16]] [SUCCESS] Screenshot refreshed successfully
[[15:03:15]] [INFO] HotUJOd6oB=running
[[15:03:15]] [INFO] Executing action 184/591: Restart app: env[appid]
[[15:03:15]] [SUCCESS] Screenshot refreshed
[[15:03:15]] [INFO] Refreshing screenshot...
[[15:03:14]] [SUCCESS] Screenshot refreshed
[[15:03:14]] [INFO] Refreshing screenshot...
[[15:03:11]] [SUCCESS] Screenshot refreshed successfully
[[15:03:11]] [SUCCESS] Screenshot refreshed successfully
[[15:03:11]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:03:10]] [SUCCESS] Screenshot refreshed
[[15:03:10]] [INFO] Refreshing screenshot...
[[15:02:58]] [SUCCESS] Screenshot refreshed successfully
[[15:02:58]] [SUCCESS] Screenshot refreshed successfully
[[15:02:58]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:02:58]] [SUCCESS] Screenshot refreshed
[[15:02:58]] [INFO] Refreshing screenshot...
[[15:02:54]] [SUCCESS] Screenshot refreshed successfully
[[15:02:54]] [SUCCESS] Screenshot refreshed successfully
[[15:02:54]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:02:53]] [SUCCESS] Screenshot refreshed
[[15:02:53]] [INFO] Refreshing screenshot...
[[15:02:50]] [SUCCESS] Screenshot refreshed successfully
[[15:02:50]] [SUCCESS] Screenshot refreshed successfully
[[15:02:49]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:02:49]] [SUCCESS] Screenshot refreshed
[[15:02:49]] [INFO] Refreshing screenshot...
[[15:02:42]] [SUCCESS] Screenshot refreshed successfully
[[15:02:42]] [SUCCESS] Screenshot refreshed successfully
[[15:02:42]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:02:41]] [SUCCESS] Screenshot refreshed
[[15:02:41]] [INFO] Refreshing screenshot...
[[15:02:34]] [SUCCESS] Screenshot refreshed successfully
[[15:02:34]] [SUCCESS] Screenshot refreshed successfully
[[15:02:34]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:02:34]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:02:34]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:02:34]] [INFO] IR7wnjW7C8=running
[[15:02:34]] [INFO] Executing action 183/591: cleanupSteps action
[[15:02:33]] [SUCCESS] Screenshot refreshed
[[15:02:33]] [INFO] Refreshing screenshot...
[[15:02:33]] [INFO] 7WYExJTqjp=pass
[[15:02:28]] [SUCCESS] Screenshot refreshed successfully
[[15:02:28]] [SUCCESS] Screenshot refreshed successfully
[[15:02:28]] [INFO] 7WYExJTqjp=running
[[15:02:28]] [INFO] Executing action 182/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[15:02:27]] [SUCCESS] Screenshot refreshed
[[15:02:27]] [INFO] Refreshing screenshot...
[[15:02:27]] [INFO] 4WfPFN961S=pass
[[15:02:20]] [SUCCESS] Screenshot refreshed successfully
[[15:02:20]] [SUCCESS] Screenshot refreshed successfully
[[15:02:20]] [INFO] 4WfPFN961S=running
[[15:02:20]] [INFO] Executing action 181/591: Swipe from (50%, 70%) to (50%, 30%)
[[15:02:20]] [SUCCESS] Screenshot refreshed
[[15:02:20]] [INFO] Refreshing screenshot...
[[15:02:20]] [INFO] NurQsFoMkE=pass
[[15:02:16]] [INFO] NurQsFoMkE=running
[[15:02:16]] [INFO] Executing action 180/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:02:16]] [SUCCESS] Screenshot refreshed successfully
[[15:02:16]] [SUCCESS] Screenshot refreshed successfully
[[15:02:15]] [SUCCESS] Screenshot refreshed
[[15:02:15]] [INFO] Refreshing screenshot...
[[15:02:15]] [INFO] CkfAScJNq8=pass
[[15:02:12]] [INFO] CkfAScJNq8=running
[[15:02:12]] [INFO] Executing action 179/591: Tap on image: env[closebtnimage]
[[15:02:11]] [SUCCESS] Screenshot refreshed successfully
[[15:02:11]] [SUCCESS] Screenshot refreshed successfully
[[15:02:11]] [SUCCESS] Screenshot refreshed
[[15:02:11]] [INFO] Refreshing screenshot...
[[15:02:11]] [INFO] 1NWfFsDiTQ=pass
[[15:02:07]] [SUCCESS] Screenshot refreshed successfully
[[15:02:07]] [SUCCESS] Screenshot refreshed successfully
[[15:02:07]] [INFO] 1NWfFsDiTQ=running
[[15:02:07]] [INFO] Executing action 178/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[15:02:06]] [SUCCESS] Screenshot refreshed
[[15:02:06]] [INFO] Refreshing screenshot...
[[15:02:06]] [INFO] tufIibCj03=pass
[[15:02:00]] [INFO] tufIibCj03=running
[[15:02:00]] [INFO] Executing action 177/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[15:02:00]] [SUCCESS] Screenshot refreshed successfully
[[15:02:00]] [SUCCESS] Screenshot refreshed successfully
[[15:02:00]] [SUCCESS] Screenshot refreshed
[[15:02:00]] [INFO] Refreshing screenshot...
[[15:02:00]] [INFO] uNbKV4slh0=pass
[[15:01:54]] [SUCCESS] Screenshot refreshed successfully
[[15:01:54]] [SUCCESS] Screenshot refreshed successfully
[[15:01:53]] [INFO] uNbKV4slh0=running
[[15:01:53]] [INFO] Executing action 176/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:01:53]] [SUCCESS] Screenshot refreshed
[[15:01:53]] [INFO] Refreshing screenshot...
[[15:01:53]] [SUCCESS] Screenshot refreshed successfully
[[15:01:53]] [SUCCESS] Screenshot refreshed successfully
[[15:01:53]] [SUCCESS] Screenshot refreshed
[[15:01:53]] [INFO] Refreshing screenshot...
[[15:01:48]] [SUCCESS] Screenshot refreshed successfully
[[15:01:48]] [SUCCESS] Screenshot refreshed successfully
[[15:01:48]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:01:48]] [SUCCESS] Screenshot refreshed
[[15:01:48]] [INFO] Refreshing screenshot...
[[15:01:44]] [SUCCESS] Screenshot refreshed successfully
[[15:01:44]] [SUCCESS] Screenshot refreshed successfully
[[15:01:43]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:01:43]] [SUCCESS] Screenshot refreshed
[[15:01:43]] [INFO] Refreshing screenshot...
[[15:01:38]] [SUCCESS] Screenshot refreshed successfully
[[15:01:38]] [SUCCESS] Screenshot refreshed successfully
[[15:01:38]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[15:01:38]] [SUCCESS] Screenshot refreshed
[[15:01:38]] [INFO] Refreshing screenshot...
[[15:01:33]] [SUCCESS] Screenshot refreshed successfully
[[15:01:33]] [SUCCESS] Screenshot refreshed successfully
[[15:01:32]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:01:32]] [SUCCESS] Screenshot refreshed
[[15:01:32]] [INFO] Refreshing screenshot...
[[15:01:26]] [SUCCESS] Screenshot refreshed successfully
[[15:01:26]] [SUCCESS] Screenshot refreshed successfully
[[15:01:26]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:01:26]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:01:26]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:01:26]] [INFO] ZhcKErtP27=running
[[15:01:26]] [INFO] Executing action 175/591: Execute Test Case: Kmart-Signin (5 steps)
[[15:01:25]] [SUCCESS] Screenshot refreshed
[[15:01:25]] [INFO] Refreshing screenshot...
[[15:01:25]] [INFO] q9ZiyYoE5B=pass
[[15:01:23]] [INFO] q9ZiyYoE5B=running
[[15:01:23]] [INFO] Executing action 174/591: iOS Function: alert_accept
[[15:01:23]] [SUCCESS] Screenshot refreshed successfully
[[15:01:23]] [SUCCESS] Screenshot refreshed successfully
[[15:01:22]] [SUCCESS] Screenshot refreshed
[[15:01:22]] [INFO] Refreshing screenshot...
[[15:01:22]] [INFO] STEdg5jOU8=pass
[[15:01:17]] [SUCCESS] Screenshot refreshed successfully
[[15:01:17]] [SUCCESS] Screenshot refreshed successfully
[[15:01:17]] [INFO] STEdg5jOU8=running
[[15:01:17]] [INFO] Executing action 173/591: Tap on Text: "in"
[[15:01:17]] [SUCCESS] Screenshot refreshed
[[15:01:17]] [INFO] Refreshing screenshot...
[[15:01:17]] [INFO] LDH2hlTZT9=pass
[[15:01:10]] [SUCCESS] Screenshot refreshed successfully
[[15:01:10]] [SUCCESS] Screenshot refreshed successfully
[[15:01:10]] [INFO] LDH2hlTZT9=running
[[15:01:10]] [INFO] Executing action 172/591: Wait for 5 ms
[[15:01:09]] [SUCCESS] Screenshot refreshed
[[15:01:09]] [INFO] Refreshing screenshot...
[[15:01:09]] [INFO] 5Dk9h5bQWl=pass
[[15:01:03]] [SUCCESS] Screenshot refreshed successfully
[[15:01:03]] [SUCCESS] Screenshot refreshed successfully
[[15:01:02]] [INFO] 5Dk9h5bQWl=running
[[15:01:02]] [INFO] Executing action 171/591: Tap on element with accessibility_id: Continue to details
[[15:01:01]] [SUCCESS] Screenshot refreshed
[[15:01:01]] [INFO] Refreshing screenshot...
[[15:01:01]] [INFO] VMzFZ2uTwl=pass
[[15:00:54]] [SUCCESS] Screenshot refreshed successfully
[[15:00:54]] [SUCCESS] Screenshot refreshed successfully
[[15:00:53]] [INFO] VMzFZ2uTwl=running
[[15:00:53]] [INFO] Executing action 170/591: Swipe up till element accessibilityid: "Continue to details" is visible
[[15:00:53]] [SUCCESS] Screenshot refreshed
[[15:00:53]] [INFO] Refreshing screenshot...
[[15:00:52]] [SUCCESS] Screenshot refreshed
[[15:00:52]] [INFO] Refreshing screenshot...
[[15:00:43]] [SUCCESS] Screenshot refreshed successfully
[[15:00:43]] [SUCCESS] Screenshot refreshed successfully
[[15:00:43]] [INFO] Executing Multi Step action step 7/7: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[15:00:42]] [SUCCESS] Screenshot refreshed
[[15:00:42]] [INFO] Refreshing screenshot...
[[15:00:37]] [SUCCESS] Screenshot refreshed successfully
[[15:00:37]] [SUCCESS] Screenshot refreshed successfully
[[15:00:37]] [INFO] Executing Multi Step action step 6/7: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[15:00:37]] [SUCCESS] Screenshot refreshed
[[15:00:37]] [INFO] Refreshing screenshot...
[[15:00:31]] [SUCCESS] Screenshot refreshed successfully
[[15:00:31]] [SUCCESS] Screenshot refreshed successfully
[[15:00:31]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:00:30]] [SUCCESS] Screenshot refreshed
[[15:00:30]] [INFO] Refreshing screenshot...
[[15:00:26]] [SUCCESS] Screenshot refreshed successfully
[[15:00:26]] [SUCCESS] Screenshot refreshed successfully
[[15:00:26]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[15:00:25]] [SUCCESS] Screenshot refreshed
[[15:00:25]] [INFO] Refreshing screenshot...
[[15:00:22]] [SUCCESS] Screenshot refreshed successfully
[[15:00:22]] [SUCCESS] Screenshot refreshed successfully
[[15:00:21]] [INFO] Executing Multi Step action step 3/7: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:00:21]] [SUCCESS] Screenshot refreshed
[[15:00:21]] [INFO] Refreshing screenshot...
[[15:00:16]] [SUCCESS] Screenshot refreshed successfully
[[15:00:16]] [SUCCESS] Screenshot refreshed successfully
[[15:00:16]] [INFO] Executing Multi Step action step 2/7: iOS Function: text - Text: "Notebook"
[[15:00:15]] [SUCCESS] Screenshot refreshed
[[15:00:15]] [INFO] Refreshing screenshot...
[[15:00:08]] [SUCCESS] Screenshot refreshed successfully
[[15:00:08]] [SUCCESS] Screenshot refreshed successfully
[[15:00:07]] [INFO] Executing Multi Step action step 1/7: Tap on Text: "Find"
[[15:00:07]] [INFO] Loaded 7 steps from test case: Search and Add (Notebooks)
[[15:00:07]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[15:00:07]] [INFO] 8HTspxuvVG=running
[[15:00:07]] [INFO] Executing action 169/591: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[15:00:07]] [SUCCESS] Screenshot refreshed
[[15:00:07]] [INFO] Refreshing screenshot...
[[15:00:07]] [INFO] NurQsFoMkE=pass
[[15:00:01]] [SUCCESS] Screenshot refreshed successfully
[[15:00:01]] [SUCCESS] Screenshot refreshed successfully
[[15:00:01]] [INFO] NurQsFoMkE=running
[[15:00:01]] [INFO] Executing action 168/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[15:00:00]] [SUCCESS] Screenshot refreshed
[[15:00:00]] [INFO] Refreshing screenshot...
[[15:00:00]] [INFO] 7QpmNS6hif=pass
[[14:59:56]] [SUCCESS] Screenshot refreshed successfully
[[14:59:56]] [SUCCESS] Screenshot refreshed successfully
[[14:59:55]] [INFO] 7QpmNS6hif=running
[[14:59:55]] [INFO] Executing action 167/591: Restart app: env[appid]
[[14:59:54]] [SUCCESS] Screenshot refreshed
[[14:59:54]] [INFO] Refreshing screenshot...
[[14:59:54]] [INFO] 7WYExJTqjp=pass
[[14:59:50]] [SUCCESS] Screenshot refreshed successfully
[[14:59:50]] [SUCCESS] Screenshot refreshed successfully
[[14:59:50]] [INFO] 7WYExJTqjp=running
[[14:59:50]] [INFO] Executing action 166/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:59:49]] [SUCCESS] Screenshot refreshed
[[14:59:49]] [INFO] Refreshing screenshot...
[[14:59:49]] [INFO] 4WfPFN961S=pass
[[14:59:42]] [SUCCESS] Screenshot refreshed successfully
[[14:59:42]] [SUCCESS] Screenshot refreshed successfully
[[14:59:42]] [INFO] 4WfPFN961S=running
[[14:59:42]] [INFO] Executing action 165/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:59:42]] [SUCCESS] Screenshot refreshed
[[14:59:42]] [INFO] Refreshing screenshot...
[[14:59:42]] [INFO] NurQsFoMkE=pass
[[14:59:38]] [INFO] NurQsFoMkE=running
[[14:59:38]] [INFO] Executing action 164/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:59:38]] [SUCCESS] Screenshot refreshed successfully
[[14:59:38]] [SUCCESS] Screenshot refreshed successfully
[[14:59:37]] [SUCCESS] Screenshot refreshed
[[14:59:37]] [INFO] Refreshing screenshot...
[[14:59:37]] [INFO] CkfAScJNq8=pass
[[14:59:34]] [SUCCESS] Screenshot refreshed successfully
[[14:59:34]] [SUCCESS] Screenshot refreshed successfully
[[14:59:33]] [INFO] CkfAScJNq8=running
[[14:59:33]] [INFO] Executing action 163/591: Tap on image: env[closebtnimage]
[[14:59:33]] [SUCCESS] Screenshot refreshed
[[14:59:33]] [INFO] Refreshing screenshot...
[[14:59:33]] [INFO] 1NWfFsDiTQ=pass
[[14:59:29]] [SUCCESS] Screenshot refreshed successfully
[[14:59:29]] [SUCCESS] Screenshot refreshed successfully
[[14:59:29]] [INFO] 1NWfFsDiTQ=running
[[14:59:29]] [INFO] Executing action 162/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:59:28]] [SUCCESS] Screenshot refreshed
[[14:59:28]] [INFO] Refreshing screenshot...
[[14:59:28]] [INFO] tufIibCj03=pass
[[14:59:24]] [SUCCESS] Screenshot refreshed successfully
[[14:59:24]] [SUCCESS] Screenshot refreshed successfully
[[14:59:24]] [INFO] tufIibCj03=running
[[14:59:24]] [INFO] Executing action 161/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[14:59:23]] [SUCCESS] Screenshot refreshed
[[14:59:23]] [INFO] Refreshing screenshot...
[[14:59:23]] [INFO] g8u66qfKkX=pass
[[14:59:20]] [INFO] g8u66qfKkX=running
[[14:59:20]] [INFO] Executing action 160/591: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:59:20]] [SUCCESS] Screenshot refreshed successfully
[[14:59:20]] [SUCCESS] Screenshot refreshed successfully
[[14:59:19]] [SUCCESS] Screenshot refreshed
[[14:59:19]] [INFO] Refreshing screenshot...
[[14:59:19]] [INFO] mg4S62Rdtq=pass
[[14:59:12]] [SUCCESS] Screenshot refreshed successfully
[[14:59:12]] [SUCCESS] Screenshot refreshed successfully
[[14:59:12]] [INFO] mg4S62Rdtq=running
[[14:59:12]] [INFO] Executing action 159/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:59:12]] [SUCCESS] Screenshot refreshed
[[14:59:12]] [INFO] Refreshing screenshot...
[[14:59:12]] [INFO] pCPTAtSZbf=pass
[[14:59:07]] [SUCCESS] Screenshot refreshed successfully
[[14:59:07]] [SUCCESS] Screenshot refreshed successfully
[[14:59:07]] [INFO] pCPTAtSZbf=running
[[14:59:07]] [INFO] Executing action 158/591: iOS Function: text - Text: "Wonderbaby@5"
[[14:59:07]] [SUCCESS] Screenshot refreshed
[[14:59:07]] [INFO] Refreshing screenshot...
[[14:59:07]] [INFO] DaVBARRwft=pass
[[14:59:01]] [SUCCESS] Screenshot refreshed successfully
[[14:59:01]] [SUCCESS] Screenshot refreshed successfully
[[14:59:01]] [INFO] DaVBARRwft=running
[[14:59:01]] [INFO] Executing action 157/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[14:59:00]] [SUCCESS] Screenshot refreshed
[[14:59:00]] [INFO] Refreshing screenshot...
[[14:59:00]] [INFO] e1RoZWCZJb=pass
[[14:58:55]] [SUCCESS] Screenshot refreshed successfully
[[14:58:55]] [SUCCESS] Screenshot refreshed successfully
[[14:58:55]] [INFO] e1RoZWCZJb=running
[[14:58:55]] [INFO] Executing action 156/591: iOS Function: text - Text: "<EMAIL>"
[[14:58:55]] [SUCCESS] Screenshot refreshed
[[14:58:55]] [INFO] Refreshing screenshot...
[[14:58:55]] [INFO] 50Z2jrodNd=pass
[[14:58:50]] [SUCCESS] Screenshot refreshed successfully
[[14:58:50]] [SUCCESS] Screenshot refreshed successfully
[[14:58:50]] [INFO] 50Z2jrodNd=running
[[14:58:50]] [INFO] Executing action 155/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:58:50]] [SUCCESS] Screenshot refreshed
[[14:58:50]] [INFO] Refreshing screenshot...
[[14:58:50]] [INFO] q9ZiyYoE5B=pass
[[14:58:47]] [INFO] q9ZiyYoE5B=running
[[14:58:47]] [INFO] Executing action 154/591: iOS Function: alert_accept
[[14:58:47]] [SUCCESS] Screenshot refreshed successfully
[[14:58:47]] [SUCCESS] Screenshot refreshed successfully
[[14:58:46]] [SUCCESS] Screenshot refreshed
[[14:58:46]] [INFO] Refreshing screenshot...
[[14:58:46]] [INFO] 6PL8P3rT57=pass
[[14:58:42]] [SUCCESS] Screenshot refreshed successfully
[[14:58:42]] [SUCCESS] Screenshot refreshed successfully
[[14:58:41]] [INFO] 6PL8P3rT57=running
[[14:58:41]] [INFO] Executing action 153/591: Tap on Text: "Sign"
[[14:58:41]] [SUCCESS] Screenshot refreshed
[[14:58:41]] [INFO] Refreshing screenshot...
[[14:58:41]] [INFO] 2YGctqXNED=pass
[[14:58:35]] [SUCCESS] Screenshot refreshed successfully
[[14:58:35]] [SUCCESS] Screenshot refreshed successfully
[[14:58:35]] [INFO] 2YGctqXNED=running
[[14:58:35]] [INFO] Executing action 152/591: Tap on element with accessibility_id: Continue to details
[[14:58:34]] [SUCCESS] Screenshot refreshed
[[14:58:34]] [INFO] Refreshing screenshot...
[[14:58:34]] [INFO] 2YGctqXNED=pass
[[14:58:25]] [SUCCESS] Screenshot refreshed successfully
[[14:58:25]] [SUCCESS] Screenshot refreshed successfully
[[14:58:25]] [INFO] 2YGctqXNED=running
[[14:58:25]] [INFO] Executing action 151/591: Swipe up till element accessibilityid: "Continue to details" is visible
[[14:58:24]] [SUCCESS] Screenshot refreshed
[[14:58:24]] [INFO] Refreshing screenshot...
[[14:58:24]] [INFO] tufIibCj03=pass
[[14:58:20]] [SUCCESS] Screenshot refreshed successfully
[[14:58:20]] [SUCCESS] Screenshot refreshed successfully
[[14:58:20]] [INFO] tufIibCj03=running
[[14:58:20]] [INFO] Executing action 150/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[14:58:19]] [SUCCESS] Screenshot refreshed
[[14:58:19]] [INFO] Refreshing screenshot...
[[14:58:19]] [INFO] g8u66qfKkX=pass
[[14:58:16]] [SUCCESS] Screenshot refreshed successfully
[[14:58:16]] [SUCCESS] Screenshot refreshed successfully
[[14:58:16]] [INFO] g8u66qfKkX=running
[[14:58:16]] [INFO] Executing action 149/591: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:58:15]] [SUCCESS] Screenshot refreshed
[[14:58:15]] [INFO] Refreshing screenshot...
[[14:58:15]] [INFO] ZBXuV4sJUR=pass
[[14:58:10]] [SUCCESS] Screenshot refreshed successfully
[[14:58:10]] [SUCCESS] Screenshot refreshed successfully
[[14:58:10]] [INFO] ZBXuV4sJUR=running
[[14:58:10]] [INFO] Executing action 148/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:58:10]] [SUCCESS] Screenshot refreshed
[[14:58:10]] [INFO] Refreshing screenshot...
[[14:58:10]] [INFO] XryN8qR1DX=pass
[[14:58:05]] [SUCCESS] Screenshot refreshed successfully
[[14:58:05]] [SUCCESS] Screenshot refreshed successfully
[[14:58:05]] [INFO] XryN8qR1DX=running
[[14:58:05]] [INFO] Executing action 147/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:58:04]] [SUCCESS] Screenshot refreshed
[[14:58:04]] [INFO] Refreshing screenshot...
[[14:58:04]] [INFO] XcWXIMtv1E=pass
[[14:57:57]] [SUCCESS] Screenshot refreshed successfully
[[14:57:57]] [SUCCESS] Screenshot refreshed successfully
[[14:57:56]] [INFO] XcWXIMtv1E=running
[[14:57:56]] [INFO] Executing action 146/591: Wait for 5 ms
[[14:57:56]] [SUCCESS] Screenshot refreshed
[[14:57:56]] [INFO] Refreshing screenshot...
[[14:57:56]] [INFO] S1cQQxksEj=pass
[[14:57:48]] [SUCCESS] Screenshot refreshed successfully
[[14:57:48]] [SUCCESS] Screenshot refreshed successfully
[[14:57:48]] [INFO] S1cQQxksEj=running
[[14:57:48]] [INFO] Executing action 145/591: Tap on element with accessibility_id: Add to bag
[[14:57:48]] [SUCCESS] Screenshot refreshed
[[14:57:48]] [INFO] Refreshing screenshot...
[[14:57:48]] [INFO] K2w9XUGwnb=pass
[[14:57:39]] [SUCCESS] Screenshot refreshed successfully
[[14:57:39]] [SUCCESS] Screenshot refreshed successfully
[[14:57:39]] [INFO] K2w9XUGwnb=running
[[14:57:39]] [INFO] Executing action 144/591: Swipe up till element accessibility_id: "Add to bag" is visible
[[14:57:38]] [SUCCESS] Screenshot refreshed
[[14:57:38]] [INFO] Refreshing screenshot...
[[14:57:38]] [INFO] BTYxjEaZEk=pass
[[14:57:35]] [SUCCESS] Screenshot refreshed successfully
[[14:57:35]] [SUCCESS] Screenshot refreshed successfully
[[14:57:34]] [INFO] BTYxjEaZEk=running
[[14:57:34]] [INFO] Executing action 143/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:57:33]] [SUCCESS] Screenshot refreshed
[[14:57:33]] [INFO] Refreshing screenshot...
[[14:57:33]] [INFO] YC6bBrKQgq=pass
[[14:57:29]] [SUCCESS] Screenshot refreshed successfully
[[14:57:29]] [SUCCESS] Screenshot refreshed successfully
[[14:57:28]] [INFO] YC6bBrKQgq=running
[[14:57:28]] [INFO] Executing action 142/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:57:28]] [SUCCESS] Screenshot refreshed
[[14:57:28]] [INFO] Refreshing screenshot...
[[14:57:28]] [INFO] aRgHcQcLDP=pass
[[14:57:23]] [SUCCESS] Screenshot refreshed successfully
[[14:57:23]] [SUCCESS] Screenshot refreshed successfully
[[14:57:23]] [INFO] aRgHcQcLDP=running
[[14:57:23]] [INFO] Executing action 141/591: iOS Function: text - Text: "uno card"
[[14:57:23]] [SUCCESS] Screenshot refreshed
[[14:57:23]] [INFO] Refreshing screenshot...
[[14:57:23]] [INFO] 4PZC1vVWJW=pass
[[14:57:17]] [SUCCESS] Screenshot refreshed successfully
[[14:57:17]] [SUCCESS] Screenshot refreshed successfully
[[14:57:17]] [INFO] 4PZC1vVWJW=running
[[14:57:17]] [INFO] Executing action 140/591: Tap on Text: "Find"
[[14:57:16]] [SUCCESS] Screenshot refreshed
[[14:57:16]] [INFO] Refreshing screenshot...
[[14:57:16]] [INFO] XryN8qR1DX=pass
[[14:57:12]] [SUCCESS] Screenshot refreshed successfully
[[14:57:12]] [SUCCESS] Screenshot refreshed successfully
[[14:57:12]] [INFO] XryN8qR1DX=running
[[14:57:12]] [INFO] Executing action 139/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:57:11]] [SUCCESS] Screenshot refreshed
[[14:57:11]] [INFO] Refreshing screenshot...
[[14:57:11]] [INFO] 7WYExJTqjp=pass
[[14:57:06]] [SUCCESS] Screenshot refreshed successfully
[[14:57:06]] [SUCCESS] Screenshot refreshed successfully
[[14:57:06]] [INFO] 7WYExJTqjp=running
[[14:57:06]] [INFO] Executing action 138/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:57:06]] [SUCCESS] Screenshot refreshed
[[14:57:06]] [INFO] Refreshing screenshot...
[[14:57:06]] [INFO] 4WfPFN961S=pass
[[14:56:58]] [SUCCESS] Screenshot refreshed successfully
[[14:56:58]] [SUCCESS] Screenshot refreshed successfully
[[14:56:58]] [INFO] 4WfPFN961S=running
[[14:56:58]] [INFO] Executing action 137/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:56:58]] [SUCCESS] Screenshot refreshed
[[14:56:58]] [INFO] Refreshing screenshot...
[[14:56:58]] [INFO] NurQsFoMkE=pass
[[14:56:54]] [SUCCESS] Screenshot refreshed successfully
[[14:56:54]] [SUCCESS] Screenshot refreshed successfully
[[14:56:54]] [INFO] NurQsFoMkE=running
[[14:56:54]] [INFO] Executing action 136/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:56:54]] [SUCCESS] Screenshot refreshed
[[14:56:54]] [INFO] Refreshing screenshot...
[[14:56:53]] [SUCCESS] Screenshot refreshed
[[14:56:53]] [INFO] Refreshing screenshot...
[[14:56:49]] [SUCCESS] Screenshot refreshed successfully
[[14:56:49]] [SUCCESS] Screenshot refreshed successfully
[[14:56:49]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:56:48]] [SUCCESS] Screenshot refreshed
[[14:56:48]] [INFO] Refreshing screenshot...
[[14:56:44]] [SUCCESS] Screenshot refreshed successfully
[[14:56:44]] [SUCCESS] Screenshot refreshed successfully
[[14:56:44]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:56:43]] [SUCCESS] Screenshot refreshed
[[14:56:43]] [INFO] Refreshing screenshot...
[[14:56:38]] [SUCCESS] Screenshot refreshed successfully
[[14:56:38]] [SUCCESS] Screenshot refreshed successfully
[[14:56:38]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[14:56:38]] [SUCCESS] Screenshot refreshed
[[14:56:38]] [INFO] Refreshing screenshot...
[[14:56:35]] [SUCCESS] Screenshot refreshed successfully
[[14:56:35]] [SUCCESS] Screenshot refreshed successfully
[[14:56:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:56:33]] [SUCCESS] Screenshot refreshed
[[14:56:33]] [INFO] Refreshing screenshot...
[[14:56:26]] [SUCCESS] Screenshot refreshed successfully
[[14:56:26]] [SUCCESS] Screenshot refreshed successfully
[[14:56:26]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:56:26]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:56:26]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:56:26]] [INFO] mWOCt0aAWW=running
[[14:56:26]] [INFO] Executing action 135/591: Execute Test Case: Kmart-Signin (5 steps)
[[14:56:26]] [SUCCESS] Screenshot refreshed
[[14:56:26]] [INFO] Refreshing screenshot...
[[14:56:26]] [INFO] byEe7qbCpq=pass
[[14:56:23]] [SUCCESS] Screenshot refreshed successfully
[[14:56:23]] [SUCCESS] Screenshot refreshed successfully
[[14:56:23]] [INFO] byEe7qbCpq=running
[[14:56:23]] [INFO] Executing action 134/591: iOS Function: alert_accept
[[14:56:23]] [SUCCESS] Screenshot refreshed
[[14:56:23]] [INFO] Refreshing screenshot...
[[14:56:23]] [INFO] L6wTorOX8B=pass
[[14:56:19]] [SUCCESS] Screenshot refreshed successfully
[[14:56:19]] [SUCCESS] Screenshot refreshed successfully
[[14:56:18]] [INFO] L6wTorOX8B=running
[[14:56:18]] [INFO] Executing action 133/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[14:56:18]] [SUCCESS] Screenshot refreshed
[[14:56:18]] [INFO] Refreshing screenshot...
[[14:56:18]] [INFO] XryN8qR1DX=pass
[[14:56:14]] [SUCCESS] Screenshot refreshed successfully
[[14:56:14]] [SUCCESS] Screenshot refreshed successfully
[[14:56:14]] [INFO] XryN8qR1DX=running
[[14:56:14]] [INFO] Executing action 132/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:56:13]] [SUCCESS] Screenshot refreshed
[[14:56:13]] [INFO] Refreshing screenshot...
[[14:56:13]] [INFO] lCSewtjn1z=pass
[[14:56:09]] [SUCCESS] Screenshot refreshed successfully
[[14:56:09]] [SUCCESS] Screenshot refreshed successfully
[[14:56:08]] [INFO] lCSewtjn1z=running
[[14:56:08]] [INFO] Executing action 131/591: Restart app: env[appid]
[[14:56:08]] [SUCCESS] Screenshot refreshed
[[14:56:08]] [INFO] Refreshing screenshot...
[[14:56:08]] [INFO] IJh702cxG0=pass
[[14:56:02]] [SUCCESS] Screenshot refreshed successfully
[[14:56:02]] [SUCCESS] Screenshot refreshed successfully
[[14:56:02]] [INFO] IJh702cxG0=running
[[14:56:02]] [INFO] Executing action 130/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:56:01]] [SUCCESS] Screenshot refreshed
[[14:56:01]] [INFO] Refreshing screenshot...
[[14:56:01]] [INFO] 4WfPFN961S=pass
[[14:55:54]] [SUCCESS] Screenshot refreshed successfully
[[14:55:54]] [SUCCESS] Screenshot refreshed successfully
[[14:55:54]] [INFO] 4WfPFN961S=running
[[14:55:54]] [INFO] Executing action 129/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:55:54]] [SUCCESS] Screenshot refreshed
[[14:55:54]] [INFO] Refreshing screenshot...
[[14:55:54]] [INFO] AOcOOSuOsB=pass
[[14:55:49]] [SUCCESS] Screenshot refreshed successfully
[[14:55:49]] [SUCCESS] Screenshot refreshed successfully
[[14:55:49]] [INFO] AOcOOSuOsB=running
[[14:55:49]] [INFO] Executing action 128/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:55:48]] [SUCCESS] Screenshot refreshed
[[14:55:48]] [INFO] Refreshing screenshot...
[[14:55:48]] [INFO] AOcOOSuOsB=pass
[[14:55:43]] [SUCCESS] Screenshot refreshed successfully
[[14:55:43]] [SUCCESS] Screenshot refreshed successfully
[[14:55:42]] [INFO] AOcOOSuOsB=running
[[14:55:42]] [INFO] Executing action 127/591: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:55:42]] [SUCCESS] Screenshot refreshed
[[14:55:42]] [INFO] Refreshing screenshot...
[[14:55:42]] [INFO] N2yjynioko=pass
[[14:55:37]] [SUCCESS] Screenshot refreshed successfully
[[14:55:37]] [SUCCESS] Screenshot refreshed successfully
[[14:55:37]] [INFO] N2yjynioko=running
[[14:55:37]] [INFO] Executing action 126/591: iOS Function: text - Text: "Wonderbaby@5"
[[14:55:37]] [SUCCESS] Screenshot refreshed
[[14:55:37]] [INFO] Refreshing screenshot...
[[14:55:37]] [INFO] SHaIduBnay=pass
[[14:55:30]] [SUCCESS] Screenshot refreshed successfully
[[14:55:30]] [SUCCESS] Screenshot refreshed successfully
[[14:55:30]] [INFO] SHaIduBnay=running
[[14:55:30]] [INFO] Executing action 125/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[14:55:30]] [SUCCESS] Screenshot refreshed
[[14:55:30]] [INFO] Refreshing screenshot...
[[14:55:30]] [INFO] wuIMlAwYVA=pass
[[14:55:25]] [SUCCESS] Screenshot refreshed successfully
[[14:55:25]] [SUCCESS] Screenshot refreshed successfully
[[14:55:25]] [INFO] wuIMlAwYVA=running
[[14:55:25]] [INFO] Executing action 124/591: iOS Function: text - Text: "env[uname1]"
[[14:55:24]] [SUCCESS] Screenshot refreshed
[[14:55:24]] [INFO] Refreshing screenshot...
[[14:55:24]] [INFO] 50Z2jrodNd=pass
[[14:55:20]] [SUCCESS] Screenshot refreshed successfully
[[14:55:20]] [SUCCESS] Screenshot refreshed successfully
[[14:55:20]] [INFO] 50Z2jrodNd=running
[[14:55:20]] [INFO] Executing action 123/591: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[14:55:19]] [SUCCESS] Screenshot refreshed
[[14:55:19]] [INFO] Refreshing screenshot...
[[14:55:19]] [INFO] VK2oI6mXSB=pass
[[14:55:15]] [SUCCESS] Screenshot refreshed successfully
[[14:55:15]] [SUCCESS] Screenshot refreshed successfully
[[14:55:15]] [INFO] VK2oI6mXSB=running
[[14:55:15]] [INFO] Executing action 122/591: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[14:55:15]] [SUCCESS] Screenshot refreshed
[[14:55:15]] [INFO] Refreshing screenshot...
[[14:55:15]] [INFO] q9ZiyYoE5B=pass
[[14:55:12]] [SUCCESS] Screenshot refreshed successfully
[[14:55:12]] [SUCCESS] Screenshot refreshed successfully
[[14:55:12]] [INFO] q9ZiyYoE5B=running
[[14:55:12]] [INFO] Executing action 121/591: iOS Function: alert_accept
[[14:55:11]] [SUCCESS] Screenshot refreshed
[[14:55:11]] [INFO] Refreshing screenshot...
[[14:55:11]] [INFO] 4PZC1vVWJW=pass
[[14:55:06]] [SUCCESS] Screenshot refreshed successfully
[[14:55:06]] [SUCCESS] Screenshot refreshed successfully
[[14:55:06]] [INFO] 4PZC1vVWJW=running
[[14:55:06]] [INFO] Executing action 120/591: Tap on Text: "Sign"
[[14:55:05]] [SUCCESS] Screenshot refreshed
[[14:55:05]] [INFO] Refreshing screenshot...
[[14:55:05]] [INFO] mcscWdhpn2=pass
[[14:54:48]] [SUCCESS] Screenshot refreshed successfully
[[14:54:48]] [SUCCESS] Screenshot refreshed successfully
[[14:54:48]] [INFO] mcscWdhpn2=running
[[14:54:48]] [INFO] Executing action 119/591: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[14:54:47]] [SUCCESS] Screenshot refreshed
[[14:54:47]] [INFO] Refreshing screenshot...
[[14:54:47]] [INFO] 6zUBxjSFym=pass
[[14:54:43]] [SUCCESS] Screenshot refreshed successfully
[[14:54:43]] [SUCCESS] Screenshot refreshed successfully
[[14:54:43]] [INFO] 6zUBxjSFym=running
[[14:54:43]] [INFO] Executing action 118/591: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:54:42]] [SUCCESS] Screenshot refreshed
[[14:54:42]] [INFO] Refreshing screenshot...
[[14:54:42]] [INFO] BTYxjEaZEk=pass
[[14:54:38]] [SUCCESS] Screenshot refreshed successfully
[[14:54:38]] [SUCCESS] Screenshot refreshed successfully
[[14:54:38]] [INFO] BTYxjEaZEk=running
[[14:54:38]] [INFO] Executing action 117/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:54:37]] [SUCCESS] Screenshot refreshed
[[14:54:37]] [INFO] Refreshing screenshot...
[[14:54:37]] [INFO] YC6bBrKQgq=pass
[[14:54:32]] [SUCCESS] Screenshot refreshed successfully
[[14:54:32]] [SUCCESS] Screenshot refreshed successfully
[[14:54:32]] [INFO] YC6bBrKQgq=running
[[14:54:32]] [INFO] Executing action 116/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:54:31]] [SUCCESS] Screenshot refreshed
[[14:54:31]] [INFO] Refreshing screenshot...
[[14:54:31]] [INFO] aRgHcQcLDP=pass
[[14:54:27]] [SUCCESS] Screenshot refreshed successfully
[[14:54:27]] [SUCCESS] Screenshot refreshed successfully
[[14:54:27]] [INFO] aRgHcQcLDP=running
[[14:54:27]] [INFO] Executing action 115/591: iOS Function: text - Text: "uno card"
[[14:54:26]] [SUCCESS] Screenshot refreshed
[[14:54:26]] [INFO] Refreshing screenshot...
[[14:54:26]] [INFO] 4PZC1vVWJW=pass
[[14:54:21]] [SUCCESS] Screenshot refreshed successfully
[[14:54:21]] [SUCCESS] Screenshot refreshed successfully
[[14:54:20]] [INFO] 4PZC1vVWJW=running
[[14:54:20]] [INFO] Executing action 114/591: Tap on Text: "Find"
[[14:54:20]] [SUCCESS] Screenshot refreshed
[[14:54:20]] [INFO] Refreshing screenshot...
[[14:54:20]] [INFO] lCSewtjn1z=pass
[[14:54:15]] [SUCCESS] Screenshot refreshed successfully
[[14:54:15]] [SUCCESS] Screenshot refreshed successfully
[[14:54:14]] [INFO] lCSewtjn1z=running
[[14:54:14]] [INFO] Executing action 113/591: Restart app: env[appid]
[[14:54:14]] [SUCCESS] Screenshot refreshed
[[14:54:14]] [INFO] Refreshing screenshot...
[[14:54:14]] [INFO] A1Wz7p1iVG=pass
[[14:54:09]] [SUCCESS] Screenshot refreshed successfully
[[14:54:09]] [SUCCESS] Screenshot refreshed successfully
[[14:54:09]] [INFO] A1Wz7p1iVG=running
[[14:54:09]] [INFO] Executing action 112/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:54:09]] [SUCCESS] Screenshot refreshed
[[14:54:09]] [INFO] Refreshing screenshot...
[[14:54:09]] [INFO] ehyLmdZWP2=pass
[[14:54:01]] [SUCCESS] Screenshot refreshed successfully
[[14:54:01]] [SUCCESS] Screenshot refreshed successfully
[[14:54:01]] [INFO] ehyLmdZWP2=running
[[14:54:01]] [INFO] Executing action 111/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:54:00]] [SUCCESS] Screenshot refreshed
[[14:54:00]] [INFO] Refreshing screenshot...
[[14:54:00]] [INFO] ydRnBBO1vR=pass
[[14:53:57]] [SUCCESS] Screenshot refreshed successfully
[[14:53:57]] [SUCCESS] Screenshot refreshed successfully
[[14:53:56]] [INFO] ydRnBBO1vR=running
[[14:53:56]] [INFO] Executing action 110/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:53:55]] [SUCCESS] Screenshot refreshed
[[14:53:55]] [INFO] Refreshing screenshot...
[[14:53:55]] [INFO] quZwUwj3a8=pass
[[14:53:52]] [SUCCESS] Screenshot refreshed successfully
[[14:53:52]] [SUCCESS] Screenshot refreshed successfully
[[14:53:51]] [INFO] quZwUwj3a8=running
[[14:53:51]] [INFO] Executing action 109/591: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[14:53:51]] [SUCCESS] Screenshot refreshed
[[14:53:51]] [INFO] Refreshing screenshot...
[[14:53:51]] [INFO] FHRlQXe58T=pass
[[14:53:47]] [SUCCESS] Screenshot refreshed successfully
[[14:53:47]] [SUCCESS] Screenshot refreshed successfully
[[14:53:46]] [INFO] FHRlQXe58T=running
[[14:53:46]] [INFO] Executing action 108/591: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:53:46]] [SUCCESS] Screenshot refreshed
[[14:53:46]] [INFO] Refreshing screenshot...
[[14:53:46]] [INFO] FHRlQXe58T=pass
[[14:53:41]] [SUCCESS] Screenshot refreshed successfully
[[14:53:41]] [SUCCESS] Screenshot refreshed successfully
[[14:53:41]] [INFO] FHRlQXe58T=running
[[14:53:41]] [INFO] Executing action 107/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[14:53:41]] [SUCCESS] Screenshot refreshed
[[14:53:41]] [INFO] Refreshing screenshot...
[[14:53:41]] [INFO] N9sXy9WltY=pass
[[14:53:38]] [SUCCESS] Screenshot refreshed successfully
[[14:53:38]] [SUCCESS] Screenshot refreshed successfully
[[14:53:38]] [INFO] N9sXy9WltY=running
[[14:53:38]] [INFO] Executing action 106/591: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[14:53:37]] [SUCCESS] Screenshot refreshed
[[14:53:37]] [INFO] Refreshing screenshot...
[[14:53:37]] [INFO] 8uojw2klHA=pass
[[14:53:31]] [SUCCESS] Screenshot refreshed successfully
[[14:53:31]] [SUCCESS] Screenshot refreshed successfully
[[14:53:31]] [INFO] 8uojw2klHA=running
[[14:53:31]] [INFO] Executing action 105/591: iOS Function: text - Text: "env[pwd]"
[[14:53:30]] [SUCCESS] Screenshot refreshed
[[14:53:30]] [INFO] Refreshing screenshot...
[[14:53:30]] [INFO] SHaIduBnay=pass
[[14:53:26]] [SUCCESS] Screenshot refreshed successfully
[[14:53:26]] [SUCCESS] Screenshot refreshed successfully
[[14:53:26]] [INFO] SHaIduBnay=running
[[14:53:26]] [INFO] Executing action 104/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:53:25]] [SUCCESS] Screenshot refreshed
[[14:53:25]] [INFO] Refreshing screenshot...
[[14:53:25]] [INFO] TGoXyeQtB7=pass
[[14:53:20]] [SUCCESS] Screenshot refreshed successfully
[[14:53:20]] [SUCCESS] Screenshot refreshed successfully
[[14:53:20]] [INFO] TGoXyeQtB7=running
[[14:53:20]] [INFO] Executing action 103/591: iOS Function: text - Text: "env[uname]"
[[14:53:20]] [SUCCESS] Screenshot refreshed
[[14:53:20]] [INFO] Refreshing screenshot...
[[14:53:20]] [INFO] rLCI6NVxSc=pass
[[14:53:16]] [SUCCESS] Screenshot refreshed successfully
[[14:53:16]] [SUCCESS] Screenshot refreshed successfully
[[14:53:16]] [INFO] rLCI6NVxSc=running
[[14:53:16]] [INFO] Executing action 102/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:53:15]] [SUCCESS] Screenshot refreshed
[[14:53:15]] [INFO] Refreshing screenshot...
[[14:53:15]] [INFO] 6mHVWI3j5e=pass
[[14:53:11]] [SUCCESS] Screenshot refreshed successfully
[[14:53:11]] [SUCCESS] Screenshot refreshed successfully
[[14:53:11]] [INFO] 6mHVWI3j5e=running
[[14:53:11]] [INFO] Executing action 101/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:53:11]] [SUCCESS] Screenshot refreshed
[[14:53:11]] [INFO] Refreshing screenshot...
[[14:53:11]] [INFO] rJVGLpLWM3=pass
[[14:53:08]] [SUCCESS] Screenshot refreshed successfully
[[14:53:08]] [SUCCESS] Screenshot refreshed successfully
[[14:53:08]] [INFO] rJVGLpLWM3=running
[[14:53:08]] [INFO] Executing action 100/591: iOS Function: alert_accept
[[14:53:07]] [SUCCESS] Screenshot refreshed
[[14:53:07]] [INFO] Refreshing screenshot...
[[14:53:07]] [INFO] WlISsMf9QA=pass
[[14:53:04]] [SUCCESS] Screenshot refreshed successfully
[[14:53:04]] [SUCCESS] Screenshot refreshed successfully
[[14:53:03]] [INFO] WlISsMf9QA=running
[[14:53:03]] [INFO] Executing action 99/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[14:53:03]] [SUCCESS] Screenshot refreshed
[[14:53:03]] [INFO] Refreshing screenshot...
[[14:53:03]] [INFO] IvqPpScAJa=pass
[[14:52:59]] [SUCCESS] Screenshot refreshed successfully
[[14:52:59]] [SUCCESS] Screenshot refreshed successfully
[[14:52:58]] [INFO] IvqPpScAJa=running
[[14:52:58]] [INFO] Executing action 98/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:52:58]] [SUCCESS] Screenshot refreshed
[[14:52:58]] [INFO] Refreshing screenshot...
[[14:52:58]] [INFO] bGo3feCwBQ=pass
[[14:52:53]] [SUCCESS] Screenshot refreshed successfully
[[14:52:53]] [SUCCESS] Screenshot refreshed successfully
[[14:52:53]] [INFO] bGo3feCwBQ=running
[[14:52:53]] [INFO] Executing action 97/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:52:53]] [SUCCESS] Screenshot refreshed
[[14:52:53]] [INFO] Refreshing screenshot...
[[14:52:53]] [INFO] 4WfPFN961S=pass
[[14:52:46]] [SUCCESS] Screenshot refreshed successfully
[[14:52:46]] [SUCCESS] Screenshot refreshed successfully
[[14:52:45]] [INFO] 4WfPFN961S=running
[[14:52:45]] [INFO] Executing action 96/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:52:45]] [SUCCESS] Screenshot refreshed
[[14:52:45]] [INFO] Refreshing screenshot...
[[14:52:45]] [INFO] F0gZF1jEnT=pass
[[14:52:41]] [SUCCESS] Screenshot refreshed successfully
[[14:52:41]] [SUCCESS] Screenshot refreshed successfully
[[14:52:41]] [INFO] F0gZF1jEnT=running
[[14:52:41]] [INFO] Executing action 95/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:52:40]] [SUCCESS] Screenshot refreshed
[[14:52:40]] [INFO] Refreshing screenshot...
[[14:52:40]] [INFO] EDHl0X27Wi=pass
[[14:52:37]] [SUCCESS] Screenshot refreshed successfully
[[14:52:37]] [SUCCESS] Screenshot refreshed successfully
[[14:52:36]] [INFO] EDHl0X27Wi=running
[[14:52:36]] [INFO] Executing action 94/591: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[14:52:36]] [SUCCESS] Screenshot refreshed
[[14:52:36]] [INFO] Refreshing screenshot...
[[14:52:36]] [INFO] j8NXU87gV3=pass
[[14:52:30]] [SUCCESS] Screenshot refreshed successfully
[[14:52:30]] [SUCCESS] Screenshot refreshed successfully
[[14:52:30]] [INFO] j8NXU87gV3=running
[[14:52:30]] [INFO] Executing action 93/591: iOS Function: text - Text: "env[pwd]"
[[14:52:29]] [SUCCESS] Screenshot refreshed
[[14:52:29]] [INFO] Refreshing screenshot...
[[14:52:29]] [INFO] dpVaKL19uc=pass
[[14:52:25]] [SUCCESS] Screenshot refreshed successfully
[[14:52:25]] [SUCCESS] Screenshot refreshed successfully
[[14:52:25]] [INFO] dpVaKL19uc=running
[[14:52:25]] [INFO] Executing action 92/591: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:52:24]] [SUCCESS] Screenshot refreshed
[[14:52:24]] [INFO] Refreshing screenshot...
[[14:52:24]] [INFO] eOm1WExcrK=pass
[[14:52:19]] [SUCCESS] Screenshot refreshed successfully
[[14:52:19]] [SUCCESS] Screenshot refreshed successfully
[[14:52:19]] [INFO] eOm1WExcrK=running
[[14:52:19]] [INFO] Executing action 91/591: iOS Function: text - Text: "env[uname]"
[[14:52:19]] [SUCCESS] Screenshot refreshed
[[14:52:19]] [INFO] Refreshing screenshot...
[[14:52:19]] [INFO] 50Z2jrodNd=pass
[[14:52:15]] [SUCCESS] Screenshot refreshed successfully
[[14:52:15]] [SUCCESS] Screenshot refreshed successfully
[[14:52:14]] [INFO] 50Z2jrodNd=running
[[14:52:14]] [INFO] Executing action 90/591: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:52:14]] [SUCCESS] Screenshot refreshed
[[14:52:14]] [INFO] Refreshing screenshot...
[[14:52:14]] [INFO] eJnHS9n9VL=pass
[[14:52:10]] [SUCCESS] Screenshot refreshed successfully
[[14:52:10]] [SUCCESS] Screenshot refreshed successfully
[[14:52:10]] [INFO] eJnHS9n9VL=running
[[14:52:10]] [INFO] Executing action 89/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:52:10]] [SUCCESS] Screenshot refreshed
[[14:52:10]] [INFO] Refreshing screenshot...
[[14:52:10]] [INFO] XuLgjNG74w=pass
[[14:52:07]] [SUCCESS] Screenshot refreshed successfully
[[14:52:07]] [SUCCESS] Screenshot refreshed successfully
[[14:52:07]] [INFO] XuLgjNG74w=running
[[14:52:07]] [INFO] Executing action 88/591: iOS Function: alert_accept
[[14:52:06]] [SUCCESS] Screenshot refreshed
[[14:52:06]] [INFO] Refreshing screenshot...
[[14:52:06]] [INFO] qA1ap4n1m4=pass
[[14:51:58]] [SUCCESS] Screenshot refreshed successfully
[[14:51:58]] [SUCCESS] Screenshot refreshed successfully
[[14:51:58]] [INFO] qA1ap4n1m4=running
[[14:51:58]] [INFO] Executing action 87/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:51:57]] [SUCCESS] Screenshot refreshed
[[14:51:57]] [INFO] Refreshing screenshot...
[[14:51:57]] [INFO] JXFxYCr98V=pass
[[14:51:44]] [SUCCESS] Screenshot refreshed successfully
[[14:51:44]] [SUCCESS] Screenshot refreshed successfully
[[14:51:43]] [INFO] JXFxYCr98V=running
[[14:51:43]] [INFO] Executing action 86/591: Restart app: env[appid]
[[14:51:43]] [SUCCESS] Screenshot refreshed
[[14:51:43]] [INFO] Refreshing screenshot...
[[14:51:42]] [SUCCESS] Screenshot refreshed
[[14:51:42]] [INFO] Refreshing screenshot...
[[14:51:39]] [SUCCESS] Screenshot refreshed successfully
[[14:51:39]] [SUCCESS] Screenshot refreshed successfully
[[14:51:39]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:51:39]] [SUCCESS] Screenshot refreshed
[[14:51:39]] [INFO] Refreshing screenshot...
[[14:51:27]] [SUCCESS] Screenshot refreshed successfully
[[14:51:27]] [SUCCESS] Screenshot refreshed successfully
[[14:51:27]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:51:26]] [SUCCESS] Screenshot refreshed
[[14:51:26]] [INFO] Refreshing screenshot...
[[14:51:22]] [SUCCESS] Screenshot refreshed successfully
[[14:51:22]] [SUCCESS] Screenshot refreshed successfully
[[14:51:22]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:51:22]] [SUCCESS] Screenshot refreshed
[[14:51:22]] [INFO] Refreshing screenshot...
[[14:51:18]] [SUCCESS] Screenshot refreshed successfully
[[14:51:18]] [SUCCESS] Screenshot refreshed successfully
[[14:51:17]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:51:17]] [SUCCESS] Screenshot refreshed
[[14:51:17]] [INFO] Refreshing screenshot...
[[14:51:10]] [SUCCESS] Screenshot refreshed successfully
[[14:51:10]] [SUCCESS] Screenshot refreshed successfully
[[14:51:10]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:51:09]] [SUCCESS] Screenshot refreshed
[[14:51:09]] [INFO] Refreshing screenshot...
[[14:51:04]] [SUCCESS] Screenshot refreshed successfully
[[14:51:04]] [SUCCESS] Screenshot refreshed successfully
[[14:51:02]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:51:02]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:51:02]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:51:02]] [INFO] hbIlJIWlVN=running
[[14:51:02]] [INFO] Executing action 85/591: cleanupSteps action
[[14:51:02]] [SUCCESS] Screenshot refreshed
[[14:51:02]] [INFO] Refreshing screenshot...
[[14:51:01]] [SUCCESS] Screenshot refreshed
[[14:51:01]] [INFO] Refreshing screenshot...
[[14:50:57]] [SUCCESS] Screenshot refreshed successfully
[[14:50:57]] [SUCCESS] Screenshot refreshed successfully
[[14:50:57]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[14:50:56]] [SUCCESS] Screenshot refreshed
[[14:50:56]] [INFO] Refreshing screenshot...
[[14:50:52]] [SUCCESS] Screenshot refreshed successfully
[[14:50:52]] [SUCCESS] Screenshot refreshed successfully
[[14:50:52]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:50:52]] [SUCCESS] Screenshot refreshed
[[14:50:52]] [INFO] Refreshing screenshot...
[[14:50:47]] [SUCCESS] Screenshot refreshed successfully
[[14:50:47]] [SUCCESS] Screenshot refreshed successfully
[[14:50:47]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:50:46]] [SUCCESS] Screenshot refreshed
[[14:50:46]] [INFO] Refreshing screenshot...
[[14:50:42]] [SUCCESS] Screenshot refreshed successfully
[[14:50:42]] [SUCCESS] Screenshot refreshed successfully
[[14:50:42]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:50:41]] [SUCCESS] Screenshot refreshed
[[14:50:41]] [INFO] Refreshing screenshot...
[[14:50:37]] [SUCCESS] Screenshot refreshed successfully
[[14:50:37]] [SUCCESS] Screenshot refreshed successfully
[[14:50:37]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[14:50:37]] [SUCCESS] Screenshot refreshed
[[14:50:37]] [INFO] Refreshing screenshot...
[[14:50:26]] [SUCCESS] Screenshot refreshed successfully
[[14:50:26]] [SUCCESS] Screenshot refreshed successfully
[[14:50:26]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[14:50:26]] [SUCCESS] Screenshot refreshed
[[14:50:26]] [INFO] Refreshing screenshot...
[[14:50:22]] [SUCCESS] Screenshot refreshed successfully
[[14:50:22]] [SUCCESS] Screenshot refreshed successfully
[[14:50:21]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[14:50:21]] [SUCCESS] Screenshot refreshed
[[14:50:21]] [INFO] Refreshing screenshot...
[[14:50:17]] [SUCCESS] Screenshot refreshed successfully
[[14:50:17]] [SUCCESS] Screenshot refreshed successfully
[[14:50:16]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[14:50:16]] [SUCCESS] Screenshot refreshed
[[14:50:16]] [INFO] Refreshing screenshot...
[[14:50:09]] [SUCCESS] Screenshot refreshed successfully
[[14:50:09]] [SUCCESS] Screenshot refreshed successfully
[[14:50:09]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[14:50:08]] [SUCCESS] Screenshot refreshed
[[14:50:08]] [INFO] Refreshing screenshot...
[[14:50:02]] [SUCCESS] Screenshot refreshed successfully
[[14:50:02]] [SUCCESS] Screenshot refreshed successfully
[[14:50:02]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[14:50:01]] [SUCCESS] Screenshot refreshed
[[14:50:01]] [INFO] Refreshing screenshot...
[[14:49:57]] [SUCCESS] Screenshot refreshed successfully
[[14:49:57]] [SUCCESS] Screenshot refreshed successfully
[[14:49:57]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[14:49:56]] [SUCCESS] Screenshot refreshed
[[14:49:56]] [INFO] Refreshing screenshot...
[[14:49:52]] [SUCCESS] Screenshot refreshed successfully
[[14:49:52]] [SUCCESS] Screenshot refreshed successfully
[[14:49:52]] [INFO] Executing Multi Step action step 25/36: textClear action
[[14:49:51]] [SUCCESS] Screenshot refreshed
[[14:49:51]] [INFO] Refreshing screenshot...
[[14:49:47]] [SUCCESS] Screenshot refreshed successfully
[[14:49:47]] [SUCCESS] Screenshot refreshed successfully
[[14:49:47]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[14:49:47]] [SUCCESS] Screenshot refreshed
[[14:49:47]] [INFO] Refreshing screenshot...
[[14:49:42]] [SUCCESS] Screenshot refreshed successfully
[[14:49:42]] [SUCCESS] Screenshot refreshed successfully
[[14:49:42]] [INFO] Executing Multi Step action step 23/36: textClear action
[[14:49:41]] [SUCCESS] Screenshot refreshed
[[14:49:41]] [INFO] Refreshing screenshot...
[[14:49:37]] [SUCCESS] Screenshot refreshed successfully
[[14:49:37]] [SUCCESS] Screenshot refreshed successfully
[[14:49:37]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:49:37]] [SUCCESS] Screenshot refreshed
[[14:49:37]] [INFO] Refreshing screenshot...
[[14:49:31]] [SUCCESS] Screenshot refreshed successfully
[[14:49:31]] [SUCCESS] Screenshot refreshed successfully
[[14:49:31]] [INFO] Executing Multi Step action step 21/36: textClear action
[[14:49:30]] [SUCCESS] Screenshot refreshed
[[14:49:30]] [INFO] Refreshing screenshot...
[[14:49:26]] [SUCCESS] Screenshot refreshed successfully
[[14:49:26]] [SUCCESS] Screenshot refreshed successfully
[[14:49:26]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[14:49:26]] [SUCCESS] Screenshot refreshed
[[14:49:26]] [INFO] Refreshing screenshot...
[[14:49:21]] [SUCCESS] Screenshot refreshed successfully
[[14:49:21]] [SUCCESS] Screenshot refreshed successfully
[[14:49:21]] [INFO] Executing Multi Step action step 19/36: textClear action
[[14:49:21]] [SUCCESS] Screenshot refreshed
[[14:49:21]] [INFO] Refreshing screenshot...
[[14:49:17]] [SUCCESS] Screenshot refreshed successfully
[[14:49:17]] [SUCCESS] Screenshot refreshed successfully
[[14:49:17]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[14:49:16]] [SUCCESS] Screenshot refreshed
[[14:49:16]] [INFO] Refreshing screenshot...
[[14:49:12]] [SUCCESS] Screenshot refreshed successfully
[[14:49:12]] [SUCCESS] Screenshot refreshed successfully
[[14:49:12]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[14:49:11]] [SUCCESS] Screenshot refreshed
[[14:49:11]] [INFO] Refreshing screenshot...
[[14:49:00]] [SUCCESS] Screenshot refreshed successfully
[[14:49:00]] [SUCCESS] Screenshot refreshed successfully
[[14:49:00]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[14:48:59]] [SUCCESS] Screenshot refreshed
[[14:48:59]] [INFO] Refreshing screenshot...
[[14:48:55]] [SUCCESS] Screenshot refreshed successfully
[[14:48:55]] [SUCCESS] Screenshot refreshed successfully
[[14:48:55]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[14:48:55]] [SUCCESS] Screenshot refreshed
[[14:48:55]] [INFO] Refreshing screenshot...
[[14:48:51]] [SUCCESS] Screenshot refreshed successfully
[[14:48:51]] [SUCCESS] Screenshot refreshed successfully
[[14:48:51]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:48:50]] [SUCCESS] Screenshot refreshed
[[14:48:50]] [INFO] Refreshing screenshot...
[[14:48:45]] [SUCCESS] Screenshot refreshed successfully
[[14:48:45]] [SUCCESS] Screenshot refreshed successfully
[[14:48:45]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:48:45]] [SUCCESS] Screenshot refreshed
[[14:48:45]] [INFO] Refreshing screenshot...
[[14:48:41]] [SUCCESS] Screenshot refreshed successfully
[[14:48:41]] [SUCCESS] Screenshot refreshed successfully
[[14:48:40]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:48:40]] [SUCCESS] Screenshot refreshed
[[14:48:40]] [INFO] Refreshing screenshot...
[[14:48:32]] [SUCCESS] Screenshot refreshed successfully
[[14:48:32]] [SUCCESS] Screenshot refreshed successfully
[[14:48:31]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[14:48:30]] [SUCCESS] Screenshot refreshed
[[14:48:30]] [INFO] Refreshing screenshot...
[[14:48:27]] [SUCCESS] Screenshot refreshed successfully
[[14:48:27]] [SUCCESS] Screenshot refreshed successfully
[[14:48:26]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:48:25]] [SUCCESS] Screenshot refreshed
[[14:48:25]] [INFO] Refreshing screenshot...
[[14:48:21]] [SUCCESS] Screenshot refreshed successfully
[[14:48:21]] [SUCCESS] Screenshot refreshed successfully
[[14:48:21]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[14:48:21]] [SUCCESS] Screenshot refreshed
[[14:48:21]] [INFO] Refreshing screenshot...
[[14:48:15]] [SUCCESS] Screenshot refreshed successfully
[[14:48:15]] [SUCCESS] Screenshot refreshed successfully
[[14:48:14]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[14:48:14]] [SUCCESS] Screenshot refreshed
[[14:48:14]] [INFO] Refreshing screenshot...
[[14:47:52]] [SUCCESS] Screenshot refreshed successfully
[[14:47:52]] [SUCCESS] Screenshot refreshed successfully
[[14:47:51]] [INFO] Executing Multi Step action step 7/36: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[14:47:50]] [SUCCESS] Screenshot refreshed
[[14:47:50]] [INFO] Refreshing screenshot...
[[14:47:38]] [SUCCESS] Screenshot refreshed successfully
[[14:47:38]] [SUCCESS] Screenshot refreshed successfully
[[14:47:37]] [INFO] Executing Multi Step action step 6/36: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[14:47:37]] [SUCCESS] Screenshot refreshed
[[14:47:37]] [INFO] Refreshing screenshot...
[[14:47:10]] [SUCCESS] Screenshot refreshed successfully
[[14:47:10]] [SUCCESS] Screenshot refreshed successfully
[[14:47:09]] [INFO] Executing Multi Step action step 5/36: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[14:47:09]] [SUCCESS] Screenshot refreshed
[[14:47:09]] [INFO] Refreshing screenshot...
[[14:47:04]] [SUCCESS] Screenshot refreshed successfully
[[14:47:04]] [SUCCESS] Screenshot refreshed successfully
[[14:47:03]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[14:47:03]] [SUCCESS] Screenshot refreshed
[[14:47:03]] [INFO] Refreshing screenshot...
[[14:46:57]] [SUCCESS] Screenshot refreshed successfully
[[14:46:57]] [SUCCESS] Screenshot refreshed successfully
[[14:46:57]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[14:46:57]] [SUCCESS] Screenshot refreshed
[[14:46:57]] [INFO] Refreshing screenshot...
[[14:46:52]] [SUCCESS] Screenshot refreshed successfully
[[14:46:52]] [SUCCESS] Screenshot refreshed successfully
[[14:46:52]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[14:46:51]] [SUCCESS] Screenshot refreshed
[[14:46:51]] [INFO] Refreshing screenshot...
[[14:46:44]] [SUCCESS] Screenshot refreshed successfully
[[14:46:44]] [SUCCESS] Screenshot refreshed successfully
[[14:46:43]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[14:46:43]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[14:46:43]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[14:46:43]] [INFO] aI4Cfo88Pv=running
[[14:46:43]] [INFO] Executing action 84/591: Execute Test Case: Delivery  Buy (34 steps)
[[14:46:43]] [SUCCESS] Screenshot refreshed
[[14:46:43]] [INFO] Refreshing screenshot...
[[14:46:43]] [INFO] cKNu2QoRC1=pass
[[14:46:39]] [SUCCESS] Screenshot refreshed successfully
[[14:46:39]] [SUCCESS] Screenshot refreshed successfully
[[14:46:38]] [INFO] cKNu2QoRC1=running
[[14:46:38]] [INFO] Executing action 83/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:46:38]] [SUCCESS] Screenshot refreshed
[[14:46:38]] [INFO] Refreshing screenshot...
[[14:46:38]] [INFO] OyUowAaBzD=pass
[[14:46:33]] [SUCCESS] Screenshot refreshed successfully
[[14:46:33]] [SUCCESS] Screenshot refreshed successfully
[[14:46:32]] [INFO] OyUowAaBzD=running
[[14:46:32]] [INFO] Executing action 82/591: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:46:32]] [SUCCESS] Screenshot refreshed
[[14:46:32]] [INFO] Refreshing screenshot...
[[14:46:32]] [INFO] Ob26qqcA0p=pass
[[14:46:25]] [SUCCESS] Screenshot refreshed successfully
[[14:46:25]] [SUCCESS] Screenshot refreshed successfully
[[14:46:25]] [INFO] Ob26qqcA0p=running
[[14:46:25]] [INFO] Executing action 81/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:46:24]] [SUCCESS] Screenshot refreshed
[[14:46:24]] [INFO] Refreshing screenshot...
[[14:46:24]] [INFO] k3mu9Mt7Ec=pass
[[14:46:20]] [INFO] k3mu9Mt7Ec=running
[[14:46:20]] [INFO] Executing action 80/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:46:20]] [SUCCESS] Screenshot refreshed successfully
[[14:46:20]] [SUCCESS] Screenshot refreshed successfully
[[14:46:20]] [SUCCESS] Screenshot refreshed
[[14:46:20]] [INFO] Refreshing screenshot...
[[14:46:20]] [INFO] 8umPSX0vrr=pass
[[14:46:16]] [INFO] 8umPSX0vrr=running
[[14:46:16]] [INFO] Executing action 79/591: Tap on image: banner-close-updated.png
[[14:46:16]] [SUCCESS] Screenshot refreshed successfully
[[14:46:16]] [SUCCESS] Screenshot refreshed successfully
[[14:46:16]] [SUCCESS] Screenshot refreshed
[[14:46:16]] [INFO] Refreshing screenshot...
[[14:46:16]] [INFO] pr9o8Zsm5p=pass
[[14:46:12]] [SUCCESS] Screenshot refreshed successfully
[[14:46:12]] [SUCCESS] Screenshot refreshed successfully
[[14:46:12]] [INFO] pr9o8Zsm5p=running
[[14:46:12]] [INFO] Executing action 78/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:46:11]] [SUCCESS] Screenshot refreshed
[[14:46:11]] [INFO] Refreshing screenshot...
[[14:46:11]] [INFO] Qbg9bipTGs=pass
[[14:46:07]] [SUCCESS] Screenshot refreshed successfully
[[14:46:07]] [SUCCESS] Screenshot refreshed successfully
[[14:46:06]] [INFO] Qbg9bipTGs=running
[[14:46:06]] [INFO] Executing action 77/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:46:06]] [SUCCESS] Screenshot refreshed
[[14:46:06]] [INFO] Refreshing screenshot...
[[14:46:06]] [INFO] qjj0i3rcUh=pass
[[14:46:00]] [INFO] qjj0i3rcUh=running
[[14:46:00]] [INFO] Executing action 76/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[14:46:00]] [SUCCESS] Screenshot refreshed successfully
[[14:46:00]] [SUCCESS] Screenshot refreshed successfully
[[14:46:00]] [SUCCESS] Screenshot refreshed
[[14:46:00]] [INFO] Refreshing screenshot...
[[14:46:00]] [INFO] 42Jm6o7r1t=pass
[[14:45:55]] [SUCCESS] Screenshot refreshed successfully
[[14:45:55]] [SUCCESS] Screenshot refreshed successfully
[[14:45:55]] [INFO] 42Jm6o7r1t=running
[[14:45:55]] [INFO] Executing action 75/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:45:54]] [SUCCESS] Screenshot refreshed
[[14:45:54]] [INFO] Refreshing screenshot...
[[14:45:54]] [INFO] lWIRxRm6HE=pass
[[14:45:50]] [SUCCESS] Screenshot refreshed successfully
[[14:45:50]] [SUCCESS] Screenshot refreshed successfully
[[14:45:50]] [INFO] lWIRxRm6HE=running
[[14:45:50]] [INFO] Executing action 74/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:45:50]] [SUCCESS] Screenshot refreshed
[[14:45:50]] [INFO] Refreshing screenshot...
[[14:45:50]] [INFO] Q0fomJIDoQ=pass
[[14:45:46]] [SUCCESS] Screenshot refreshed successfully
[[14:45:46]] [SUCCESS] Screenshot refreshed successfully
[[14:45:46]] [INFO] Q0fomJIDoQ=running
[[14:45:46]] [INFO] Executing action 73/591: Tap on image: banner-close-updated.png
[[14:45:45]] [SUCCESS] Screenshot refreshed
[[14:45:45]] [INFO] Refreshing screenshot...
[[14:45:45]] [INFO] 7SpDO20tS2=pass
[[14:45:34]] [SUCCESS] Screenshot refreshed successfully
[[14:45:34]] [SUCCESS] Screenshot refreshed successfully
[[14:45:34]] [INFO] 7SpDO20tS2=running
[[14:45:34]] [INFO] Executing action 72/591: Wait for 10 ms
[[14:45:33]] [SUCCESS] Screenshot refreshed
[[14:45:33]] [INFO] Refreshing screenshot...
[[14:45:33]] [INFO] FKZs2qCWoU=pass
[[14:45:27]] [SUCCESS] Screenshot refreshed successfully
[[14:45:27]] [SUCCESS] Screenshot refreshed successfully
[[14:45:27]] [INFO] FKZs2qCWoU=running
[[14:45:27]] [INFO] Executing action 71/591: Tap on Text: "Brunswick"
[[14:45:27]] [SUCCESS] Screenshot refreshed
[[14:45:27]] [INFO] Refreshing screenshot...
[[14:45:27]] [INFO] Qbg9bipTGs=pass
[[14:45:22]] [SUCCESS] Screenshot refreshed successfully
[[14:45:22]] [SUCCESS] Screenshot refreshed successfully
[[14:45:22]] [INFO] Qbg9bipTGs=running
[[14:45:22]] [INFO] Executing action 70/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:45:22]] [SUCCESS] Screenshot refreshed
[[14:45:22]] [INFO] Refreshing screenshot...
[[14:45:22]] [INFO] qjj0i3rcUh=pass
[[14:45:17]] [SUCCESS] Screenshot refreshed successfully
[[14:45:17]] [SUCCESS] Screenshot refreshed successfully
[[14:45:17]] [INFO] qjj0i3rcUh=running
[[14:45:17]] [INFO] Executing action 69/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[14:45:17]] [SUCCESS] Screenshot refreshed
[[14:45:17]] [INFO] Refreshing screenshot...
[[14:45:17]] [INFO] uM5FOSrU5U=pass
[[14:45:14]] [INFO] uM5FOSrU5U=running
[[14:45:14]] [INFO] Executing action 68/591: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[14:45:14]] [SUCCESS] Screenshot refreshed successfully
[[14:45:14]] [SUCCESS] Screenshot refreshed successfully
[[14:45:13]] [SUCCESS] Screenshot refreshed
[[14:45:13]] [INFO] Refreshing screenshot...
[[14:45:13]] [INFO] QB2bKb0SsP=pass
[[14:45:08]] [SUCCESS] Screenshot refreshed successfully
[[14:45:08]] [SUCCESS] Screenshot refreshed successfully
[[14:45:08]] [INFO] QB2bKb0SsP=running
[[14:45:08]] [INFO] Executing action 67/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:45:08]] [SUCCESS] Screenshot refreshed
[[14:45:08]] [INFO] Refreshing screenshot...
[[14:45:08]] [INFO] F1olhgKhUt=pass
[[14:45:06]] [SUCCESS] Screenshot refreshed successfully
[[14:45:06]] [SUCCESS] Screenshot refreshed successfully
[[14:45:03]] [INFO] F1olhgKhUt=running
[[14:45:03]] [INFO] Executing action 66/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:45:02]] [SUCCESS] Screenshot refreshed
[[14:45:02]] [INFO] Refreshing screenshot...
[[14:45:02]] [INFO] jY0oPjKbuS=pass
[[14:44:59]] [SUCCESS] Screenshot refreshed successfully
[[14:44:59]] [SUCCESS] Screenshot refreshed successfully
[[14:44:58]] [INFO] jY0oPjKbuS=running
[[14:44:58]] [INFO] Executing action 65/591: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[14:44:58]] [SUCCESS] Screenshot refreshed
[[14:44:58]] [INFO] Refreshing screenshot...
[[14:44:58]] [INFO] FnrbyHq7bU=pass
[[14:44:51]] [SUCCESS] Screenshot refreshed successfully
[[14:44:51]] [SUCCESS] Screenshot refreshed successfully
[[14:44:51]] [INFO] FnrbyHq7bU=running
[[14:44:51]] [INFO] Executing action 64/591: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[14:44:50]] [SUCCESS] Screenshot refreshed
[[14:44:50]] [INFO] Refreshing screenshot...
[[14:44:50]] [INFO] nAB6Q8LAdv=pass
[[14:44:47]] [SUCCESS] Screenshot refreshed successfully
[[14:44:47]] [SUCCESS] Screenshot refreshed successfully
[[14:44:46]] [INFO] nAB6Q8LAdv=running
[[14:44:46]] [INFO] Executing action 63/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:44:46]] [SUCCESS] Screenshot refreshed
[[14:44:46]] [INFO] Refreshing screenshot...
[[14:44:46]] [INFO] sc2KH9bG6H=pass
[[14:44:41]] [SUCCESS] Screenshot refreshed successfully
[[14:44:41]] [SUCCESS] Screenshot refreshed successfully
[[14:44:41]] [INFO] sc2KH9bG6H=running
[[14:44:41]] [INFO] Executing action 62/591: iOS Function: text - Text: "Uno card"
[[14:44:41]] [SUCCESS] Screenshot refreshed
[[14:44:41]] [INFO] Refreshing screenshot...
[[14:44:41]] [INFO] ZBXCQNlT8z=pass
[[14:44:36]] [SUCCESS] Screenshot refreshed successfully
[[14:44:36]] [SUCCESS] Screenshot refreshed successfully
[[14:44:35]] [INFO] ZBXCQNlT8z=running
[[14:44:35]] [INFO] Executing action 61/591: Tap on Text: "Find"
[[14:44:35]] [SUCCESS] Screenshot refreshed
[[14:44:35]] [INFO] Refreshing screenshot...
[[14:44:34]] [SUCCESS] Screenshot refreshed
[[14:44:34]] [INFO] Refreshing screenshot...
[[14:44:28]] [SUCCESS] Screenshot refreshed successfully
[[14:44:28]] [SUCCESS] Screenshot refreshed successfully
[[14:44:28]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:44:28]] [SUCCESS] Screenshot refreshed
[[14:44:28]] [INFO] Refreshing screenshot...
[[14:44:23]] [SUCCESS] Screenshot refreshed successfully
[[14:44:23]] [SUCCESS] Screenshot refreshed successfully
[[14:44:23]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:44:23]] [SUCCESS] Screenshot refreshed
[[14:44:23]] [INFO] Refreshing screenshot...
[[14:44:18]] [SUCCESS] Screenshot refreshed successfully
[[14:44:18]] [SUCCESS] Screenshot refreshed successfully
[[14:44:18]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[14:44:17]] [SUCCESS] Screenshot refreshed
[[14:44:17]] [INFO] Refreshing screenshot...
[[14:44:13]] [SUCCESS] Screenshot refreshed successfully
[[14:44:13]] [SUCCESS] Screenshot refreshed successfully
[[14:44:13]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:44:13]] [SUCCESS] Screenshot refreshed
[[14:44:13]] [INFO] Refreshing screenshot...
[[14:44:07]] [SUCCESS] Screenshot refreshed successfully
[[14:44:07]] [SUCCESS] Screenshot refreshed successfully
[[14:44:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:44:07]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:44:07]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:44:07]] [INFO] El6k4IPZly=running
[[14:44:07]] [INFO] Executing action 60/591: Execute Test Case: Kmart-Signin (8 steps)
[[14:44:06]] [SUCCESS] Screenshot refreshed
[[14:44:06]] [INFO] Refreshing screenshot...
[[14:44:06]] [INFO] 3caMBvQX7k=pass
[[14:44:01]] [SUCCESS] Screenshot refreshed successfully
[[14:44:01]] [SUCCESS] Screenshot refreshed successfully
[[14:44:01]] [INFO] 3caMBvQX7k=running
[[14:44:01]] [INFO] Executing action 59/591: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:44:00]] [SUCCESS] Screenshot refreshed
[[14:44:00]] [INFO] Refreshing screenshot...
[[14:44:00]] [INFO] yUJyVO5Wev=pass
[[14:43:58]] [SUCCESS] Screenshot refreshed successfully
[[14:43:58]] [SUCCESS] Screenshot refreshed successfully
[[14:43:57]] [INFO] yUJyVO5Wev=running
[[14:43:57]] [INFO] Executing action 58/591: iOS Function: alert_accept
[[14:43:57]] [SUCCESS] Screenshot refreshed
[[14:43:57]] [INFO] Refreshing screenshot...
[[14:43:57]] [INFO] rkL0oz4kiL=pass
[[14:43:50]] [SUCCESS] Screenshot refreshed successfully
[[14:43:50]] [SUCCESS] Screenshot refreshed successfully
[[14:43:49]] [INFO] rkL0oz4kiL=running
[[14:43:49]] [INFO] Executing action 57/591: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:43:49]] [SUCCESS] Screenshot refreshed
[[14:43:49]] [INFO] Refreshing screenshot...
[[14:43:49]] [INFO] HotUJOd6oB=pass
[[14:43:36]] [SUCCESS] Screenshot refreshed successfully
[[14:43:36]] [SUCCESS] Screenshot refreshed successfully
[[14:43:35]] [INFO] HotUJOd6oB=running
[[14:43:35]] [INFO] Executing action 56/591: Restart app: env[appid]
[[14:43:35]] [SUCCESS] Screenshot refreshed
[[14:43:35]] [INFO] Refreshing screenshot...
[[14:43:34]] [SUCCESS] Screenshot refreshed
[[14:43:34]] [INFO] Refreshing screenshot...
[[14:43:31]] [SUCCESS] Screenshot refreshed successfully
[[14:43:31]] [SUCCESS] Screenshot refreshed successfully
[[14:43:31]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:43:30]] [SUCCESS] Screenshot refreshed
[[14:43:30]] [INFO] Refreshing screenshot...
[[14:43:19]] [SUCCESS] Screenshot refreshed successfully
[[14:43:19]] [SUCCESS] Screenshot refreshed successfully
[[14:43:19]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:43:18]] [SUCCESS] Screenshot refreshed
[[14:43:18]] [INFO] Refreshing screenshot...
[[14:43:14]] [SUCCESS] Screenshot refreshed successfully
[[14:43:14]] [SUCCESS] Screenshot refreshed successfully
[[14:43:14]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:43:14]] [SUCCESS] Screenshot refreshed
[[14:43:14]] [INFO] Refreshing screenshot...
[[14:43:10]] [SUCCESS] Screenshot refreshed successfully
[[14:43:10]] [SUCCESS] Screenshot refreshed successfully
[[14:43:10]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:43:09]] [SUCCESS] Screenshot refreshed
[[14:43:09]] [INFO] Refreshing screenshot...
[[14:43:03]] [SUCCESS] Screenshot refreshed successfully
[[14:43:03]] [SUCCESS] Screenshot refreshed successfully
[[14:43:02]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:43:02]] [SUCCESS] Screenshot refreshed
[[14:43:02]] [INFO] Refreshing screenshot...
[[14:42:56]] [SUCCESS] Screenshot refreshed successfully
[[14:42:56]] [SUCCESS] Screenshot refreshed successfully
[[14:42:55]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:42:55]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:42:55]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:42:55]] [INFO] vKo6Ox3YrP=running
[[14:42:55]] [INFO] Executing action 55/591: cleanupSteps action
[[14:42:55]] [SUCCESS] Screenshot refreshed
[[14:42:55]] [INFO] Refreshing screenshot...
[[14:42:55]] [INFO] x4yLCZHaCR=pass
[[14:42:51]] [INFO] x4yLCZHaCR=running
[[14:42:51]] [INFO] Executing action 54/591: Terminate app: env[appid]
[[14:42:51]] [SUCCESS] Screenshot refreshed successfully
[[14:42:51]] [SUCCESS] Screenshot refreshed successfully
[[14:42:51]] [SUCCESS] Screenshot refreshed
[[14:42:51]] [INFO] Refreshing screenshot...
[[14:42:51]] [INFO] 2p13JoJbbA=pass
[[14:42:47]] [SUCCESS] Screenshot refreshed successfully
[[14:42:47]] [SUCCESS] Screenshot refreshed successfully
[[14:42:47]] [INFO] 2p13JoJbbA=running
[[14:42:47]] [INFO] Executing action 53/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:42:46]] [SUCCESS] Screenshot refreshed
[[14:42:46]] [INFO] Refreshing screenshot...
[[14:42:46]] [INFO] 2p13JoJbbA=pass
[[14:42:41]] [INFO] 2p13JoJbbA=running
[[14:42:41]] [INFO] Executing action 52/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:42:30]] [SUCCESS] Screenshot refreshed successfully
[[14:42:30]] [SUCCESS] Screenshot refreshed successfully
[[14:42:30]] [SUCCESS] Screenshot refreshed
[[14:42:30]] [INFO] Refreshing screenshot...
[[14:42:30]] [INFO] nyBidG0kHp=pass
[[14:42:23]] [INFO] nyBidG0kHp=running
[[14:42:23]] [INFO] Executing action 51/591: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[14:42:21]] [SUCCESS] Screenshot refreshed successfully
[[14:42:21]] [SUCCESS] Screenshot refreshed successfully
[[14:42:20]] [SUCCESS] Screenshot refreshed
[[14:42:20]] [INFO] Refreshing screenshot...
[[14:42:20]] [INFO] w7I4F66YKQ=pass
[[14:42:15]] [SUCCESS] Screenshot refreshed successfully
[[14:42:15]] [SUCCESS] Screenshot refreshed successfully
[[14:42:15]] [INFO] w7I4F66YKQ=running
[[14:42:15]] [INFO] Executing action 50/591: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[14:42:15]] [SUCCESS] Screenshot refreshed
[[14:42:15]] [INFO] Refreshing screenshot...
[[14:42:15]] [INFO] F4NGh9HrLw=pass
[[14:42:10]] [SUCCESS] Screenshot refreshed successfully
[[14:42:10]] [SUCCESS] Screenshot refreshed successfully
[[14:42:10]] [INFO] F4NGh9HrLw=running
[[14:42:10]] [INFO] Executing action 49/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:42:09]] [SUCCESS] Screenshot refreshed
[[14:42:09]] [INFO] Refreshing screenshot...
[[14:42:09]] [INFO] VtMfqK1V9t=pass
[[14:41:48]] [SUCCESS] Screenshot refreshed successfully
[[14:41:48]] [SUCCESS] Screenshot refreshed successfully
[[14:41:48]] [INFO] VtMfqK1V9t=running
[[14:41:48]] [INFO] Executing action 48/591: Tap on element with accessibility_id: Add to bag
[[14:41:47]] [SUCCESS] Screenshot refreshed
[[14:41:47]] [INFO] Refreshing screenshot...
[[14:41:47]] [INFO] NOnuFzXy63=pass
[[14:41:44]] [SUCCESS] Screenshot refreshed successfully
[[14:41:44]] [SUCCESS] Screenshot refreshed successfully
[[14:41:43]] [INFO] NOnuFzXy63=running
[[14:41:43]] [INFO] Executing action 47/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:41:43]] [SUCCESS] Screenshot refreshed
[[14:41:43]] [INFO] Refreshing screenshot...
[[14:41:43]] [INFO] kz9lnCdwoH=pass
[[14:41:38]] [SUCCESS] Screenshot refreshed successfully
[[14:41:38]] [SUCCESS] Screenshot refreshed successfully
[[14:41:38]] [INFO] kz9lnCdwoH=running
[[14:41:38]] [INFO] Executing action 46/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:41:37]] [SUCCESS] Screenshot refreshed
[[14:41:37]] [INFO] Refreshing screenshot...
[[14:41:37]] [INFO] kz9lnCdwoH=pass
[[14:41:33]] [SUCCESS] Screenshot refreshed successfully
[[14:41:33]] [SUCCESS] Screenshot refreshed successfully
[[14:41:32]] [INFO] kz9lnCdwoH=running
[[14:41:32]] [INFO] Executing action 45/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:41:31]] [SUCCESS] Screenshot refreshed
[[14:41:31]] [INFO] Refreshing screenshot...
[[14:41:31]] [INFO] qIF9CVPc56=pass
[[14:41:27]] [SUCCESS] Screenshot refreshed successfully
[[14:41:27]] [SUCCESS] Screenshot refreshed successfully
[[14:41:27]] [INFO] qIF9CVPc56=running
[[14:41:27]] [INFO] Executing action 44/591: iOS Function: text - Text: "mat"
[[14:41:27]] [SUCCESS] Screenshot refreshed
[[14:41:27]] [INFO] Refreshing screenshot...
[[14:41:27]] [INFO] yEga5MkcRe=pass
[[14:41:23]] [SUCCESS] Screenshot refreshed successfully
[[14:41:23]] [SUCCESS] Screenshot refreshed successfully
[[14:41:23]] [INFO] yEga5MkcRe=running
[[14:41:23]] [INFO] Executing action 43/591: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:41:22]] [SUCCESS] Screenshot refreshed
[[14:41:22]] [INFO] Refreshing screenshot...
[[14:41:22]] [INFO] F4NGh9HrLw=pass
[[14:41:18]] [SUCCESS] Screenshot refreshed successfully
[[14:41:18]] [SUCCESS] Screenshot refreshed successfully
[[14:41:18]] [INFO] F4NGh9HrLw=running
[[14:41:18]] [INFO] Executing action 42/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:41:17]] [SUCCESS] Screenshot refreshed
[[14:41:17]] [INFO] Refreshing screenshot...
[[14:41:17]] [INFO] kz9lnCdwoH=pass
[[14:41:13]] [SUCCESS] Screenshot refreshed successfully
[[14:41:13]] [SUCCESS] Screenshot refreshed successfully
[[14:41:13]] [INFO] kz9lnCdwoH=running
[[14:41:13]] [INFO] Executing action 41/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:41:12]] [SUCCESS] Screenshot refreshed
[[14:41:12]] [INFO] Refreshing screenshot...
[[14:41:12]] [INFO] kz9lnCdwoH=pass
[[14:41:09]] [SUCCESS] Screenshot refreshed successfully
[[14:41:09]] [SUCCESS] Screenshot refreshed successfully
[[14:41:08]] [INFO] kz9lnCdwoH=running
[[14:41:08]] [INFO] Executing action 40/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:41:08]] [SUCCESS] Screenshot refreshed
[[14:41:08]] [INFO] Refreshing screenshot...
[[14:41:08]] [INFO] JRheDTvpJf=pass
[[14:41:04]] [SUCCESS] Screenshot refreshed successfully
[[14:41:04]] [SUCCESS] Screenshot refreshed successfully
[[14:41:03]] [INFO] JRheDTvpJf=running
[[14:41:03]] [INFO] Executing action 39/591: iOS Function: text - Text: "Kid toy"
[[14:41:03]] [SUCCESS] Screenshot refreshed
[[14:41:03]] [INFO] Refreshing screenshot...
[[14:41:03]] [INFO] yEga5MkcRe=pass
[[14:40:59]] [SUCCESS] Screenshot refreshed successfully
[[14:40:59]] [SUCCESS] Screenshot refreshed successfully
[[14:40:58]] [INFO] yEga5MkcRe=running
[[14:40:58]] [INFO] Executing action 38/591: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:40:58]] [SUCCESS] Screenshot refreshed
[[14:40:58]] [INFO] Refreshing screenshot...
[[14:40:58]] [INFO] F4NGh9HrLw=pass
[[14:40:55]] [SUCCESS] Screenshot refreshed successfully
[[14:40:55]] [SUCCESS] Screenshot refreshed successfully
[[14:40:54]] [INFO] F4NGh9HrLw=running
[[14:40:54]] [INFO] Executing action 37/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:40:53]] [SUCCESS] Screenshot refreshed
[[14:40:53]] [INFO] Refreshing screenshot...
[[14:40:53]] [INFO] XPEr3w6Zof=pass
[[14:40:48]] [SUCCESS] Screenshot refreshed successfully
[[14:40:48]] [SUCCESS] Screenshot refreshed successfully
[[14:40:48]] [INFO] XPEr3w6Zof=running
[[14:40:48]] [INFO] Executing action 36/591: Restart app: env[appid]
[[14:40:48]] [SUCCESS] Screenshot refreshed
[[14:40:48]] [INFO] Refreshing screenshot...
[[14:40:48]] [INFO] PiQRBWBe3E=pass
[[14:40:44]] [SUCCESS] Screenshot refreshed successfully
[[14:40:44]] [SUCCESS] Screenshot refreshed successfully
[[14:40:44]] [INFO] PiQRBWBe3E=running
[[14:40:44]] [INFO] Executing action 35/591: Tap on image: env[device-back-img]
[[14:40:43]] [SUCCESS] Screenshot refreshed
[[14:40:43]] [INFO] Refreshing screenshot...
[[14:40:43]] [INFO] GWoppouz1l=pass
[[14:40:40]] [SUCCESS] Screenshot refreshed successfully
[[14:40:40]] [SUCCESS] Screenshot refreshed successfully
[[14:40:40]] [INFO] GWoppouz1l=running
[[14:40:40]] [INFO] Executing action 34/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[14:40:40]] [SUCCESS] Screenshot refreshed
[[14:40:40]] [INFO] Refreshing screenshot...
[[14:40:40]] [INFO] B6GDXWAmWp=pass
[[14:40:19]] [SUCCESS] Screenshot refreshed successfully
[[14:40:19]] [SUCCESS] Screenshot refreshed successfully
[[14:40:19]] [INFO] B6GDXWAmWp=running
[[14:40:19]] [INFO] Executing action 33/591: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[14:40:18]] [SUCCESS] Screenshot refreshed
[[14:40:18]] [INFO] Refreshing screenshot...
[[14:40:18]] [INFO] mtYqeDttRc=pass
[[14:40:14]] [SUCCESS] Screenshot refreshed successfully
[[14:40:14]] [SUCCESS] Screenshot refreshed successfully
[[14:40:14]] [INFO] mtYqeDttRc=running
[[14:40:14]] [INFO] Executing action 32/591: Tap on image: env[paypal-close-img]
[[14:40:14]] [SUCCESS] Screenshot refreshed
[[14:40:14]] [INFO] Refreshing screenshot...
[[14:40:14]] [INFO] q6cKxgMAIn=pass
[[14:40:07]] [SUCCESS] Screenshot refreshed successfully
[[14:40:07]] [SUCCESS] Screenshot refreshed successfully
[[14:40:07]] [INFO] q6cKxgMAIn=running
[[14:40:07]] [INFO] Executing action 31/591: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[14:40:06]] [SUCCESS] Screenshot refreshed
[[14:40:06]] [INFO] Refreshing screenshot...
[[14:40:06]] [INFO] KRQDBv2D3A=pass
[[14:40:02]] [SUCCESS] Screenshot refreshed successfully
[[14:40:02]] [SUCCESS] Screenshot refreshed successfully
[[14:40:02]] [INFO] KRQDBv2D3A=running
[[14:40:02]] [INFO] Executing action 30/591: Tap on image: env[device-back-img]
[[14:40:01]] [SUCCESS] Screenshot refreshed
[[14:40:01]] [INFO] Refreshing screenshot...
[[14:40:01]] [INFO] P4b2BITpCf=pass
[[14:39:58]] [SUCCESS] Screenshot refreshed successfully
[[14:39:58]] [SUCCESS] Screenshot refreshed successfully
[[14:39:58]] [INFO] P4b2BITpCf=running
[[14:39:58]] [INFO] Executing action 29/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[14:39:57]] [SUCCESS] Screenshot refreshed
[[14:39:57]] [INFO] Refreshing screenshot...
[[14:39:57]] [INFO] inrxgdWzXr=pass
[[14:39:51]] [SUCCESS] Screenshot refreshed successfully
[[14:39:51]] [SUCCESS] Screenshot refreshed successfully
[[14:39:51]] [INFO] inrxgdWzXr=running
[[14:39:51]] [INFO] Executing action 28/591: Tap on element with accessibility_id: Learn more about Zip
[[14:39:51]] [SUCCESS] Screenshot refreshed
[[14:39:51]] [INFO] Refreshing screenshot...
[[14:39:51]] [INFO] Et3kvnFdxh=pass
[[14:39:47]] [SUCCESS] Screenshot refreshed successfully
[[14:39:47]] [SUCCESS] Screenshot refreshed successfully
[[14:39:46]] [INFO] Et3kvnFdxh=running
[[14:39:46]] [INFO] Executing action 27/591: Tap on image: env[device-back-img]
[[14:39:46]] [INFO] Skipping disabled action 26/591: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[14:39:46]] [SUCCESS] Screenshot refreshed
[[14:39:46]] [INFO] Refreshing screenshot...
[[14:39:46]] [INFO] pk2DLZFBmx=pass
[[14:39:40]] [SUCCESS] Screenshot refreshed successfully
[[14:39:40]] [SUCCESS] Screenshot refreshed successfully
[[14:39:39]] [INFO] pk2DLZFBmx=running
[[14:39:39]] [INFO] Executing action 25/591: Tap on element with accessibility_id: Learn more about AfterPay
[[14:39:39]] [SUCCESS] Screenshot refreshed
[[14:39:39]] [INFO] Refreshing screenshot...
[[14:39:39]] [INFO] ShJSdXvmVL=pass
[[14:39:30]] [SUCCESS] Screenshot refreshed successfully
[[14:39:30]] [SUCCESS] Screenshot refreshed successfully
[[14:39:30]] [INFO] ShJSdXvmVL=running
[[14:39:30]] [INFO] Executing action 24/591: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[14:39:30]] [SUCCESS] Screenshot refreshed
[[14:39:30]] [INFO] Refreshing screenshot...
[[14:39:30]] [INFO] sHQtYzpI4s=pass
[[14:39:25]] [SUCCESS] Screenshot refreshed successfully
[[14:39:25]] [SUCCESS] Screenshot refreshed successfully
[[14:39:25]] [INFO] sHQtYzpI4s=running
[[14:39:25]] [INFO] Executing action 23/591: Tap on image: env[closebtnimage]
[[14:39:24]] [SUCCESS] Screenshot refreshed
[[14:39:24]] [INFO] Refreshing screenshot...
[[14:39:24]] [INFO] 83tV9A4NOn=pass
[[14:39:21]] [SUCCESS] Screenshot refreshed successfully
[[14:39:21]] [SUCCESS] Screenshot refreshed successfully
[[14:39:20]] [INFO] 83tV9A4NOn=running
[[14:39:20]] [INFO] Executing action 22/591: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[14:39:20]] [SUCCESS] Screenshot refreshed
[[14:39:20]] [INFO] Refreshing screenshot...
[[14:39:20]] [INFO] dCqKBG3e7u=pass
[[14:39:16]] [INFO] dCqKBG3e7u=running
[[14:39:16]] [INFO] Executing action 21/591: Tap on image: env[product-share-img]
[[14:39:16]] [SUCCESS] Screenshot refreshed successfully
[[14:39:16]] [SUCCESS] Screenshot refreshed successfully
[[14:39:15]] [SUCCESS] Screenshot refreshed
[[14:39:15]] [INFO] Refreshing screenshot...
[[14:39:15]] [INFO] kAQ1yIIw3h=pass
[[14:39:11]] [SUCCESS] Screenshot refreshed successfully
[[14:39:11]] [SUCCESS] Screenshot refreshed successfully
[[14:39:11]] [INFO] kAQ1yIIw3h=running
[[14:39:11]] [INFO] Executing action 20/591: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[14:39:10]] [SUCCESS] Screenshot refreshed
[[14:39:10]] [INFO] Refreshing screenshot...
[[14:39:10]] [INFO] OmKfD9iBjD=pass
[[14:39:07]] [SUCCESS] Screenshot refreshed successfully
[[14:39:07]] [SUCCESS] Screenshot refreshed successfully
[[14:39:06]] [INFO] OmKfD9iBjD=running
[[14:39:06]] [INFO] Executing action 19/591: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:39:06]] [SUCCESS] Screenshot refreshed
[[14:39:06]] [INFO] Refreshing screenshot...
[[14:39:06]] [INFO] dMl1PH9Dlc=pass
[[14:38:54]] [SUCCESS] Screenshot refreshed successfully
[[14:38:54]] [SUCCESS] Screenshot refreshed successfully
[[14:38:54]] [INFO] dMl1PH9Dlc=running
[[14:38:54]] [INFO] Executing action 18/591: Wait for 10 ms
[[14:38:53]] [SUCCESS] Screenshot refreshed
[[14:38:53]] [INFO] Refreshing screenshot...
[[14:38:53]] [INFO] eHLWiRoqqS=pass
[[14:38:49]] [SUCCESS] Screenshot refreshed successfully
[[14:38:49]] [SUCCESS] Screenshot refreshed successfully
[[14:38:48]] [INFO] eHLWiRoqqS=running
[[14:38:48]] [INFO] Executing action 17/591: Swipe from (50%, 70%) to (50%, 30%)
[[14:38:48]] [SUCCESS] Screenshot refreshed
[[14:38:48]] [INFO] Refreshing screenshot...
[[14:38:48]] [INFO] huUnpMMjVR=pass
[[14:38:44]] [SUCCESS] Screenshot refreshed successfully
[[14:38:44]] [SUCCESS] Screenshot refreshed successfully
[[14:38:43]] [INFO] huUnpMMjVR=running
[[14:38:43]] [INFO] Executing action 16/591: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[14:38:43]] [SUCCESS] Screenshot refreshed
[[14:38:43]] [INFO] Refreshing screenshot...
[[14:38:43]] [INFO] XmAxcBtFI0=pass
[[14:38:40]] [SUCCESS] Screenshot refreshed successfully
[[14:38:40]] [SUCCESS] Screenshot refreshed successfully
[[14:38:39]] [INFO] XmAxcBtFI0=running
[[14:38:39]] [INFO] Executing action 15/591: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[14:38:39]] [SUCCESS] Screenshot refreshed
[[14:38:39]] [INFO] Refreshing screenshot...
[[14:38:39]] [INFO] ktAufkDJnF=pass
[[14:38:35]] [SUCCESS] Screenshot refreshed successfully
[[14:38:35]] [SUCCESS] Screenshot refreshed successfully
[[14:38:35]] [INFO] ktAufkDJnF=running
[[14:38:35]] [INFO] Executing action 14/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[14:38:34]] [SUCCESS] Screenshot refreshed
[[14:38:34]] [INFO] Refreshing screenshot...
[[14:38:34]] [INFO] dMl1PH9Dlc=pass
[[14:38:28]] [SUCCESS] Screenshot refreshed successfully
[[14:38:28]] [SUCCESS] Screenshot refreshed successfully
[[14:38:27]] [INFO] dMl1PH9Dlc=running
[[14:38:27]] [INFO] Executing action 13/591: Wait for 5 ms
[[14:38:27]] [SUCCESS] Screenshot refreshed
[[14:38:27]] [INFO] Refreshing screenshot...
[[14:38:27]] [INFO] a50JhCx0ir=pass
[[14:38:23]] [SUCCESS] Screenshot refreshed successfully
[[14:38:23]] [SUCCESS] Screenshot refreshed successfully
[[14:38:23]] [INFO] a50JhCx0ir=running
[[14:38:23]] [INFO] Executing action 12/591: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[14:38:22]] [SUCCESS] Screenshot refreshed
[[14:38:22]] [INFO] Refreshing screenshot...
[[14:38:22]] [INFO] Y1O1clhMSJ=pass
[[14:38:18]] [SUCCESS] Screenshot refreshed successfully
[[14:38:18]] [SUCCESS] Screenshot refreshed successfully
[[14:38:18]] [INFO] Y1O1clhMSJ=running
[[14:38:18]] [INFO] Executing action 11/591: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[14:38:18]] [SUCCESS] Screenshot refreshed
[[14:38:18]] [INFO] Refreshing screenshot...
[[14:38:18]] [INFO] lYPskZt0Ya=pass
[[14:38:14]] [SUCCESS] Screenshot refreshed successfully
[[14:38:14]] [SUCCESS] Screenshot refreshed successfully
[[14:38:14]] [INFO] lYPskZt0Ya=running
[[14:38:14]] [INFO] Executing action 10/591: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:38:13]] [SUCCESS] Screenshot refreshed
[[14:38:13]] [INFO] Refreshing screenshot...
[[14:38:13]] [INFO] xUbWFa8Ok2=pass
[[14:38:08]] [SUCCESS] Screenshot refreshed successfully
[[14:38:08]] [SUCCESS] Screenshot refreshed successfully
[[14:38:08]] [INFO] xUbWFa8Ok2=running
[[14:38:08]] [INFO] Executing action 9/591: Tap on Text: "Latest"
[[14:38:08]] [SUCCESS] Screenshot refreshed
[[14:38:08]] [INFO] Refreshing screenshot...
[[14:38:08]] [INFO] RbNtEW6N9T=pass
[[14:38:04]] [SUCCESS] Screenshot refreshed successfully
[[14:38:04]] [SUCCESS] Screenshot refreshed successfully
[[14:38:03]] [INFO] RbNtEW6N9T=running
[[14:38:03]] [INFO] Executing action 8/591: Tap on Text: "Toys"
[[14:38:03]] [SUCCESS] Screenshot refreshed
[[14:38:03]] [INFO] Refreshing screenshot...
[[14:38:03]] [INFO] ltDXyWvtEz=pass
[[14:37:58]] [SUCCESS] Screenshot refreshed successfully
[[14:37:58]] [SUCCESS] Screenshot refreshed successfully
[[14:37:58]] [INFO] ltDXyWvtEz=running
[[14:37:58]] [INFO] Executing action 7/591: Tap on image: env[device-back-img]
[[14:37:58]] [SUCCESS] Screenshot refreshed
[[14:37:58]] [INFO] Refreshing screenshot...
[[14:37:58]] [INFO] QPKR6jUF9O=pass
[[14:37:55]] [SUCCESS] Screenshot refreshed successfully
[[14:37:55]] [SUCCESS] Screenshot refreshed successfully
[[14:37:55]] [INFO] QPKR6jUF9O=running
[[14:37:55]] [INFO] Executing action 6/591: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[14:37:54]] [SUCCESS] Screenshot refreshed
[[14:37:54]] [INFO] Refreshing screenshot...
[[14:37:54]] [INFO] vfwUVEyq6X=pass
[[14:37:51]] [SUCCESS] Screenshot refreshed successfully
[[14:37:51]] [SUCCESS] Screenshot refreshed successfully
[[14:37:51]] [INFO] vfwUVEyq6X=running
[[14:37:51]] [INFO] Executing action 5/591: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[14:37:50]] [SUCCESS] Screenshot refreshed
[[14:37:50]] [INFO] Refreshing screenshot...
[[14:37:50]] [INFO] Xr6F8gdd8q=pass
[[14:37:46]] [SUCCESS] Screenshot refreshed successfully
[[14:37:46]] [SUCCESS] Screenshot refreshed successfully
[[14:37:46]] [INFO] Xr6F8gdd8q=running
[[14:37:46]] [INFO] Executing action 4/591: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:37:46]] [SUCCESS] Screenshot refreshed
[[14:37:46]] [INFO] Refreshing screenshot...
[[14:37:46]] [INFO] Xr6F8gdd8q=pass
[[14:37:43]] [SUCCESS] Screenshot refreshed successfully
[[14:37:43]] [SUCCESS] Screenshot refreshed successfully
[[14:37:43]] [INFO] Xr6F8gdd8q=running
[[14:37:43]] [INFO] Executing action 3/591: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:37:42]] [SUCCESS] Screenshot refreshed
[[14:37:42]] [INFO] Refreshing screenshot...
[[14:37:42]] [INFO] F4NGh9HrLw=pass
[[14:37:39]] [SUCCESS] Screenshot refreshed successfully
[[14:37:39]] [SUCCESS] Screenshot refreshed successfully
[[14:37:38]] [INFO] F4NGh9HrLw=running
[[14:37:38]] [INFO] Executing action 2/591: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:37:37]] [SUCCESS] Screenshot refreshed
[[14:37:37]] [INFO] Refreshing screenshot...
[[14:37:37]] [INFO] H9fy9qcFbZ=pass
[[14:37:32]] [INFO] H9fy9qcFbZ=running
[[14:37:32]] [INFO] Executing action 1/591: Restart app: env[appid]
[[14:37:32]] [INFO] ExecutionManager: Starting execution of 591 actions...
[[14:37:32]] [SUCCESS] Cleared 1 screenshots from database
[[14:37:32]] [INFO] Clearing screenshots from database before execution...
[[14:37:32]] [SUCCESS] All screenshots deleted successfully
[[14:37:32]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:37:32]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250711_143732/screenshots
[[14:37:32]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250711_143732
[[14:37:32]] [SUCCESS] Report directory initialized successfully
[[14:37:32]] [INFO] Initializing report directory and screenshots folder for test suite...
[[14:37:24]] [INFO] Collapsed all test cases
[[14:37:23]] [INFO] Expanded all test cases
[[14:36:52]] [INFO] Collapsed all test cases
[[14:36:49]] [SUCCESS] All screenshots deleted successfully
[[14:36:49]] [INFO] All actions cleared
[[14:36:49]] [INFO] Cleaning up screenshots...
[[14:36:35]] [SUCCESS] Screenshot refreshed successfully
[[14:36:34]] [SUCCESS] Screenshot refreshed
[[14:36:34]] [INFO] Refreshing screenshot...
[[14:36:33]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[14:36:33]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[14:36:12]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[14:36:10]] [SUCCESS] Found 1 device(s)
[[14:36:09]] [INFO] Refreshing device list...
