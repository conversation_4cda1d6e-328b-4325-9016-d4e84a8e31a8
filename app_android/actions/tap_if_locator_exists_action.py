import os
import traceback
import time
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class TapIfLocatorExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Tap If Locator Exists action.
        This action will tap on an element if it exists on the screen using locators, and do nothing if it doesn't.

        Args:
            params (dict): Parameters for the action
                - locator_type (str): Type of locator (id, xpath, accessibility_id, class_name, etc.)
                - locator_value (str): Value of the locator
                - timeout (int, optional): Maximum time to wait for the element to appear in seconds. Default is 10

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
        """
        try:
            # Check if controller is available
            if not self.controller:
                # Always return success for "if exists" behavior
                return {"status": "success", "message": "Device controller not available - step passed (if exists behavior)"}

            # Get parameters
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            timeout = int(params.get('timeout', 10))

            if not locator_type or not locator_value:
                # Always return success for "if exists" behavior
                return {"status": "success", "message": "Locator type and value are required - step passed (if exists behavior)"}

            self.logger.info(f"Looking for element with {locator_type}: {locator_value} (timeout: {timeout}s)")

            # Try to find the element using universal locator method first (includes UISelector support)
            try:
                element = self.find_element_with_locator(locator_type, locator_value, timeout)

                # Fallback to controller's method if universal method didn't work
                if not element and hasattr(self.controller, 'find_element'):
                    element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                
                if element:
                    self.logger.info(f"Element found with {locator_type}: {locator_value}, attempting to tap...")
                    
                    # Try to tap on the element
                    try:
                        # Use the controller's tap_element method if available
                        if hasattr(self.controller, 'tap_element'):
                            tap_result = self.controller.tap_element(locator_type, locator_value, timeout=timeout)
                            if isinstance(tap_result, dict) and tap_result.get('status') == 'success':
                                return {"status": "success", "message": f"Tapped on element: {locator_type}={locator_value}"}
                            elif isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                                # Always return success for "if exists" behavior
                                return {"status": "success", "message": f"Element found but tap failed - step passed (if exists behavior): {tap_result.get('message')}"}
                        
                        # Fallback: try to click the element directly
                        if hasattr(element, 'click'):
                            element.click()
                            return {"status": "success", "message": f"Tapped on element: {locator_type}={locator_value}"}
                        
                        # Fallback: try to get element location and tap at coordinates
                        if hasattr(element, 'location') and hasattr(element, 'size'):
                            location = element.location
                            size = element.size
                            
                            # Calculate center coordinates
                            x = location['x'] + size['width'] // 2
                            y = location['y'] + size['height'] // 2
                            
                            # Validate coordinates
                            device_width = None
                            device_height = None
                            
                            # Try to get device dimensions if available
                            if hasattr(self.controller, 'get_device_size'):
                                try:
                                    device_size = self.controller.get_device_size()
                                    if device_size and len(device_size) == 2:
                                        device_width, device_height = device_size
                                except Exception as size_err:
                                    self.logger.warning(f"Failed to get device size: {size_err}")
                            
                            # Validate the coordinates
                            valid_coords = validate_coordinates((x, y), device_width, device_height)
                            
                            if valid_coords:
                                x, y = valid_coords
                                self.logger.info(f"Element found at valid position: ({x}, {y}), tapping...")
                                
                                # Use the controller's tap method
                                if hasattr(self.controller, 'tap'):
                                    tap_result = self.controller.tap(x, y)
                                    if isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                                        # Always return success for "if exists" behavior
                                        return {"status": "success", "message": f"Element found but tap failed - step passed (if exists behavior): {tap_result.get('message')}"}
                                    return {"status": "success", "message": f"Tapped on element: {locator_type}={locator_value} at position ({x}, {y})"}
                                else:
                                    # Always return success for "if exists" behavior
                                    return {"status": "success", "message": "Element found but no tap method available - step passed (if exists behavior)"}
                            else:
                                # Always return success for "if exists" behavior
                                return {"status": "success", "message": f"Element found but coordinates invalid - step passed (if exists behavior): ({x}, {y})"}

                        # If all methods fail
                        # Always return success for "if exists" behavior
                        return {"status": "success", "message": f"Element found but could not tap - step passed (if exists behavior): {locator_type}={locator_value}"}

                    except Exception as tap_err:
                        self.logger.error(f"Error tapping on element: {tap_err}")
                        # Always return success for "if exists" behavior
                        return {"status": "success", "message": f"Element found but tap error - step passed (if exists behavior): {str(tap_err)}"}
                        
                else:
                    # Element doesn't exist, do nothing (this is the expected behavior for "if exists")
                    self.logger.info(f"Element not found: {locator_type}={locator_value}, skipping tap")
                    return {"status": "success", "message": f"Element not found: {locator_type}={locator_value}, no action taken"}
                    
            except Exception as find_err:
                self.logger.warning(f"Error finding element: {find_err}")
                # Element doesn't exist, do nothing (this is the expected behavior for "if exists")
                self.logger.info(f"Element not found: {locator_type}={locator_value}, skipping tap")
                return {"status": "success", "message": f"Element not found: {locator_type}={locator_value}, no action taken"}

        except Exception as e:
            self.logger.error(f"Error executing Tap If Locator Exists action: {e}")
            traceback.print_exc()
            # Always return success for "if exists" behavior - even on general errors
            return {"status": "success", "message": f"Tap If Locator Exists action encountered error but marked as passed: {str(e)}"}
