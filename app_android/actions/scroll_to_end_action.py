from .base_action import BaseAction
from appium.webdriver.common.appiumby import AppiumBy

class ScrollToEndAction(BaseAction):
    """Handler for scrolling to the end of a scrollable view using UiScrollable"""
    
    def execute(self, params):
        """
        Execute scroll to end action using Android UiScrollable

        Args:
            params: Dictionary containing:
                - max_swipes: Maximum number of swipes to perform (default: 50)
                - timeout: Timeout for the operation (default: 30)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        if not self.controller.driver:
            return {"status": "error", "message": "No active driver session"}

        # Check if this is an Android platform
        if hasattr(self.controller, 'platform_name') and self.controller.platform_name:
            if self.controller.platform_name.lower() != 'android':
                return {"status": "error", "message": "Scroll to End action is only supported on Android devices"}
        else:
            self.logger.warning("Platform name not available, proceeding with scroll action")
            
        # Extract parameters
        max_swipes = int(params.get('max_swipes', 50))
        timeout = int(params.get('timeout', 30))
        
        try:
            self.logger.info(f"Executing scroll to end action with max_swipes: {max_swipes}")
            
            # Use Android UiScrollable to scroll to the end
            # This uses the UiSelector to find scrollable elements and scroll to the end
            uiautomator_command = f"new UiScrollable(new UiSelector().scrollable(true)).scrollToEnd({max_swipes})"
            
            # Execute the UiAutomator command
            self.controller.driver.find_element(
                AppiumBy.ANDROID_UIAUTOMATOR, 
                uiautomator_command
            )
            
            self.logger.info("Successfully scrolled to end using UiScrollable")
            return {
                "status": "success",
                "message": f"Successfully scrolled to end (max_swipes: {max_swipes})"
            }
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error executing scroll to end action: {error_msg}")
            
            # Try fallback method using UIAutomator2 helper if available
            try:
                if hasattr(self.controller, 'uiautomator2_helper') and self.controller.uiautomator2_helper:
                    self.logger.info("Attempting fallback scroll to end using UIAutomator2 helper")
                    
                    # Get device dimensions for fallback swipe
                    device_dimensions = self.controller.get_device_dimensions()
                    if device_dimensions:
                        width, height = device_dimensions
                        
                        # Perform multiple swipes to scroll to end
                        start_x = width // 2
                        start_y = int(height * 0.8)  # Start from 80% down
                        end_x = width // 2
                        end_y = int(height * 0.2)    # End at 20% down
                        
                        swipe_count = 0
                        max_fallback_swipes = min(max_swipes, 10)  # Limit fallback swipes
                        
                        for i in range(max_fallback_swipes):
                            try:
                                # Perform swipe using controller
                                result = self.controller.swipe(start_x, start_y, end_x, end_y, 500)
                                swipe_count += 1
                                
                                # Small delay between swipes
                                import time
                                time.sleep(0.5)
                                
                            except Exception as swipe_error:
                                self.logger.warning(f"Fallback swipe {i+1} failed: {swipe_error}")
                                break
                        
                        if swipe_count > 0:
                            self.logger.info(f"Fallback scroll to end completed with {swipe_count} swipes")
                            return {
                                "status": "success",
                                "message": f"Scroll to end completed using fallback method ({swipe_count} swipes)"
                            }
                
            except Exception as fallback_error:
                self.logger.warning(f"Fallback scroll to end failed: {fallback_error}")
            
            return {
                "status": "error",
                "message": f"Scroll to end action failed: {error_msg}"
            }
