import time
import cv2
import numpy as np
import os
import json
import threading
from datetime import datetime
from skimage.metrics import structural_similarity as ssim
import pytesseract
from PIL import Image

class Recorder:
    """Records user actions on an Android device"""

    def __init__(self, device_controller):
        """Initialize with a device controller"""
        self.device_controller = device_controller
        self.is_recording = False
        self.actions = []
        self.start_time = None
        self.last_screenshot = None
        self.recording_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                          'recordings')
        self.elements_cache = {}  # Cache for detected elements
        os.makedirs(self.recording_dir, exist_ok=True)

        # Configure threading for saving
        self.save_lock = threading.Lock()
        self.is_saving = False

        # Configure OCR
        try:
            # Check if tesseract is available (will raise error if not)
            pytesseract.get_tesseract_version()
            self.ocr_available = True
        except:
            self.ocr_available = False
            print("Warning: Tesseract OCR not available. Text detection will be disabled.")

    def start_recording(self):
        """Start recording user actions"""
        print("Starting recording session")
        self.is_recording = True
        self.actions = []
        self.start_time = time.time()
        self.elements_cache = {}

        # Take initial screenshot
        action_id = f"initial_recording_{int(time.time())}"
        screenshot_result = self.device_controller.take_screenshot(action_id=action_id)
        screenshot_path = screenshot_result.get('path') if screenshot_result else None
        if screenshot_path:
            print(f"Initial screenshot captured: {screenshot_path}")
            # Log the initial state
            self.actions.append({
                'type': 'initial_state',
                'timestamp': 0,
                'screenshot': screenshot_path
            })

            # Pre-detect elements in the initial screen
            self._detect_elements(screenshot_path)
        else:
            print("Failed to capture initial screenshot")

    def stop_recording(self):
        """Stop recording and return recorded actions"""
        print(f"Stopping recording with {len(self.actions)} actions")
        self.is_recording = False
        return self.actions

    def add_action(self, action_data):
        """Add a user action to the recording"""
        if not self.is_recording:
            print("Not recording, action ignored")
            return None

        print(f"Adding action: {action_data}")

        # Validate action data
        if action_data['type'] == 'swipe':
            required_fields = ['start_x', 'start_y', 'end_x', 'end_y']
            if any(action_data.get(field) is None for field in required_fields):
                print(f"Error: Missing coordinates for swipe action: {action_data}")
                return None
        elif action_data['type'] == 'tap':
            required_fields = ['x', 'y']
            if any(action_data.get(field) is None for field in required_fields):
                print(f"Error: Missing coordinates for tap action: {action_data}")
                return None

        # Calculate timestamp relative to recording start
        current_time = time.time()
        elapsed_time = current_time - self.start_time

        # Enhanced action information for better replay
        if action_data['type'] == 'tap':
            # Extract information about what was tapped
            tapped_elements = action_data.get('tapped_elements', [])

            # If we have text elements, include a description
            action_description = ""
            for elem in tapped_elements:
                if elem.get('text') and elem.get('text').strip():
                    action_description = f"Tapped on \"{elem['text']}\""
                    break

            if not action_description:
                action_description = f"Tapped at ({action_data['x']}, {action_data['y']})"

            action_data['description'] = action_description
            print(f"Action description: {action_description}")

        # Take a screenshot after the action
        action_id = f"recorded_action_{int(time.time())}"
        screenshot_result = self.device_controller.take_screenshot(action_id=action_id)
        screenshot_path = screenshot_result.get('path') if screenshot_result else None

        if not screenshot_path:
            print("Failed to capture screenshot for action")
        else:
            print(f"Screenshot captured for action: {screenshot_path}")

        # Get UI elements and visible text for better context
        try:
            page_source = self.device_controller.get_page_source()
            if page_source:
                action_data['page_source'] = page_source
        except Exception as e:
            print(f"Error getting page source: {e}")

        # Create action record with timestamp and screenshot
        action = {
            **action_data,
            'timestamp': round(elapsed_time, 3),
            'screenshot': screenshot_path
        }

        print(f"Created action record with timestamp {action['timestamp']}")
        self.actions.append(action)
        print(f"Total actions recorded: {len(self.actions)}")

        # Generate basic code
        action_code = self._generate_simple_code(action)
        action['code'] = action_code

        return action

    def _process_elements_in_background(self, screenshot_path, action_index):
        """Process element detection in background thread"""
        # Detect elements
        elements = self._detect_elements(screenshot_path)

        # Update action with detected elements if we're still recording
        if self.is_recording and action_index < len(self.actions):
            self.actions[action_index]['elements'] = elements

    def _detect_changes_fast(self, prev_screenshot, curr_screenshot):
        """Fast detection of visual changes between screenshots"""
        try:
            # Read images
            img1 = cv2.imread(prev_screenshot)
            img2 = cv2.imread(curr_screenshot)

            # Convert to grayscale
            gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

            # Calculate absolute difference
            diff = cv2.absdiff(gray1, gray2)

            # Apply threshold to highlight changes
            _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)

            # Find contours - using a simplified approach for speed
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Just return the top 5 largest changes for performance
            all_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # Filter tiny changes
                    all_contours.append((contour, area))

            # Sort by area and get top 5
            all_contours.sort(key=lambda x: x[1], reverse=True)
            top_contours = all_contours[:5]

            changes = []
            for contour, area in top_contours:
                x, y, w, h = cv2.boundingRect(contour)
                changes.append({
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h,
                    'area': int(area)
                })

            return changes
        except Exception as e:
            print(f"Error detecting fast changes: {e}")
            return []

    def _detect_elements(self, screenshot_path):
        """Detect UI elements on the screen"""
        elements = []

        # Check if we already have cached elements for this screenshot
        cache_key = os.path.basename(screenshot_path)
        if cache_key in self.elements_cache:
            return self.elements_cache[cache_key]

        try:
            # Check if file exists
            if not os.path.isfile(screenshot_path):
                print(f"Warning: Screenshot file not found: {screenshot_path}")
                return []

            # Try to detect elements using Appium first
            if self.device_controller.driver:
                try:
                    page_source = self.device_controller.get_page_source()
                    if page_source:
                        # Parse XML and extract element information
                        import xml.etree.ElementTree as ET
                        root = ET.fromstring(page_source)

                        for elem in root.findall(".//*[@bounds]"):
                            # Extract bounds
                            bounds_str = elem.attrib.get('bounds', '')
                            if bounds_str:
                                try:
                                    # Parse bounds format like "[0,0][100,100]"
                                    bounds_parts = bounds_str.replace("][", ",").replace("[", "").replace("]", "").split(",")
                                    if len(bounds_parts) == 4:
                                        x1, y1, x2, y2 = map(int, bounds_parts)

                                        element_info = {
                                            'type': 'ui_element',
                                            'bounds': [x1, y1, x2, y2],
                                            'center_x': (x1 + x2) // 2,
                                            'center_y': (y1 + y2) // 2,
                                            'text': elem.attrib.get('text', ''),
                                            'resource-id': elem.attrib.get('resource-id', ''),
                                            'class': elem.attrib.get('class', '')
                                        }

                                        elements.append(element_info)
                                except Exception as e:
                                    print(f"Error parsing element bounds: {e}")

                except Exception as e:
                    print(f"Error extracting elements from page source: {e}")

            # If no elements found via Appium or if we want to supplement with image processing
            if not elements or len(elements) < 5:  # If few elements found, augment with image analysis
                # Read image for OpenCV processing
                img = cv2.imread(screenshot_path)
                if img is None:
                    print(f"Warning: Could not read image file: {screenshot_path}")
                    return elements  # Return what we have so far

                # Run OCR if available
                if self.ocr_available:
                    try:
                        text_elements = self._detect_text_elements(screenshot_path)
                        elements.extend(text_elements)
                    except Exception as e:
                        print(f"Error in OCR detection: {e}")

                # Detect UI elements with OpenCV
                try:
                    ui_elements = self._detect_ui_elements_with_opencv(img)
                    elements.extend(ui_elements)
                except Exception as e:
                    print(f"Error in UI element detection: {e}")

            # Cache the result only if we have some elements
            if elements:
                self.elements_cache[cache_key] = elements

            return elements
        except Exception as e:
            print(f"Error detecting elements: {e}")
            return []

    def _detect_text_elements(self, screenshot_path):
        """Detect text elements using OCR"""
        if not self.ocr_available:
            return []

        try:
            # Use Tesseract OCR to detect text
            img = Image.open(screenshot_path)
            text_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)

            text_elements = []
            for i in range(len(text_data['text'])):
                # Filter out empty results and low-confidence detections
                if int(text_data['conf'][i]) > 60 and text_data['text'][i].strip():
                    x = text_data['left'][i]
                    y = text_data['top'][i]
                    w = text_data['width'][i]
                    h = text_data['height'][i]

                    text_elements.append({
                        'type': 'text',
                        'text': text_data['text'][i],
                        'bounds': [x, y, x + w, y + h],
                        'center_x': x + w // 2,
                        'center_y': y + h // 2,
                        'confidence': int(text_data['conf'][i])
                    })

            return text_elements
        except Exception as e:
            print(f"Error detecting text elements: {e}")
            return []

    def _detect_ui_elements_with_opencv(self, img):
        """Detect UI elements using OpenCV"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Basic edge detection
            edges = cv2.Canny(gray, 50, 150)

            # Dilate to connect edges
            kernel = np.ones((3, 3), np.uint8)
            dilated = cv2.dilate(edges, kernel, iterations=2)

            # Find contours
            contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            ui_elements = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 500:  # Filter small elements
                    x, y, w, h = cv2.boundingRect(contour)

                    ui_elements.append({
                        'type': 'ui_element',
                        'bounds': [x, y, x + w, y + h],
                        'center_x': x + w // 2,
                        'center_y': y + h // 2,
                        'area': int(area)
                    })

            return ui_elements
        except Exception as e:
            print(f"Error detecting UI elements: {e}")
            return []

    def _generate_simple_code(self, action):
        """Generate a simple Python code snippet for the action"""
        action_type = action.get('type', '')

        if action_type == 'tap':
            x, y = action.get('x'), action.get('y')
            return f"driver.tap({x}, {y})"

        elif action_type == 'swipe':
            start_x = action.get('start_x')
            start_y = action.get('start_y')
            end_x = action.get('end_x')
            end_y = action.get('end_y')
            duration = action.get('duration', 500)
            return f"driver.swipe({start_x}, {start_y}, {end_x}, {end_y}, {duration})"

        elif action_type == 'text':
            text = action.get('text', '')
            return f"driver.input_text('{text}')"

        elif action_type == 'wait':
            duration = action.get('duration', 1000)
            return f"time.sleep({duration / 1000})"

        elif action_type == 'key':
            key_code = action.get('key_code')
            return f"driver.press_key({key_code})"

        else:
            return f"# Unsupported action type: {action_type}"

    def generate_code(self, actions):
        """Generate Python code from recorded actions"""
        if not actions:
            return "# No actions recorded"

        # Get the current device ID
        device_id = "DEVICE_ID"
        if self.device_controller and self.device_controller.device:
            device_id = self.device_controller.device.serial

        code_lines = [
            "from app_android.utils.appium_device_controller import AppiumDeviceController",
            "import time",
            "",
            "# Initialize device controller",
            "device_controller = AppiumDeviceController()",
            f"# Connect to device with ID: {device_id}",
            f"device_controller.connect_to_device('{device_id}')",
            ""
        ]

        for i, action in enumerate(actions):
            # Skip initial state
            if action['type'] == 'initial_state':
                continue

            # Add comment with timestamp
            code_lines.append(f"# Action {i}: {action['type']} at {action['timestamp']}s")

            # Generate code based on action type
            if action['type'] == 'tap':
                if 'x' in action and 'y' in action:
                    # Add description if available
                    if action.get('description'):
                        code_lines.append(f"# {action['description']}")
                    code_lines.append(f"device_controller.tap({action['x']}, {action['y']})")
                else:
                    code_lines.append("# Missing tap coordinates")

            elif action['type'] == 'swipe':
                if all(k in action for k in ('start_x', 'start_y', 'end_x', 'end_y')):
                    code_lines.append(
                        f"device_controller.swipe({action['start_x']}, {action['start_y']}, "
                        f"{action['end_x']}, {action['end_y']}, {action.get('duration', 300)})"
                    )
                else:
                    code_lines.append("# Missing swipe coordinates")

            elif action['type'] == 'text':
                if 'text' in action:
                    safe_text = action['text'].replace("'", "\\'") if 'text' in action else ""
                    code_lines.append(f"device_controller.input_text('{safe_text}')")
                else:
                    code_lines.append("# Missing text content")

            elif action['type'] == 'key':
                if 'key_code' in action:
                    code_lines.append(f"device_controller.press_keycode({action['key_code']})")
                else:
                    code_lines.append("# Missing key code")

            # Add wait for screen change if screenshot is available
            if 'screenshot' in action:
                code_lines.append(f"# Wait for screen to update")
                code_lines.append(f"time.sleep(1)")

            # Add sleep based on timestamp difference if not the last action
            if i < len(actions) - 1:
                next_time = actions[i+1]['timestamp']
                current_time = action['timestamp']
                sleep_duration = round(next_time - current_time, 2)

                if sleep_duration > 0.1:  # Only add sleep if significant
                    code_lines.append(f"time.sleep({sleep_duration})  # Wait for next action")

            # Add an empty line between actions
            code_lines.append("")

        return "\n".join(code_lines)

    def save_recording(self, name=None):
        """Save the recording to a file"""
        if not name:
            name = f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Make sure the name has a .json extension
        if not name.endswith('.json'):
            filename = f"{name}.json"
        else:
            filename = name

        filepath = os.path.join(self.recording_dir, filename)

        # Save the recording
        try:
            with open(filepath, 'w') as file:
                json.dump({
                    'name': name,
                    'date': datetime.now().isoformat(),
                    'actions': self.actions
                }, file, indent=2)

            print(f"Recording saved to: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error saving recording: {e}")
            return None

    def load_recording(self, filepath):
        """Load a recording from a file"""
        try:
            with open(filepath, 'r') as file:
                data = json.load(file)

            # Update our actions list
            self.actions = data.get('actions', [])
            print(f"Loaded {len(self.actions)} actions from {filepath}")
            return self.actions
        except Exception as e:
            print(f"Error loading recording: {e}")
            return []