#!/usr/bin/env python3
"""
Test script to verify the "Press Enter after text input" checkbox functionality
after the AirTest to UIAutomator2 migration fixes.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the parent directory to the path to import the action
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from actions.input_text_action import InputTextAction
from utils.appium_device_controller import AppiumDeviceController

class TestEnterKeyFunctionality(unittest.TestCase):
    """Test cases for Enter key functionality in Input Text action"""
    
    def setUp(self):
        """Set up test environment"""
        # Create mock controller
        self.mock_controller = Mock(spec=AppiumDeviceController)
        self.mock_controller.platform_name = 'android'
        self.mock_controller.device_id = 'test_device'
        
        # Create mock UIAutomator2 helper
        self.mock_ui2_helper = Mock()
        self.mock_ui2_helper.device_id = 'test_device'
        self.mock_ui2_helper.find_element_by_text.return_value = {
            'bounds': {'left': 100, 'top': 200, 'right': 300, 'bottom': 250}
        }
        self.mock_ui2_helper.tap_at_coordinates.return_value = True
        self.mock_ui2_helper.input_text.return_value = True
        
        self.mock_controller.uiautomator2_helper = self.mock_ui2_helper
        
        # Create InputTextAction instance
        self.action = InputTextAction(self.mock_controller)
        
    def test_enter_key_enabled_uiautomator2_path(self):
        """Test Enter key functionality when checkbox is enabled - UIAutomator2 path"""
        params = {
            'locator_type': 'text',
            'locator_value': 'Username',
            'text': 'testuser',
            'enter': True,
            'clear_first': True,
            'timeout': 10
        }
        
        with patch('subprocess.run') as mock_subprocess:
            result = self.action.execute(params)
            
            # Verify text input was successful
            self.assertEqual(result['status'], 'success')
            self.assertIn('+ Enter', result['message'])
            self.assertIn('testuser', result['message'])
            
            # Verify Enter key was pressed via ADB
            enter_calls = [call for call in mock_subprocess.call_args_list 
                          if 'KEYCODE_ENTER' in str(call)]
            self.assertTrue(len(enter_calls) > 0, "Enter key should have been pressed")
            
    def test_enter_key_disabled_uiautomator2_path(self):
        """Test Enter key functionality when checkbox is disabled - UIAutomator2 path"""
        params = {
            'locator_type': 'text',
            'locator_value': 'Username',
            'text': 'testuser',
            'enter': False,
            'clear_first': True,
            'timeout': 10
        }
        
        with patch('subprocess.run') as mock_subprocess:
            result = self.action.execute(params)
            
            # Verify text input was successful
            self.assertEqual(result['status'], 'success')
            self.assertNotIn('+ Enter', result['message'])
            self.assertIn('testuser', result['message'])
            
            # Verify Enter key was NOT pressed
            enter_calls = [call for call in mock_subprocess.call_args_list 
                          if 'KEYCODE_ENTER' in str(call)]
            self.assertEqual(len(enter_calls), 0, "Enter key should NOT have been pressed")
            
    def test_enter_key_enabled_controller_fallback(self):
        """Test Enter key functionality when falling back to controller method"""
        # Make UIAutomator2 fail to trigger fallback
        self.mock_controller.uiautomator2_helper = None
        
        # Mock controller's input_text method
        self.mock_controller.input_text.return_value = {
            'status': 'success',
            'message': 'Text input successful using fallback: testuser + Enter'
        }
        
        params = {
            'locator_type': 'id',
            'locator_value': 'username_field',
            'text': 'testuser',
            'enter': True,
            'clear_first': True,
            'timeout': 10
        }
        
        result = self.action.execute(params)
        
        # Verify text input was successful
        self.assertEqual(result['status'], 'success')
        
        # Verify controller's input_text was called with enter_after_text=True
        self.mock_controller.input_text.assert_called_once_with(
            locator_type='id',
            locator_value='username_field',
            text='testuser',
            clear_first=True,
            timeout=10,
            enter_after_text=True
        )
        
    def test_enter_key_disabled_controller_fallback(self):
        """Test Enter key functionality disabled when falling back to controller method"""
        # Make UIAutomator2 fail to trigger fallback
        self.mock_controller.uiautomator2_helper = None
        
        # Mock controller's input_text method
        self.mock_controller.input_text.return_value = {
            'status': 'success',
            'message': 'Text input successful using fallback: testuser'
        }
        
        params = {
            'locator_type': 'id',
            'locator_value': 'username_field',
            'text': 'testuser',
            'enter': False,
            'clear_first': True,
            'timeout': 10
        }
        
        result = self.action.execute(params)
        
        # Verify text input was successful
        self.assertEqual(result['status'], 'success')
        
        # Verify controller's input_text was called with enter_after_text=False
        self.mock_controller.input_text.assert_called_once_with(
            locator_type='id',
            locator_value='username_field',
            text='testuser',
            clear_first=True,
            timeout=10,
            enter_after_text=False
        )

class TestControllerEnterKeyMethods(unittest.TestCase):
    """Test cases for controller's Enter key methods"""
    
    def setUp(self):
        """Set up test environment"""
        self.mock_driver = Mock()
        self.controller = AppiumDeviceController()
        self.controller.driver = self.mock_driver
        self.controller.platform_name = 'android'
        self.controller.device_id = 'test_device'
        
    def test_press_enter_key_selenium_method(self):
        """Test _press_enter_key using Selenium Keys.RETURN"""
        mock_element = Mock()
        
        with patch('selenium.webdriver.common.keys.Keys') as mock_keys:
            mock_keys.RETURN = '\ue007'
            result = self.controller._press_enter_key(mock_element)
            
            self.assertTrue(result)
            mock_element.send_keys.assert_called_once_with('\ue007')
            
    def test_press_enter_key_adb_method(self):
        """Test _press_enter_key using ADB keyevent"""
        mock_element = Mock()
        mock_element.send_keys.side_effect = Exception("Selenium failed")
        
        with patch('subprocess.run') as mock_subprocess:
            result = self.controller._press_enter_key(mock_element)
            
            self.assertTrue(result)
            # Verify ADB command was called
            adb_calls = [call for call in mock_subprocess.call_args_list 
                        if 'KEYCODE_ENTER' in str(call)]
            self.assertTrue(len(adb_calls) > 0)

def run_enter_key_tests():
    """Run all Enter key functionality tests"""
    print("🧪 Running Enter Key Functionality Tests...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestEnterKeyFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestControllerEnterKeyMethods))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ All Enter key functionality tests passed!")
        return True
    else:
        print("❌ Some Enter key functionality tests failed!")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(f"  {failure[1]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(f"  {error[1]}")
        return False

if __name__ == "__main__":
    success = run_enter_key_tests()
    exit(0 if success else 1)
