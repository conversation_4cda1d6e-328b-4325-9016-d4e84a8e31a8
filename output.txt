2025-07-13 18:23:02,853 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-13 18:23:02,853 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-13 18:23:02,855 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-13 18:23:02,855 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-13 18:23:02,855 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-13 18:23:02,856 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-13 18:23:02,856 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-13 18:23:02,857 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-13 18:23:02,857 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-13 18:23:02,858 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-13 18:23:02,858 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-13 18:23:02,858 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-13 18:23:02,858 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-13 18:23:02,858 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-13 18:23:04,731 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-13 18:23:04,789 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-13 18:23:05,417 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-13 18:23:05,418 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-13 18:23:05,418 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-13 18:23:05,418 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-13 18:23:05,418 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-13 18:23:05,419 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-13 18:23:05,420 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-13 18:23:05,420 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-13 18:23:05,420 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-13 18:23:05,420 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-13 18:23:05,420 - utils.database - INFO - Database initialized successfully
2025-07-13 18:23:05,420 - utils.database - INFO - Checking initial database state...
2025-07-13 18:23:05,436 - utils.database - INFO - Database state: 0 suites, 0 cases, 2011 steps, 48 screenshots, 2011 tracking entries
2025-07-13 18:23:05,439 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-13 18:23:05,465 - app - INFO - Using directories from config.py:
2025-07-13 18:23:05,465 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-13 18:23:05,465 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-13 18:23:05,465 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-13 18:23:05,469] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-13 18:23:05,470] INFO in database: Test_steps table schema updated successfully
[2025-07-13 18:23:05,470] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-13 18:23:05,470] INFO in database: Screenshots table schema updated successfully
[2025-07-13 18:23:05,470] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-13 18:23:05,471] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-13 18:23:05,471] INFO in database: action_type column already exists in execution_tracking table
[2025-07-13 18:23:05,471] INFO in database: action_params column already exists in execution_tracking table
[2025-07-13 18:23:05,471] INFO in database: action_id column already exists in execution_tracking table
[2025-07-13 18:23:05,471] INFO in database: Successfully updated execution_tracking table schema
[2025-07-13 18:23:05,471] INFO in database: Database initialized successfully
[2025-07-13 18:23:05,471] INFO in database: Checking initial database state...
[2025-07-13 18:23:05,473] INFO in database: Database state: 0 suites, 0 cases, 2011 steps, 48 screenshots, 2011 tracking entries
[2025-07-13 18:23:05,473] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-13 18:23:05,474] INFO in database: Test_steps table schema updated successfully
[2025-07-13 18:23:05,474] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-13 18:23:05,474] INFO in database: Screenshots table schema updated successfully
[2025-07-13 18:23:05,474] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-13 18:23:05,475] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-13 18:23:05,475] INFO in database: action_type column already exists in execution_tracking table
[2025-07-13 18:23:05,475] INFO in database: action_params column already exists in execution_tracking table
[2025-07-13 18:23:05,475] INFO in database: action_id column already exists in execution_tracking table
[2025-07-13 18:23:05,475] INFO in database: Successfully updated execution_tracking table schema
[2025-07-13 18:23:05,475] INFO in database: Database initialized successfully
[2025-07-13 18:23:05,475] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-13 18:23:05,476] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-13 18:23:05,476] INFO in database: action_type column already exists in execution_tracking table
[2025-07-13 18:23:05,476] INFO in database: action_params column already exists in execution_tracking table
[2025-07-13 18:23:05,476] INFO in database: action_id column already exists in execution_tracking table
[2025-07-13 18:23:05,476] INFO in database: Successfully updated execution_tracking table schema
[2025-07-13 18:23:05,476] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-13 18:23:05,476] INFO in database: Screenshots table schema updated successfully
[2025-07-13 18:23:05,552] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-13 18:23:05,553] INFO in global_values_db: Global values database initialized successfully
[2025-07-13 18:23:05,553] INFO in global_values_db: Using global values from config.py
[2025-07-13 18:23:05,553] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-13 18:23:05,603] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-13 18:23:05,619] INFO in appium_device_controller: Appium server is running and ready
[2025-07-13 18:23:05,619] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-13 18:23:05,619] INFO in appium_device_controller: Connection monitoring started
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-13 18:23:05,644] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://*************:8081
[2025-07-13 18:23:05,644] INFO in _internal: [33mPress CTRL+C to quit[0m
