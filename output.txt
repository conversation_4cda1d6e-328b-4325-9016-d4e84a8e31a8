2025-07-13 19:46:57,640 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-13 19:46:57,641 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-13 19:46:57,642 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-13 19:46:57,643 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-13 19:46:57,643 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-13 19:46:57,644 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-13 19:46:57,644 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-13 19:46:57,645 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-13 19:46:57,645 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-13 19:46:57,646 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-13 19:46:57,646 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-13 19:46:57,647 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-13 19:46:57,647 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-13 19:46:57,647 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-13 19:46:59,194 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-13 19:46:59,236 - AppiumDeviceController - INFO - Successfully imported Airtest library.
